{"common": {"actions": {"actions": "Actions", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "update": "Update", "add": "Add", "create": "Create", "save": "Save", "search": "Search", "filter": "Filter", "verify": "Verify", "upload": "Upload", "save_as_draft": "Save as Draft", "next": "Next", "previous": "Previous", "publish": "Publish", "view": "View"}, "responses": {"yes": "Yes", "no": "No", "no_data": "No data available"}, "requirements": {"required": "Required", "optional": "Optional"}, "states": {"loading": "Loading...", "active": "Active", "inactive": "Inactive", "verified": "Verified", "pending": "Pending", "pending_verification": "Pending Verification"}, "status": {"error": "Error", "no_permission": "You do not have permission to access this resource."}, "fields": {"name": "Name", "description": "Description", "created_at": "Created At", "updated_at": "Updated At", "actions": "Actions", "id": "ID", "phone": "Phone", "email": "Email", "role": "Role", "status": "Status", "last_login": "Last Login", "address": "Address", "website": "Website", "founded_year": "Founded Year", "industry": "Industry", "contact": "Contact", "user": "User", "title": "Title", "company": "Company", "branch": "Branch", "location": "Location", "dateOfBirth": "Date of Birth", "gender": "Gender"}, "placeholders": {"please_input": "Please input", "please_select": "Please select an option", "please_enter_valid": "Please enter a valid", "search_placeholder": "Search", "select_salary_period": "Select salary period", "enter_text_here": "Enter text here...", "select_date": "Select date", "select_an_option": "Select an option"}, "messages": {"success": {"deleted_successfully": "deleted successfully", "updated_successfully": "updated successfully", "created_successfully": "created successfully", "verified_successfully": "verified successfully", "uploaded_successfully": "uploaded successfully", "post_updated": "Post updated successfully", "post_created": "Post created successfully"}, "error": {"failed_to_delete": "Failed to delete", "failed_to_update": "Failed to update", "failed_to_create": "Failed to create", "failed_to_verify": "Failed to verify", "upload_failed": "Upload failed", "failed_to_load_post_data": "Failed to load post data. Please try again.", "failed_to_save_post": "Failed to save post. Please check your inputs and try again."}, "confirm_delete": "Confirm Delete", "delete_confirmation_message": "Are you sure you want to delete this item?", "delete_confirmation_warning": "This action cannot be undone."}, "roles": {"employer": "Employer", "job_seeker": "<PERSON>"}, "file_types": {"image": "Image", "document": "Document", "logo": "Logo"}, "languages": {"english": "English", "vietnamese": "Tiếng <PERSON>"}, "version": "Version"}, "constants": {"job": {"types": {"part_time": "Part Time", "full_time": "Full Time"}, "work_types": {"on_site": "On-site", "remote": "Remote", "hybrid": "Hybrid"}, "experience_levels": {"no_requirement": "No Experience Required", "under_1_year": "Under 1 Year", "1_to_2_years": "1-2 Years", "2_to_5_years": "2-5 Years", "over_5_years": "Over 5 Years"}, "salary_periods": {"hourly": "Hourly", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "yearly": "Yearly"}, "salary_currencies": {"vnd": "VND", "usd": "USD", "eur": "EUR", "gbp": "GBP", "cny": "CNY"}}}, "form": {"required": "This field is required", "email": "Please enter a valid email address", "phone": "Please enter a valid phone number", "password": "Password must be at least 6 characters long", "confirm_password": "Passwords do not match", "default_error": "An error occurred. Please try again.", "url": "Please enter a valid URL", "year": "Please enter a valid year", "debounce_select": {"default_helper_text": "Type to search"}}, "route": {"home": "Home", "dashboard": "Dashboard", "account_management": "Account Management", "users": "Users", "permissions": "Permissions", "roles": "Roles", "job_seekers_management": "Job Seekers Management", "employers_management": "Employers Management", "employers_list": "List", "transactions_management": "Transactions Management", "posts_management": "Posts Management", "transactions": "Transactions", "companies": "Companies", "contracts": "Contracts", "interviews": "Interviews", "user_management": "User Management", "job_seekers": "Job Seekers", "employers": "Employers", "message_management": "Message Management", "notifications": "Notifications", "campaigns": "Campaigns", "message_templates": "Message Templates", "sent_notification": "Sent Notifications", "new": "New", "edit": "Edit", "posts_employer": "Posts", "posts_job_seeker": "Posts", "resumes": "Resumes", "job_seekers_list": "List", "profile": "Profile", "wallet_management": "Wallet Management", "wallet_job_seeker": "Job Seeker Wallets", "wallet_employer": "Employer Wallets"}, "user": {"user_list": "User List", "email_verified": "<PERSON><PERSON>", "phone_verified": "Phone Verified", "name_filter": "Name", "role_filter": "Role", "status_filter": "Status", "admin": "Admin", "user": "User", "employer": "Employer", "job_seeker": "<PERSON>", "user_type": "User Type", "admin_portal_access": "Admin Portal Access", "password_cannot_be_changed": "Password cannot be changed", "user_deleted_successfully": "User {name} deleted successfully", "user_updated_successfully": "User updated successfully", "user_created_successfully": "User created successfully", "failed_to_delete_user": "Failed to delete user: {error}", "add_user": "Add User", "edit_user": "Edit User", "please_input_user_name": "Please input user name!", "please_input_phone_number": "Please input phone number!", "please_input_email": "Please input email!", "please_enter_valid_email": "Please enter a valid email!", "please_select_user_role": "Please select user role!", "failed_to_update_user": "Failed to update user: {error}"}, "messages": {"success": {"post_updated": "Post updated successfully", "post_created": "Post created successfully"}, "errors": {"failed_to_load_post_data": "Failed to load post data. Please try again.", "failed_to_save_post": "Failed to save post. Please check your inputs and try again."}}, "permissions": {"title": "Permissions", "add_permission": "Add Permission", "edit_permission": "Edit Permission", "delete_permission": "Delete Permission", "search_placeholder": "Search Permission", "permission_updated_successfully": "Permission updated successfully", "permission_created_successfully": "Permission created successfully", "permission_deleted_successfully": "Permission {name} deleted successfully", "failed_to_delete_permission": "Failed to delete permission: {error}", "permission_name": "Permission Name", "enter_permission_name": "Enter permission name", "enter_permission_name_subtitle": "Format: action_module (e.g., create_user, view_post)", "permission_description_placeholder": "Describe what this permission allows"}, "posts": {"post_management": "Post Management", "create_new_post": "Create New Post", "search_posts_placeholder": "Search posts by title, company...", "title": "Title", "featured": "Featured", "urgent": "<PERSON><PERSON>", "company": "Company", "location": "Location", "job_type": "Job Type", "applications": "Applications", "new": "new", "post_date": "Post Date", "expire_date": "Expire Date", "work_type": "Work Type", "work_schedule": {"other_periods": "Working Days & Hours", "start": "Start", "to": "to", "end": "End", "apply_to_all": "Apply to all active days", "days": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "day_abbr": {"monday": "M", "tuesday": "T", "wednesday": "W", "thursday": "T", "friday": "F", "saturday": "S", "sunday": "S"}}, "post_date_range": "Post Date Range", "status_pending": "Pending", "status_active": "Active", "status_draft": "Draft", "status_closed": "Closed", "status_expired": "Expired", "work_type_onsite": "On-site", "work_type_remote": "Remote", "work_type_hybrid": "Hybrid", "job_type_part_time": "Part Time", "job_type_full_time": "Full Time", "basic_information": "Basic Information", "job_title": "Job Title", "job_description": "Job Description", "work_hours": "Work Hours", "work_days": "Work Days", "salary_min": "<PERSON><PERSON>", "salary_max": "<PERSON><PERSON>", "experience_level": "Experience Level", "positions": "Number of Positions", "tabs": {"basic_information": "Basic Information", "job_details": "Job Details", "skills": "Skills", "benefits": "Benefits", "required_documents": "Required Documents", "post_settings": "Post Settings"}, "featured_duration": "Featured Duration", "featured_duration_placeholder": "Select featured duration", "form": {"edit_post": "Edit Post", "create_new_post": "Create New Post"}, "status": {"draft": "Draft", "published": "Published"}, "documents": {"resume_cv": "Resume/CV"}, "actions": {"save_as_draft": "Save as Draft"}, "placeholders": {"job_title": "e.g. Senior Software Engineer", "company_name": "Company name", "job_location": "e.g. Ho <PERSON> Minh City", "job_description": "Detailed job description", "select_job_type": "Select job type", "select_work_type": "Select work type", "work_hours": "e.g. 9:00 AM - 6:00 PM", "work_days": "e.g. Monday - Friday", "salary": "e.g. $1000 - $1500 per month", "select_experience_level": "Select experience level", "positions": "Enter number of positions"}, "validation": {"please_enter_job_title": "Please enter job title", "please_enter_company_name": "Please enter company name", "please_enter_job_location": "Please enter job location", "please_enter_job_description": "Please enter job description", "at_least_one_day": "At least one work day must be selected", "set_time_ranges": "Please set time ranges for all active days"}, "benefits": {"title": "Benefits", "label": "Job Benefits", "placeholder": "Add a benefit (e.g. Health Insurance, Performance Bonus)", "add_benefit": "Add benefit", "remove_benefit": "Remove benefit", "no_benefits_added": "No benefits added yet. Start by adding your first benefit above."}, "skills": {"title": "Required Skills", "label": "Skills", "placeholder": "Add a skill (e.g. React, Project Management)", "add_skill": "Add skill", "remove_skill": "Remove skill", "no_skills_added": "No skills added yet. Start by adding your first skill above."}}, "roles": {"role_management": "Role Management", "add_role": "Add Role", "edit_role": "Edit Role", "delete_role": "Delete Role", "search_placeholder": "Search Role", "role_updated_successfully": "Role updated successfully", "role_update_failed": "Role update failed", "role_created_successfully": "Role created successfully", "role_creation_failed": "Role creation failed", "role_name": "Role Name", "enter_role_name": "Enter role name", "role_description_placeholder": "Describe what this role allows"}, "companies": {"company_management": "Company Management", "add_company": "Add Company", "edit_company": "Edit Company", "add_new_company": "Add New Company", "create_company": "Create Company", "update_company": "Update Company", "search_companies_placeholder": "Search companies...", "company_name": "Company Name", "company_description": "Company Description", "company_logo": "Company Logo", "company_type": "Company Type", "verification_status": "Verification Status", "contact_email": "Contact Email", "contact_phone": "Contact Phone", "website_url": "Website URL", "founded_year": "Founded Year", "teams_jobs": "Teams/Jobs", "team_members": "Team Members", "active_job_postings": "Active Job Postings", "individual_company": "Individual Company", "individual_company_help": "Individual companies represent freelancers or sole proprietors", "organization": "Organization", "individual": "Individual", "verified": "Verified", "not_verified": "Not Verified", "upload_logo": "Upload Logo", "company_created_successfully": "Company created successfully", "company_updated_successfully": "Company updated successfully", "company_deleted_successfully": "Company {name} has been deleted successfully", "company_verified_successfully": "Company {name} has been verified successfully", "failed_to_save_company": "Failed to save company", "failed_to_delete_company": "Failed to delete company", "failed_to_verify_company": "Failed to verify company", "verify_company_title": "Verify {name}?", "verify_company_message": "This will mark the company as verified. This action cannot be undone.", "enter_company_name": "Enter company name", "enter_company_address": "Enter company address", "enter_company_description": "Enter company description", "select_industry": "Select industry", "please_enter_company_name": "Please enter company name", "please_enter_company_address": "Please enter company address", "please_enter_company_description": "Please enter company description", "please_select_industry": "Please select an industry", "please_enter_valid_email": "Please enter a valid email", "please_enter_valid_url": "Please enter a valid URL", "industries": {"technology": "Technology", "healthcare": "Healthcare", "education": "Education", "finance": "Finance", "retail": "Retail", "manufacturing": "Manufacturing", "hospitality": "Hospitality", "other": "Other"}, "placeholders": {"company_name": "Enter company name", "contact_email": "<EMAIL>", "contact_phone": "+****************", "website_url": "https://www.company.com", "founded_year": "e.g. 2010", "company_address": "Enter company address", "company_description": "Enter company description"}}, "team_member": {"team_member_name": "Name", "team_member_management": "Team Member Management", "add_team_member": "Add Team Member", "edit_team_member": "Edit Team Member", "invite_member": "Invite Member", "update_member": "Update Member", "select_users": "Select Users", "search_and_select_users_placeholder": "Search and select users to invite as team members", "position": "Position", "branch": "Branch", "manager_role": "Manager Role", "manager_role_help": "Managers have additional permissions within the company", "default": "<PERSON><PERSON><PERSON>", "enter_email_placeholder": "Enter member's email address", "enter_position_placeholder": "Enter member's position/title", "select_branch_placeholder": "Select branch", "please_enter_email": "Please enter email address", "invitation_email_help": "An invitation will be sent to this email", "team_member_updated_successfully": "Team member updated successfully", "team_member_invited_successfully": "Team member invited successfully", "failed_to_save_team_member": "Failed to save team member", "failed_to_load_branches": "Failed to load branches"}, "table": {"filter_data": "Filter Data", "apply_filters": "Apply Filters", "clear_filters": "Clear", "enter_placeholder": "Enter {label}", "select_placeholder": "Select {label}", "confirm_delete": "Confirm Delete", "delete_confirmation_message": "Are you sure you want to delete this item?", "delete_confirmation_warning": "This action cannot be undone.", "deleted_successfully": "Deleted successfully", "delete_failed": "Failed to delete", "selected_items": "Selected {count} items"}, "address": {"province": "Province/City", "district": "District", "ward": "Ward", "detailed_address": "Detailed Address", "select_province": "Select province/city", "select_district": "Select district", "select_ward": "Select ward", "enter_detailed_address": "Enter house number, street name...", "search_province": "Search Province/City", "type_to_search_province": "Type to search province/city...", "full_address": "Full Address", "address_not_provided": "Address not provided", "loading_provinces": "Loading provinces/cities...", "loading_districts": "Loading districts...", "loading_wards": "Loading wards...", "error_loading_provinces": "Failed to load provinces/cities", "error_loading_districts": "Failed to load districts", "error_loading_wards": "Failed to load wards", "please_select_province_first": "Please select province/city first", "please_select_district_first": "Please select district first", "validation": {"province_required": "Please select province/city", "district_required": "Please select district", "ward_required": "Please select ward", "detail_address_required": "Please enter detailed address", "invalid_address": "Please enter a valid address"}, "copy": {"copy_address": "Copy address", "address_copied": "Address copied", "copy_failed": "Failed to copy address"}, "format": {"short_format": "Short format", "full_format": "Full format", "administrative_only": "Administrative units only"}}, "profile": {"title": "Profile: Detail Information", "edit": "Edit", "role": "Role", "role_admin": "Admin", "role_employer": "Employer", "role_employee": "Employee", "address": "Address", "gender": "Gender", "gender_male": "Male", "gender_female": "Female", "gender_other": "Other", "dob": "Date of Birth", "created_at": "Created At", "change_password": "Change password", "load_error": "Failed to load profile data", "save": "Save", "cancel": "Cancel", "old_password": "Old password", "new_password": "New password", "confirm_password": "Confirm new password", "password_required": "Password is required", "password_mismatch": "Passwords do not match", "password_format_error": "Password must be at least 8 characters long and contain at least one digit, one lowercase letter, one uppercase letter, and one special character", "edit_title": "Edit Profile", "save_success": "Profile updated successfully!", "update_success": "Profile updated successfully!", "update_error": "Failed to update profile!", "change_password_title": "Change Password", "change_success": "Password changed successfully!", "change_error": "Failed to change password!", "name": "Name", "phone_number": "Phone number", "na": "N/A"}, "wallet": {"wallet_management": "Wallet Management", "job_seeker_wallet_management": "Job Seeker Wallets", "employer_wallet_management": "Employer Wallets", "job_seeker_wallet_description": "Manage job seeker wallet points and transactions", "job_seeker_wallets": "Job Seeker Wallets", "employer_wallets": "Employer Wallets", "points": "Points", "points_unit": "pts", "search_placeholder": "Search users by name, email..."}, "transactions": {"transaction_history": "Transaction History", "transaction_details": "Transaction Details", "transaction_id": "Transaction ID", "user": "User", "user_id": "User ID", "date": "Date", "date_time": "Date & Time", "points": "Points", "amount": "Amount", "status": "Status", "payment_method": "Payment Method", "search_placeholder": "Search by ID or user name...", "export_to_csv": "Export to CSV", "payment_methods": {"credit_card": "Credit Card", "bank_transfer": "Bank Transfer", "e_wallet": "E-Wallet", "other": "Other"}, "statuses": {"completed": "Completed", "pending": "Pending", "failed": "Failed", "refunded": "Refunded"}}, "job_seeker_post": {"validation": {"please_enter_job_title": "Please enter job title", "please_enter_company_name": "Please enter company name", "please_enter_job_location": "Please enter job location", "please_enter_job_description": "Please enter job description", "at_least_one_day": "At least one work day must be selected", "set_time_ranges": "Please set time ranges for all active days"}, "create_new_post": "Create New Post", "edit_post": "Edit Post", "work_type": "Work Type", "work_type_onsite": "On-site", "work_type_remote": "Remote", "work_type_hybrid": "Hybrid", "job_type_part_time": "Part Time", "job_type_full_time": "Full Time", "job_type": "Job Type", "featured": "Featured", "post_date_range": "Post Date Range", "search_posts_placeholder": "Search posts by title, company...", "title": "Job Seeker Post", "status": "Status", "status_pending": "Pending", "status_active": "Active", "status_draft": "Draft", "status_closed": "Closed", "status_expired": "Expired", "work_schedule": "Work Schedule", "salary_min": "<PERSON><PERSON>", "salary_max": "<PERSON><PERSON>", "experience_level": "Experience Level", "positions": "Number of Positions", "location": "Location", "basic_information": "Basic Information", "job_details": "Job Details", "skills": "Skills", "skills_placeholder": "Add a skill (e.g. React, Project Management)", "benefits": "Benefits", "required_documents": "Required Documents", "post_settings": "Post Settings", "languages_placeholder": "Add a language (e.g. English)", "add_language": "Add Language", "level": "Level", "education": "Education", "education_level": "Education Level", "education_detail": "Education Detail", "experiences": "Experiences", "add_experience": "Add Experience", "year_of_experience": "Years of Experience", "working_hour_per_day": "Working Hours/Day", "start_time": "Start Time", "end_time": "End Time", "jobType": "Job Type", "contractType": "Contract Type", "workType": "Work Type", "salary_period_hourly": "Hourly", "salary_period_daily": "Daily", "salary_period_weekly": "Weekly", "salary_period_monthly": "Monthly", "salary_period_yearly": "Yearly", "detail_address": "Detail Address", "isEnableEmailNotification": "Enable Email Notification", "isEnableInformation": "Show Information", "isAutoAcceptInterviewInvitation": "Auto Accept Interview Invitation", "isReady": "Ready", "beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced", "fluent": "Fluent", "native": "Native", "add_skill": "<PERSON><PERSON>", "opportunity_count": "Opportunities", "view_count": "Views", "status_paused": "Paused", "status_reopen": "Reopen", "status_rejected": "Rejected", "job_seeker_post_detail": "Job Seeker Post Detail", "new_opportunity_count": "New Opportunities", "featureJob": "Featured Job", "feature_duration": "Featured Duration", "interview_request_count": "Interview Requests", "delete_post_confirm": "Delete this post?", "delete_post_confirm_desc": "Are you sure you want to delete this post? This action cannot be undone.", "industry": "Industry", "skills_and_languages": "Skills & Languages", "languages": "Languages", "salary": "Salary", "salary_currency": "<PERSON><PERSON><PERSON><PERSON>", "salary_period": "Period", "other_information": "Other Information", "active_date": "Active Date", "statistics": "Statistics", "working_days": "Working Days", "working_shifts": "Working Shifts", "post_not_found": "Post not found", "failed_to_load_post_data": "Failed to load post data. Please try again.", "activate_post_confirm": "Activate this post?", "activate_post_confirm_desc": "Are you sure you want to activate this post?", "activate": "Activate", "working_information": "Working Information", "features": "Features", "close_post": "Close Post", "edit_job_seeker_post": "Edit Job <PERSON> Post"}, "resume": {"active_resume": "Active Resume", "inactive": "Inactive", "no_description": "No description provided", "skills": "Skills", "no_skills": "No skills added", "contact_information": "Contact Information", "phone": "Phone", "email": "Email", "address": "Address", "linkedin": "LinkedIn", "portfolio": "Portfolio", "no_contact_info": "No contact information provided", "work_experience": "Work Experience", "no_work_experience": "No work experience added", "present": "Present", "education": "Education", "no_education": "No education history added", "major": "Major", "languages": "Languages", "no_languages": "No language skills added", "form": {"user": "User", "resume_name": "Resume Name", "resume_summary_description": "Resume Summary/Description", "skills": "Skills", "set_as_active_resume": "Set as active resume", "search_and_select_user": "Search and select a user...", "resume_name_placeholder": "e.g., Main Resume, Technical Resume, etc.", "resume_summary_placeholder": "Brief summary of your qualifications and career objectives", "skills_placeholder": "e.g., JavaScript, React, Node.js", "skills_help_text": "Enter your skills separated by commas. Example: JavaScript, React, Node.js", "active_resume_help_text": "Only one resume can be active at a time. The active resume is used for job applications.", "please_enter_at_least_one_skill": "Please enter at least one skill", "language_name": "Language Name", "language_level": "Language Level", "language_name_placeholder": "e.g., English, Vietnamese, Chinese", "select_language_level": "Select language level", "add_language": "Add Language"}, "part_time_preferences": "Part-time Preferences", "min_hourly_rate": "Min Hourly Rate", "max_hours_per_week": "Max Hours Per Week", "available_days": "Available Days", "available_time_slots": "Available Time Slots", "preferred_job_types": "Preferred Job Types", "remote_only": "Remote Only", "max_travel_distance": "Max Travel Distance", "student_status": "Student Status", "student": "Student", "not_student": "Not Student", "study_major": "Study Major", "additional_notes": "Additional Notes", "yes": "Yes", "no": "No"}, "notifications": {"form": {"notification_type": "Notification Type", "channel": "Channel", "target_audience": "Target Audience", "audience_type": "Audience Type", "select_users": "Select Users", "target_role": "Target Role", "message_content": "Message Content", "title": "Title", "content": "Content", "delivery_options": "Delivery Options", "send_time": "Send Time", "scheduled_time": "Scheduled Time", "cancel": "Cancel", "send_now": "Send Now", "schedule_notification": "Schedule Notification", "send_to_all_users": "Send to All Users", "send_to_all_users_desc": "This notification will be sent to all registered users on the platform.", "placeholders": {"select_notification_type": "Select notification type", "select_delivery_channel": "Select delivery channel", "select_audience_type": "Select audience type", "search_and_select_users": "Search and select users...", "select_user_role": "Select user role", "enter_notification_title": "Enter notification title", "enter_notification_content": "Enter notification content", "select_when_to_send": "Select when to send", "select_date_and_time": "Select date and time"}, "help_text": {"search_users": "Type at least 2 characters to search for users", "content_variables": "You can use variables like {{user_name}}, {{platform_name}}, etc."}, "send_options": {"now": "Send Now", "scheduled": "Schedule for Later"}, "audience_options": {"specific": "Specific Users", "all": "All Users", "role": "By Role"}, "role_options": {"employers": "Employers", "job_seekers": "Job Seekers"}, "templates": {"welcome": {"title": "Welcome to Flexi!", "content": "Welcome to Flexi! We're excited to have you join our job platform. Start exploring opportunities today."}, "verification": {"title": "Verify Your Account", "content": "Please verify your account by clicking the link we sent to your email address."}, "password_reset": {"title": "Password Reset Request", "content": "We received a request to reset your password. Click the link below to create a new password."}, "job_match": {"title": "New Job Matches Available", "content": "We found new job opportunities that match your profile. Check them out now!"}, "application_status": {"title": "Application Status Update", "content": "Your job application status has been updated. Please check your dashboard for details."}, "interview_invitation": {"title": "Interview Invitation", "content": "Congratulations! You have been invited for an interview. Please check the details and confirm your availability."}, "payment_confirmation": {"title": "Payment Confirmation", "content": "Your payment has been processed successfully. Thank you for your purchase."}, "point_purchase": {"title": "Points Purchase Confirmation", "content": "Your points have been added to your account successfully."}, "subscription_renewal": {"title": "Subscription Renewal", "content": "Your subscription has been renewed successfully. Thank you for continuing with us."}, "system_maintenance": {"title": "Scheduled Maintenance Notice", "content": "We will be performing scheduled maintenance. The platform may be temporarily unavailable."}, "policy_update": {"title": "Policy Update Notice", "content": "We have updated our terms and conditions. Please review the changes."}}}}}