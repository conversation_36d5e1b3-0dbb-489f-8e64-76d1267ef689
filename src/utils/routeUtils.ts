// utils/routeUtils.ts
import { routes } from "@/routes";
import { Routes, Route } from "@/types/routes";

/**
 * Finds the active route keys based on the current path
 * @param pathname Current path from usePathname()
 * @param collapsed Whether the sidebar is collapsed (affects open keys)
 * @returns Object containing selectedKeys and openKeys
 */
export const findActiveRouteKeys = (
  pathname: string,
  collapsed = false
): { selectedKeys: string[]; openKeys: string[] } => {
  const pathSegments = pathname.split("/").filter(Boolean);
  if (pathSegments.length === 0) {
    return { selectedKeys: ["dashboard"], openKeys: [] };
  }

  let selectedKey = "";
  let openKey = "";

  // First try to match exact paths or dynamic paths
  const findExactMatch = (
    routes: Routes,
    parentKeys: string[] = []
  ): boolean => {
    for (const [key, route] of Object.entries(routes)) {
      if ("path" in route) {
        // Check exact match
        if (route.path === pathname) {
          // Use route.key if it exists (for KeyedRoute), otherwise use object key
          selectedKey = "key" in route ? route.key : key;
          if (parentKeys.length > 0) {
            openKey = parentKeys.join(",");
          }
          return true;
        }

        // Check dynamic route match (path + ID)
        const routeSegments = route.path.split("/").filter(Boolean);
        if (
          routeSegments.length === pathSegments.length - 1 &&
          pathSegments.slice(0, -1).join("/") === routeSegments.join("/") &&
          /^\d+$/.test(pathSegments[pathSegments.length - 1])
        ) {
          // Use route.key if it exists (for KeyedRoute), otherwise use object key
          selectedKey = "key" in route ? route.key : key;
          if (parentKeys.length > 0) {
            openKey = parentKeys.join(",");
          }
          return true;
        }
      }

      if ("children" in route) {
        const newParentKeys = route.key
          ? [...parentKeys, route.key as string]
          : parentKeys;
        if (findExactMatch(route.children, newParentKeys)) {
          return true;
        }
      }
    }
    return false;
  };

  // If no exact match, try to match based on path hierarchy
  const findBestMatch = () => {
    let bestMatch = { key: "", parentKey: "", matchScore: 0 };

    const calculateMatch = (routes: Routes, parentKey = "") => {
      for (const [key, route] of Object.entries(routes)) {
        if ("path" in route) {
          const routeSegments = route.path.split("/").filter(Boolean);

          // Calculate how many segments match from the beginning
          let matchScore = 0;
          const minLength = Math.min(routeSegments.length, pathSegments.length);

          for (let i = 0; i < minLength; i++) {
            if (routeSegments[i] === pathSegments[i]) {
              matchScore++;
            } else {
              break; // Stop at first mismatch
            }
          }

          // Only consider it a valid match if we have at least one matching segment
          // and the route path is a prefix of the current path
          if (matchScore > 0 && matchScore === routeSegments.length) {
            // Check if this is a better match than what we have
            if (
              matchScore > bestMatch.matchScore ||
              (matchScore === bestMatch.matchScore &&
                routeSegments.length > bestMatch.matchScore)
            ) {
              bestMatch = {
                key: "key" in route ? route.key : key,
                parentKey,
                matchScore,
              };
            }
          }
        }

        // Recursively check children
        if ("children" in route) {
          calculateMatch(route.children, route.key as string);
        }
      }
    };

    calculateMatch(routes);

    if (bestMatch.key) {
      selectedKey = bestMatch.key;
      openKey = bestMatch.parentKey;
    } else {
      // Fallback: try to match just the first segment
      const baseSegment = pathSegments[0];

      for (const [key, route] of Object.entries(routes)) {
        if ("path" in route) {
          const routeBase = route.path.split("/").filter(Boolean)[0];
          if (routeBase === baseSegment) {
            selectedKey = "key" in route ? route.key : key;
            return;
          }
        }

        // Check children for first segment match (including nested children)
        if ("children" in route) {
          const checkChildren = (
            children: Routes,
            parentKeys: string[]
          ): boolean => {
            for (const [childKey, childRoute] of Object.entries(children)) {
              if ("path" in childRoute) {
                const childBase = childRoute.path.split("/").filter(Boolean)[0];
                if (childBase === baseSegment) {
                  selectedKey = "key" in childRoute ? childRoute.key : childKey;
                  openKey = parentKeys.join(",");
                  return true;
                }
              }

              // Check nested children
              if ("children" in childRoute) {
                const newParentKeys = childRoute.key
                  ? [...parentKeys, childRoute.key as string]
                  : parentKeys;
                if (checkChildren(childRoute.children, newParentKeys)) {
                  return true;
                }
              }
            }
            return false;
          };

          if (checkChildren(route.children, [route.key as string])) {
            return;
          }
        }
      }

      // Final fallback
      selectedKey = "dashboard";
    }
  };

  // Try exact match first (including dynamic routes)
  if (!findExactMatch(routes)) {
    // Fall back to best match
    findBestMatch();
  }

  // Convert single keys to arrays
  const selectedKeys = selectedKey ? [selectedKey] : [];
  const openKeys = collapsed ? [] : openKey ? openKey.split(",") : [];

  return { selectedKeys, openKeys };
};

/**
 * Finds parent key for a given route key
 * @param key The route key to find parent for
 * @returns The parent key or null if not found
 */
export const findParentKey = (key: string): string | null => {
  const findParent = (routes: Routes, parentKey?: string): string | null => {
    for (const [routeKey, route] of Object.entries(routes)) {
      // Check both object key and route.key for match
      const currentKey = "key" in route && route.key ? route.key : routeKey;
      if (currentKey === key) {
        return parentKey || null;
      }

      if ("children" in route) {
        const result = findParent(route.children, route.key as string);
        if (result !== null) {
          return result;
        }
      }
    }
    return null;
  };

  return findParent(routes);
};
