// constants/interview/constants.ts
import { InterviewStatus, InterviewType } from "@/types/interview";

/**
 * Interview status options with display names and colors
 */
export const INTERVIEW_STATUS_OPTIONS = [
  { value: InterviewStatus.PENDING, label: "Chờ xác nhận", color: "#faad14" },
  { value: InterviewStatus.SCHEDULED, label: "<PERSON><PERSON><PERSON> lịch", color: "#1890ff" },
  { value: InterviewStatus.CONFIRMED, label: "Đã xác nhận", color: "#52c41a" },
  {
    value: InterviewStatus.COMPLETED,
    label: "Đã hoàn thành",
    color: "#722ed1",
  },
  { value: InterviewStatus.CANCELLED, label: "Đã hủy", color: "#f5222d" },
  { value: InterviewStatus.RESCHEDULED, label: "Đổi lịch", color: "#fa8c16" },
  { value: InterviewStatus.NO_SHOW, label: "<PERSON>hông tham gia", color: "#8c8c8c" },
];

/**
 * Interview type options with display names and icons
 */
export const INTERVIEW_TYPE_OPTIONS = [
  {
    value: InterviewType.VIDEO_CALL,
    label: "Cuộc gọi video",
    icon: "video-camera",
  },
  { value: InterviewType.PHONE_CALL, label: "Cuộc gọi thoại", icon: "phone" },
  {
    value: InterviewType.IN_PERSON,
    label: "Phỏng vấn trực tiếp",
    icon: "environment",
  },
];

/**
 * Interview duration options in minutes
 */
export const INTERVIEW_DURATION_OPTIONS = [
  { value: 15, label: "15 phút" },
  { value: 30, label: "30 phút" },
  { value: 45, label: "45 phút" },
  { value: 60, label: "60 phút" },
  { value: 90, label: "90 phút" },
  { value: 120, label: "120 phút" },
];

/**
 * Rejection reason options for feedback
 */
export const REJECTION_REASON_OPTIONS = [
  "Thiếu kỹ năng phù hợp",
  "Thiếu kinh nghiệm",
  "Không phù hợp với văn hóa công ty",
  "Kỳ vọng lương quá cao",
  "Lịch làm việc không phù hợp",
  "Kỹ năng giao tiếp kém",
  "Khác",
];

/**
 * Notification methods
 */
export const NOTIFICATION_METHOD_OPTIONS = [
  { value: "email", label: "Email" },
  { value: "sms", label: "SMS" },
  { value: "app", label: "In-App Notification" },
];

/**
 * Notification recipient options
 */
export const NOTIFICATION_RECIPIENT_OPTIONS = [
  { value: "candidate", label: "Ứng viên" },
  { value: "employer", label: "Nhà tuyển dụng" },
  { value: "both", label: "Cả hai" },
];

/**
 * Notification template options
 */
export const NOTIFICATION_TEMPLATE_OPTIONS = [
  { value: "reminder", label: "Nhắc nhở phỏng vấn" },
  { value: "reschedule", label: "Đổi lịch phỏng vấn" },
  { value: "cancellation", label: "Hủy phỏng vấn" },
  { value: "confirmation", label: "Xác nhận phỏng vấn" },
  { value: "feedback", label: "Gửi đánh giá phỏng vấn" },
  { value: "custom", label: "Tin nhắn tùy chỉnh" },
];

/**
 * Interview preparation tips for candidates
 */
export const CANDIDATE_PREPARATION_TIPS = [
  "Xem lại mô tả công việc và yêu cầu",
  "Chuẩn bị câu trả lời cho câu hỏi phỏng vấn phổ biến",
  "Nghiên cứu về công ty và văn hóa",
  "Chuẩn bị câu hỏi để hỏi nhà tuyển dụng",
  "Kiểm tra thiết bị và kết nối internet trước khi phỏng vấn",
];

/**
 * Interview preparation tips for employers
 */
export const EMPLOYER_PREPARATION_TIPS = [
  "Xem lại hồ sơ và CV của ứng viên",
  "Chuẩn bị câu hỏi phù hợp với vị trí và kinh nghiệm",
  "Lập kế hoạch đánh giá kỹ năng và năng lực",
  "Đảm bảo môi trường phỏng vấn chuyên nghiệp",
  "Kiểm tra thiết bị và kết nối internet trước khi phỏng vấn",
];
