export enum UserRole {
  ADMIN = "admin",
  EMPLOYER = "employer",
  JOB_SEEKER = "jobSeeker",
}

// Helper function to get display label for role
export const getRoleLabel = (role: UserRole | string): string => {
  switch (role) {
    case UserRole.ADMIN:
      return "Admin";
    case UserRole.EMPLOYER:
      return "Employer";
    case UserRole.JOB_SEEKER:
      return "Job Seeker";
    default:
      return role;
  }
};

// Helper function to get role color for tags
export const getRoleColor = (role: UserRole | string): string => {
  switch (role) {
    case UserRole.ADMIN:
      return "red";
    case UserRole.EMPLOYER:
      return "blue";
    case UserRole.JOB_SEEKER:
      return "green";
    default:
      return "default";
  }
};
