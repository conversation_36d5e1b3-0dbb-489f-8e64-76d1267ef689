import { NotificationChannel, NotificationStatus } from "@/types/notifications";

export const NOTIFICATION_TYPES = [
  { value: "welcome", label: "Welcome Message" },
  { value: "verification", label: "Account Verification" },
  { value: "password_reset", label: "Password Reset" },
  { value: "job_match", label: "Job Match Alert" },
  { value: "application_status", label: "Application Status Update" },
  { value: "interview_invitation", label: "Interview Invitation" },
  { value: "payment_confirmation", label: "Payment Confirmation" },
  { value: "point_purchase", label: "Point Purchase" },
  { value: "subscription_renewal", label: "Subscription Renewal" },
  { value: "system_maintenance", label: "System Maintenance" },
  { value: "policy_update", label: "Policy Update" },
];

export const NOTIFICATION_CHANNELS = [
  { value: "email", label: "Email" },
  { value: "sms", label: "SMS" },
  { value: "push", label: "Push Notification" },
  { value: "in_app", label: "In-App Notification" },
];

export const NOTIFICATION_STATUSES = [
  { value: "pending", label: "Pending" },
  { value: "sent", label: "Sent" },
  { value: "delivered", label: "Delivered" },
  { value: "read", label: "Read" },
  { value: "clicked", label: "Clicked" },
  { value: "failed", label: "Failed" },
  { value: "cancelled", label: "Cancelled" },
];

export const getStatusColor = (status: NotificationStatus): string => {
  const colors = {
    pending: "orange",
    sent: "blue",
    delivered: "cyan",
    read: "green",
    clicked: "purple",
    failed: "red",
    cancelled: "default",
  };
  return colors[status] || "default";
};

export const getChannelIcon = (channel: NotificationChannel): string => {
  const icons = {
    email: "📧",
    sms: "📱",
    push: "🔔",
    in_app: "💬",
  };
  return icons[channel] || "📬";
};
