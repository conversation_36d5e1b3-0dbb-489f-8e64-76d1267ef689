// hooks/useFetchData.ts
"use client";

import { useState, useEffect, useRef, useCallback } from "react";

interface PaginationParams {
  page?: number;
  pageSize?: number;
  [key: string]: number | string | undefined;
}

interface FetchDataResult<T> {
  data: T[];
  total: number;
  loading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

export function useFetchData<T>(
  api: (params?: Record<string, unknown>) => Promise<unknown>,
  initialParams: PaginationParams = {}
): FetchDataResult<T> {
  const [data, setData] = useState<T[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const paramsRef = useRef(initialParams);

  const requestInProgressRef = useRef(false);

  useEffect(() => {
    paramsRef.current = initialParams;
  }, [initialParams]);

  const fetchData = useCallback(async () => {
    if (requestInProgressRef.current) {
      return;
    }

    requestInProgressRef.current = true;
    setLoading(true);
    setError(null);

    try {
      const response = await api(paramsRef.current);

      const typedResponse = response as {
        data: T[];
        paginationMetadata: { totalItems: number };
      };

      setData(typedResponse.data);
      setTotal(typedResponse.paginationMetadata.totalItems);
    } catch (err) {
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
      requestInProgressRef.current = false;
    }
  }, [api]);

  const prevParamsRef = useRef<{
    page?: number;
    pageSize?: number;
  }>({});

  useEffect(() => {
    if (
      prevParamsRef.current.page !== initialParams.page ||
      prevParamsRef.current.pageSize !== initialParams.pageSize
    ) {
      prevParamsRef.current = {
        page: initialParams.page,
        pageSize: initialParams.pageSize,
      };
      fetchData();
    }
  }, [initialParams.page, initialParams.pageSize, fetchData]);

  return {
    data,
    total,
    loading,
    error,
    refetch: fetchData,
  };
}
