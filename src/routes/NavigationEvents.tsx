"use client";
import { useEffect } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import NProgress from "nprogress";
import { Suspense } from "react";

// This component handles the actual navigation events
function NavigationEventsContent() {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Handle link clicks and form submissions
  useEffect(() => {
    const handleLinkClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      const link = target.closest("a");

      if (
        link &&
        link.href &&
        link.href.startsWith(window.location.origin) &&
        !link.target
      ) {
        NProgress.start();
      }
    };

    const handleFormSubmit = () => {
      NProgress.start();
    };

    const handleBeforeUnload = () => {
      NProgress.start();
    };

    document.addEventListener("click", handleLinkClick);
    document.addEventListener("submit", handleFormSubmit);
    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      document.removeEventListener("click", handleLinkClick);
      document.removeEventListener("submit", handleFormSubmit);
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, []);

  // Complete loading when route change is done
  useEffect(() => {
    NProgress.done();
  }, [pathname, searchParams]);

  return null;
}

// The main component that wraps NavigationEventsContent in a Suspense boundary
export function NavigationEvents() {
  return (
    <Suspense fallback={null}>
      <NavigationEventsContent />
    </Suspense>
  );
}
