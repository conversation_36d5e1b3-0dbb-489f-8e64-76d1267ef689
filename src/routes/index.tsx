import { Routes } from "@/types/routes";
import {
  AppstoreOutlined,
  BankOutlined,
  DashboardOutlined,
  FileProtectOutlined,
  FileTextOutlined,
  FundOutlined,
  IdcardOutlined,
  LockOutlined,
  MessageOutlined,
  NotificationOutlined,
  Pi<PERSON><PERSON><PERSON>tOutlined,
  TeamOutlined,
  TransactionOutlined,
  UserOutlined,
  VideoCameraOutlined,
  WalletOutlined,
} from "@ant-design/icons";

export const routes: Routes = {
  dashboard: {
    path: "/",
    icon: <DashboardOutlined />,
    label: "dashboard",
  },
  accountManagement: {
    key: "account-management",
    icon: <TeamOutlined />,
    label: "account_management",
    access: "viewAccountManagement",
    children: {
      users: {
        path: "/account-management/users",
        icon: <UserOutlined />,
        label: "users",
      },
      permissions: {
        path: "/account-management/permissions",
        icon: <LockOutlined />,
        label: "permissions",
      },
      roles: {
        path: "/account-management/roles",
        icon: <IdcardOutlined />,
        label: "roles",
      },
    },
  },
  jobSeekersManagement: {
    key: "job-seekers-management",
    icon: <UserOutlined />,
    label: "job_seekers_management",
    children: {
      list: {
        key: "job-seekers-list",
        path: "/job-seekers-management/list",
        icon: <UserOutlined />,
        label: "job_seekers_list",
        access: "viewUser",
      },
      posts: {
        key: "job-seeker-posts",
        path: "/job-seekers-management/posts",
        icon: <FileTextOutlined />,
        label: "posts_job_seeker",
        access: "viewJobSeekerPost",
      },
      resumes: {
        path: "/job-seekers-management/resumes",
        icon: <FileTextOutlined />,
        label: "resumes",
        access: "viewUser",
      },
      wallet: {
        key: "job-seeker-wallet",
        path: "/job-seekers-management/wallet",
        icon: <WalletOutlined />,
        label: "wallet_job_seeker",
        access: "viewWallet",
      },
      contracts: {
        key: "job-seeker-contracts",
        path: "/job-seekers-management/contracts",
        icon: <FileProtectOutlined />,
        label: "contracts",
      },
      interviews: {
        key: "job-seeker-interviews",
        path: "/job-seekers-management/interviews",
        icon: <VideoCameraOutlined />,
        label: "interviews",
        access: "viewInterview",
      },
    },
  },
  employersManagement: {
    key: "employers-management",
    icon: <BankOutlined />,
    label: "employers_management",
    children: {
      list: {
        key: "employers-list",
        path: "/employers-management/list",
        icon: <UserOutlined />,
        label: "employers_list",
        access: "viewUser",
      },
      companies: {
        key: "employers-companies",
        path: "/employers-management/companies",
        icon: <BankOutlined />,
        label: "companies",
        access: "viewCompany",
      },
      posts: {
        key: "employers-posts",
        path: "/employers-management/posts",
        icon: <FileTextOutlined />,
        label: "posts_employer",
        access: "viewPost",
      },
      wallet: {
        key: "employers-wallet",
        path: "/employers-management/wallet",
        icon: <WalletOutlined />,
        label: "wallet_employer",
        access: "viewWallet",
      },
      contracts: {
        key: "employer-contracts",
        path: "/employers-management/contracts",
        icon: <FileProtectOutlined />,
        label: "contracts",
      },
      interviews: {
        key: "employer-interviews",
        path: "/employers-management/interviews",
        icon: <VideoCameraOutlined />,
        label: "interviews",
        access: "viewInterview",
      },
    },
  },
  transactionsManagement: {
    path: "/transactions-management",
    icon: <TransactionOutlined />,
    label: "transactions_management",
  },
  messageManagement: {
    key: "message-management",
    icon: <MessageOutlined />,
    label: "message_management",
    children: {
      notifications: {
        path: "/message-management/notifications",
        icon: <NotificationOutlined />,
        label: "notifications",
      },
      campaigns: {
        path: "/message-management/campaigns",
        icon: <FundOutlined />,
        label: "campaigns",
      },
      message_templates: {
        path: "/message-management/templates",
        icon: <PicLeftOutlined />,
        label: "message_templates",
      },
    },
  },
};
