import ApiService from "@/services/ApiService";
import { ApiDetailResponse, ApiListResponse } from "@/types/common";
import {
  Company,
  CompanyCreateInput,
  CompanyUpdateInput,
} from "@/types/company";

interface ICompanyService {
  getList: (
    params?: Record<string, unknown>
  ) => Promise<ApiListResponse<Company>>;
  getDetail: (id: string) => Promise<ApiDetailResponse<Company>>;
  create: (data: CompanyCreateInput) => Promise<ApiDetailResponse<Company>>;
  update: (
    id: string,
    data: CompanyUpdateInput
  ) => Promise<ApiDetailResponse<Company>>;
  delete: (id: string) => Promise<unknown>;
  verifyCompany: (id: string) => Promise<ApiDetailResponse<Company>>;
}

const basePath = "v1/admin/companies";

const CompanyService: ICompanyService = {
  getList: async (params?: Record<string, unknown>) => {
    return await ApiService.post(`${basePath}/search`, params);
  },

  getDetail: async (id: string) => {
    return await ApiService.get(`${basePath}/${id}`);
  },

  create: async (data) => {
    return await ApiService.post(`${basePath}`, data);
  },
  update: async (id, data) => {
    return await ApiService.put(`${basePath}/${id}`, data);
  },
  delete: async (id) => {
    return await ApiService.delete(`${basePath}/${id}`);
  },

  verifyCompany: async (id: string) => {
    return await ApiService.patch(`${basePath}/${id}/verify`);
  },
};

export default CompanyService;
