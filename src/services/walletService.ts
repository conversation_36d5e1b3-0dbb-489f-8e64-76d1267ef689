import ApiService from "./ApiService";
import {
  WalletListResponse,
  AdminWalletResponse,
  AdminWalletFilter,
  PersonalTransactionResponse,
  PersonalTransactionFilter,
  WalletStatistics,
} from "@/types/wallet";
import { PaginationResponse } from "@/types/common";
import { UserRole } from "@/constants/userRole";

/**
 * Search wallets with admin filter
 */
export const searchWallets = async (
  filter: AdminWalletFilter = {}
): Promise<{ data: AdminWalletResponse[]; pagination: PaginationResponse }> => {
  const response = await ApiService.post<
    { data: AdminWalletResponse[]; pagination: PaginationResponse },
    AdminWalletFilter
  >("/v1/admin/wallets/search", filter);

  return response as any;
};

/**
 * Get wallet detail by ID
 */
export const getWalletDetail = async (
  id: number
): Promise<AdminWalletResponse> => {
  const response = await ApiService.get<AdminWalletResponse>(
    `/v1/admin/wallets/${id}`
  );
  return response.data;
};

/**
 * Get wallet by user ID
 */
export const getWalletByUserId = async (
  userId: number
): Promise<AdminWalletResponse> => {
  const response = await ApiService.get<AdminWalletResponse>(
    `/v1/admin/wallets/user/${userId}`
  );
  return response.data;
};

/**
 * Get wallet transactions
 */
export const getWalletTransactions = async (
  walletId: number,
  filter: PersonalTransactionFilter = {}
): Promise<{
  data: PersonalTransactionResponse[];
  pagination: PaginationResponse;
}> => {
  const response = await ApiService.post<
    { data: PersonalTransactionResponse[]; pagination: PaginationResponse },
    PersonalTransactionFilter
  >(`/v1/admin/wallets/${walletId}/transactions`, filter);

  return response.data;
};

/**
 * Adjust wallet points
 */
export const adjustWalletPoints = async (
  walletId: number,
  points: number,
  reason: string
): Promise<AdminWalletResponse> => {
  const response = await ApiService.patch<
    AdminWalletResponse,
    Record<string, unknown>
  >(
    `/v1/admin/wallets/${walletId}/adjust?points=${points}&reason=${encodeURIComponent(
      reason
    )}`
  );
  return response.data;
};

/**
 * Get wallet statistics summary
 */
export const getWalletStatistics = async (): Promise<WalletStatistics> => {
  const response = await ApiService.get<WalletStatistics>(
    "/v1/admin/wallets/statistics"
  );
  return response.data;
};

/**
 * Helper function to transform AdminWalletResponse to legacy Wallet format
 */
const transformToLegacyFormat = (
  wallets: AdminWalletResponse[]
): WalletListResponse => {
  const walletData = wallets.map((wallet) => ({
    id: wallet.id,
    user: {
      id: wallet.user.id,
      ulid: `wallet-${wallet.id}`,
      name: wallet.user.name,
      email: wallet.user.email || "",
      phoneNumber: wallet.user.phone || "",
      profilePicture: wallet.user.photoUrl || "",
      address: {
        id: 0,
        detailAddress: "",
        ward: {
          code: 0,
          name: "",
          codename: "",
          divisionType: "",
          districtCode: 0,
        },
        district: {
          code: 0,
          name: "",
          codename: "",
          divisionType: "",
          provinceCode: 0,
          wards: [],
        },
        province: {
          code: 0,
          name: "",
          codename: "",
          divisionType: "",
          phoneCode: 0,
          districts: [],
        },
      },
    },
    point: wallet.point,
  }));

  return {
    data: walletData,
    message: "Success",
    statusCode: 200,
    success: true,
  };
};

/**
 * Legacy method for job seeker wallets - for backward compatibility
 */
export const getJobSeekerWallets = async (
  params?: Record<string, unknown>
): Promise<WalletListResponse> => {
  const filter: AdminWalletFilter = {
    userName: params?.search as string,
    userEmail: params?.userEmail as string,
    userRole: UserRole.JOB_SEEKER, // Filter for job seekers only
    minPoints: params?.minPoints as number,
    maxPoints: params?.maxPoints as number,
    createdFrom: params?.createdFrom as string,
    createdTo: params?.createdTo as string,
    page: (params?.page as number) || 0,
    limit: (params?.limit as number) || 20,
    sortBy: params?.sortBy as string,
    sortOrder: params?.sortOrder as string,
  };

  const result = await searchWallets(filter);
  // Transform to legacy format for compatibility
  const walletData = result.data.map((wallet) => ({
    id: wallet.id,
    user: {
      id: wallet.user.id,
      ulid: `wallet-${wallet.id}`,
      name: wallet.user.name,
      email: wallet.user.email || "",
      phoneNumber: wallet.user.phone || "",
      profilePicture: wallet.user.photoUrl || "",
      address: {
        id: 0,
        detailAddress: "",
        ward: {
          code: 0,
          name: "",
          codename: "",
          divisionType: "",
          districtCode: 0,
        },
        district: {
          code: 0,
          name: "",
          codename: "",
          divisionType: "",
          provinceCode: 0,
          wards: [],
        },
        province: {
          code: 0,
          name: "",
          codename: "",
          divisionType: "",
          phoneCode: 0,
          districts: [],
        },
      },
    },
    point: wallet.point,
  }));
  return {
    data: walletData,
    message: "Success",
    statusCode: 200,
    success: true,
  };
};

/**
 * Legacy method for employer wallets - for backward compatibility
 */
export const getEmployerWallets = async (
  params?: Record<string, unknown>
): Promise<WalletListResponse> => {
  const filter: AdminWalletFilter = {
    userName: params?.search as string,
    userEmail: params?.userEmail as string,
    userRole: UserRole.EMPLOYER, // Filter for employers only
    minPoints: params?.minPoints as number,
    maxPoints: params?.maxPoints as number,
    createdFrom: params?.createdFrom as string,
    createdTo: params?.createdTo as string,
    page: (params?.page as number) || 0,
    limit: (params?.limit as number) || 20,
    sortBy: params?.sortBy as string,
    sortOrder: params?.sortOrder as string,
  };

  const result = await searchWallets(filter);
  return transformToLegacyFormat(result.data);
};

// Default export for backward compatibility
const WalletService = {
  searchWallets,
  getWalletDetail,
  getWalletByUserId,
  getWalletTransactions,
  adjustWalletPoints,
  getWalletStatistics,
  getJobSeekerWallets,
  getEmployerWallets,
};

export default WalletService;
