// services/TransactionService.ts
import {
  mockPointPackages,
  mockTransactions,
  mockTransactionStats,
  mockTransactionTrends,
} from "@/mocks/transactions";
import ApiService from "@/services/ApiService";
import {
  Transaction,
  TransactionStats,
  PointPackage,
  TransactionTrend,
} from "@/types/transaction";
import dayjs from "dayjs";

const TransactionService = {
  async getTransactions(
    params?: Record<string, unknown>
  ): Promise<{ data: Transaction[]; total: number }> {
    console.log("🚀 ~ params:", params);
    // const response = await ApiService.get<{
    //   data: Transaction[];
    //   total: number;
    // }>("/transactions", params);
    return { data: mockTransactions, total: mockTransactions.length };
  },

  async getTransactionStats(): Promise<TransactionStats> {
    // const response = await ApiService.get<TransactionStats>(
    //   "/transactions/stats"
    // );
    return mockTransactionStats;
  },

  async getTransactionTrends(
    period: "day" | "week" | "month" | "year" = "month"
  ): Promise<TransactionTrend[]> {
    console.log("🚀 ~ period:", period);
    // const response = await ApiService.get<TransactionTrend[]>(
    //   "/transactions/trends",
    //   { period }
    // );
    return mockTransactionTrends;
  },

  async exportTransactions(params?: Record<string, unknown>): Promise<Blob> {
    const response = await ApiService.get<Blob>(
      "/transactions/export",
      params,
      {
        responseType: "blob",
      }
    );
    return response.data;
  },

  async getPointPackages() {
    // const response = await ApiService.get<PointPackage[]>("/point-packages");
    return { data: mockPointPackages, total: mockPointPackages.length };
  },

  createPointPackage: async (data: Omit<PointPackage, "id" | "createdAt">) => {
    return {
      ...data,
      id: `pkg${Math.floor(1000 + Math.random() * 9000)}`,
      createdAt: dayjs().toISOString(),
    };
  },

  updatePointPackage: async (id: string, data: Partial<PointPackage>) => {
    const pkg = mockPointPackages.find((p) => p.id === id);
    return { ...pkg, ...data } as PointPackage;
  },

  async deletePointPackage(id: string): Promise<void> {
    await ApiService.delete(`/point-packages/${id}`);
  },
};

export default TransactionService;
