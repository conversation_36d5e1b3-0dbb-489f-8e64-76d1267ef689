// services/notificationService.ts
import ApiService from "@/services/ApiService";
import { ApiDetailResponse, ApiListResponse } from "@/types/common";
import {
  CreateNotificationRequest,
  Notification,
  NotificationStats,
} from "@/types/notifications";
import {
  mockNotifications,
  mockNotificationStats,
  mockAnalyticsData,
  mockNotificationListResponse,
  generateMockNotification,
} from "@/mocks/notifications";

interface INotificationService {
  getList: (
    params?: Record<string, unknown>
  ) => Promise<ApiListResponse<Notification>>;
  getStats: (
    filters?: Record<string, unknown>
  ) => Promise<ApiDetailResponse<NotificationStats>>;
  getById: (id: string) => Promise<ApiDetailResponse<Notification>>;
  create: (
    data: CreateNotificationRequest
  ) => Promise<ApiDetailResponse<Notification>>;
  sendBulk: (
    data: CreateNotificationRequest
  ) => Promise<ApiDetailResponse<{ count: number }>>;
  cancel: (id: string) => Promise<unknown>;
  resend: (id: string) => Promise<ApiDetailResponse<Notification>>;
  markAsRead: (id: string) => Promise<unknown>;
  delete: (id: string) => Promise<unknown>;
  getAnalytics: (
    period?: "day" | "week" | "month" | "year"
  ) => Promise<unknown>;
  getDeliveryReport: (filters?: Record<string, unknown>) => Promise<unknown>;
  export: (
    filters?: Record<string, unknown>
  ) => Promise<ApiDetailResponse<Blob>>;
}

const basePath = "v1/admin/notifications";

// Helper function to simulate API delay
const simulateDelay = (ms: number = 500) =>
  new Promise((resolve) => setTimeout(resolve, ms));

// Helper function to filter mock data based on params
const filterMockNotifications = (
  params?: Record<string, unknown>
): Notification[] => {
  let filteredData = [...mockNotifications];

  if (params?.search) {
    const searchTerm = String(params.search).toLowerCase();
    filteredData = filteredData.filter(
      (notification) =>
        notification.title.toLowerCase().includes(searchTerm) ||
        notification.user_name.toLowerCase().includes(searchTerm) ||
        notification.user_email.toLowerCase().includes(searchTerm) ||
        notification.content.toLowerCase().includes(searchTerm)
    );
  }

  if (params?.type) {
    filteredData = filteredData.filter(
      (notification) => notification.type === params.type
    );
  }

  if (params?.channel) {
    filteredData = filteredData.filter(
      (notification) => notification.channel === params.channel
    );
  }

  if (params?.status) {
    filteredData = filteredData.filter(
      (notification) => notification.status === params.status
    );
  }

  if (params?.user_id) {
    filteredData = filteredData.filter(
      (notification) => notification.user_id === params.user_id
    );
  }

  return filteredData;
};

const NotificationService: INotificationService = {
  getList: async (params) => {
    // return await ApiService.get(`${basePath}`, params);

    // Mock implementation
    await simulateDelay();

    const filteredData = filterMockNotifications(params);
    const page = Number(params?.page) || 1;
    const pageSize = Number(params?.pageSize) || 10;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedData = filteredData.slice(startIndex, endIndex);

    const totalItems = filteredData.length;
    const totalPages = Math.ceil(totalItems / pageSize);
    const firstItemIndex = startIndex + 1;
    const lastItemIndex = Math.min(endIndex, totalItems);

    return {
      data: paginatedData,
      paginationMetadata: {
        totalItems: totalItems,
        currentPage: page,
        totalPages: totalPages,
        itemsPerPage: pageSize,
        firstItemIndex: firstItemIndex,
        lastItemIndex: lastItemIndex,
        hasMorePages: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  },

  getStats: async (filters) => {
    // return await ApiService.get(`${basePath}/stats`, filters);

    // Mock implementation
    await simulateDelay();

    return {
      data: mockNotificationStats,
      message: "Notification statistics retrieved successfully",
    };
  },

  getById: async (id: string) => {
    // return await ApiService.get(`${basePath}/${id}`);

    // Mock implementation
    await simulateDelay();

    const notification = mockNotifications.find((n) => n.id === id);
    if (!notification) {
      throw new Error("Notification not found");
    }

    return {
      data: notification,
      message: "Notification retrieved successfully",
    };
  },

  create: async (data: CreateNotificationRequest) => {
    // return await ApiService.post(`${basePath}`, data);

    // Mock implementation
    await simulateDelay();

    const newNotification = generateMockNotification({
      type: data.type,
      channel: data.channel,
      title: data.title,
      content: data.content,
      status: data.scheduled_at ? "pending" : "sent",
      sent_at: data.scheduled_at || new Date().toISOString(),
    });

    // Add to mock data (in real app, this would be persisted)
    mockNotifications.unshift(newNotification);

    return {
      data: newNotification,
      message: "Notification created successfully",
    };
  },

  sendBulk: async (data: CreateNotificationRequest) => {
    // return await ApiService.post(`${basePath}/bulk`, data);

    // Mock implementation
    await simulateDelay(1000); // Longer delay for bulk operation

    const count = data.user_ids.length;

    // Generate notifications for each user (in real app, this would be handled by backend)
    data.user_ids.forEach((userId, index) => {
      const notification = generateMockNotification({
        user_id: userId,
        user_name: `User ${index + 1}`,
        user_email: `user${index + 1}@example.com`,
        type: data.type,
        channel: data.channel,
        title: data.title,
        content: data.content,
        status: data.scheduled_at ? "pending" : "sent",
        sent_at: data.scheduled_at || new Date().toISOString(),
      });
      mockNotifications.unshift(notification);
    });

    return {
      data: { count },
      message: `Bulk notification sent to ${count} users successfully`,
    };
  },

  cancel: async (id: string) => {
    // return await ApiService.patch(`${basePath}/${id}/cancel`);

    // Mock implementation
    await simulateDelay();

    const notificationIndex = mockNotifications.findIndex((n) => n.id === id);
    if (notificationIndex === -1) {
      throw new Error("Notification not found");
    }

    mockNotifications[notificationIndex].status = "cancelled";
    mockNotifications[notificationIndex].updated_at = new Date().toISOString();

    return { message: "Notification cancelled successfully" };
  },

  resend: async (id: string) => {
    // return await ApiService.post(`${basePath}/${id}/resend`);

    // Mock implementation
    await simulateDelay();

    const notificationIndex = mockNotifications.findIndex((n) => n.id === id);
    if (notificationIndex === -1) {
      throw new Error("Notification not found");
    }

    // Update status and timestamp
    mockNotifications[notificationIndex].status = "sent";
    mockNotifications[notificationIndex].sent_at = new Date().toISOString();
    mockNotifications[notificationIndex].updated_at = new Date().toISOString();
    mockNotifications[notificationIndex].failed_reason = undefined;

    return {
      data: mockNotifications[notificationIndex],
      message: "Notification resent successfully",
    };
  },

  markAsRead: async (id: string) => {
    // return await ApiService.patch(`${basePath}/${id}/read`);

    // Mock implementation
    await simulateDelay();

    const notificationIndex = mockNotifications.findIndex((n) => n.id === id);
    if (notificationIndex === -1) {
      throw new Error("Notification not found");
    }

    mockNotifications[notificationIndex].status = "read";
    mockNotifications[notificationIndex].read_at = new Date().toISOString();
    mockNotifications[notificationIndex].updated_at = new Date().toISOString();

    return { message: "Notification marked as read successfully" };
  },

  delete: async (id: string) => {
    // return await ApiService.delete(`${basePath}/${id}`);

    // Mock implementation
    await simulateDelay();

    const notificationIndex = mockNotifications.findIndex((n) => n.id === id);
    if (notificationIndex === -1) {
      throw new Error("Notification not found");
    }

    mockNotifications.splice(notificationIndex, 1);

    return { message: "Notification deleted successfully" };
  },

  getAnalytics: async (period: "day" | "week" | "month" | "year" = "week") => {
    // return await ApiService.get(`${basePath}/analytics`, { period });

    // Mock implementation
    await simulateDelay();

    return mockAnalyticsData;
  },

  getDeliveryReport: async (filters) => {
    // return await ApiService.get(`${basePath}/delivery-report`, filters);

    // Mock implementation
    await simulateDelay();

    const filteredData = filterMockNotifications(filters);

    const report = {
      total_notifications: filteredData.length,
      successful_deliveries: filteredData.filter((n) =>
        ["sent", "delivered", "read", "clicked"].includes(n.status)
      ).length,
      failed_deliveries: filteredData.filter((n) => n.status === "failed")
        .length,
      pending_deliveries: filteredData.filter((n) => n.status === "pending")
        .length,
      delivery_rate: 0,
      by_channel: {} as Record<string, number>,
      by_type: {} as Record<string, number>,
    };

    report.delivery_rate =
      report.total_notifications > 0
        ? (report.successful_deliveries / report.total_notifications) * 100
        : 0;

    // Group by channel
    filteredData.forEach((notification) => {
      report.by_channel[notification.channel] =
        (report.by_channel[notification.channel] || 0) + 1;
    });

    // Group by type
    filteredData.forEach((notification) => {
      report.by_type[notification.type] =
        (report.by_type[notification.type] || 0) + 1;
    });

    return report;
  },

  export: async (filters) => {
    // return await ApiService.get(`${basePath}/export`, filters, {
    //   responseType: "blob",
    // });

    // Mock implementation
    await simulateDelay(1500); // Longer delay for export

    const filteredData = filterMockNotifications(filters);

    // Create CSV content
    const headers = [
      "ID",
      "User Name",
      "User Email",
      "Type",
      "Channel",
      "Title",
      "Status",
      "Sent At",
      "Read At",
      "Created At",
    ];

    const csvContent = [
      headers.join(","),
      ...filteredData.map((notification) =>
        [
          notification.id,
          `"${notification.user_name}"`,
          notification.user_email,
          notification.type,
          notification.channel,
          `"${notification.title}"`,
          notification.status,
          notification.sent_at || "",
          notification.read_at || "",
          notification.created_at,
        ].join(",")
      ),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });

    return {
      data: blob,
      message: "Export completed successfully",
    };
  },
};

export default NotificationService;
