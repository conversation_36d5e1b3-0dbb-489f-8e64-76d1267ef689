// services/permissionService.ts
import ApiService from "@/services/ApiService";
import { ApiDetailResponse, ApiListResponse } from "@/types/common";
import { IUser, UserFormData } from "@/types/user";

interface IPermissionService {
  getList: (
    params?: Record<string, unknown>
  ) => Promise<ApiListResponse<IUser>>;
  getDetail: (id: string) => Promise<ApiDetailResponse<IUser>>;
  create: (data: UserFormData) => Promise<unknown>;
  update: (id: string, data: UserFormData) => Promise<unknown>;
  delete: (id: string | number) => Promise<unknown>;
  changeStatus: (
    id: string | number,
    active: boolean
  ) => Promise<ApiDetailResponse<IUser>>;
}

const basePath = "v1/admin/users";

const UserService: IPermissionService = {
  getList: async (params) => {
    return await ApiService.post(`${basePath}/search`, params);
  },
  getDetail: async (id) => {
    return await ApiService.get(`${basePath}/${id}`);
  },
  create: async (data) => {
    return await ApiService.post(`${basePath}`, data);
  },
  update: async (id, data) => {
    return await ApiService.put(`${basePath}/${id}`, data);
  },
  delete: async (id) => {
    return await ApiService.delete(`${basePath}/${id}`);
  },
  changeStatus: async (id, active) => {
    return await ApiService.patch(`${basePath}/${id}/status?active=${active}`);
  },
};

export default UserService;
