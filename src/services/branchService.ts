import ApiService from "@/services/ApiService";
import { Branch } from "@/types/branch";
import { ApiDetailResponse, ApiListResponse } from "@/types/common";

interface IBranchService {
  getList: (
    params?: Record<string, unknown>
  ) => Promise<ApiListResponse<Branch>>;
  getDetail: (
    companyId: string,
    branchId: string
  ) => Promise<ApiDetailResponse<Branch>>;
  create: (
    companyId: string,
    data: Branch
  ) => Promise<ApiDetailResponse<Branch>>;
  update: (
    companyId: string,
    id: string,
    data: Branch
  ) => Promise<ApiDetailResponse<Branch>>;
  delete: (companyId: string, id: string) => Promise<unknown>;
}

const basePath = "v1/admin/companies";

const BranchService: IBranchService = {
  getList: async (params?: Record<string, unknown>) => {
    return await ApiService.post(
      `${basePath}/${params?.companyId}/branches/search`,
      params
    );
  },
  getDetail: async (companyId: string, branchId: string) => {
    return await ApiService.get(
      `${basePath}/${companyId}/branches/${branchId}`
    );
  },

  create: async (companyId: string, data) => {
    return await ApiService.post(`${basePath}/${companyId}/branches`, data);
  },
  update: async (companyId: string, id, data) => {
    return await ApiService.put(
      `${basePath}/${companyId}/branches/${id}`,
      data
    );
  },
  delete: async (companyId, id) => {
    return await ApiService.delete(`${basePath}/${companyId}/branches/${id}`);
  },
};

export default BranchService;
