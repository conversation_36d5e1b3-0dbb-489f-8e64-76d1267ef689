// services/interview/interviewService.ts
import ApiService from "./ApiService";
import {
  Interview,
  InterviewFilter,
  InterviewStats,
  CreateInterviewRequest,
  UpdateInterviewRequest,
} from "@/types/interview";
import { PaginationResponse } from "@/types/common";

/**
 * Service for handling interview-related API requests
 */
const InterviewService = {
  /**
   * Search interviews with filtering and pagination
   */
  searchInterviews: async (filter: InterviewFilter = {}) => {
    const response = await ApiService.post<
      {
        data: Interview[];
        pagination: PaginationResponse;
      },
      InterviewFilter
    >("/v1/admin/interviews/search", filter);

    return {
      data: response.data.data,
      pagination: response.data.pagination,
    };
  },

  /**
   * Fetch a single interview by ID
   */
  fetchInterviewById: async (id: number) => {
    const response = await ApiService.get<Interview>(
      `/v1/admin/interviews/${id}`
    );
    return response.data;
  },

  /**
   * Create a new interview
   */
  createInterview: async (interviewData: CreateInterviewRequest) => {
    const response = await ApiService.post<Interview, CreateInterviewRequest>(
      "/v1/admin/interviews",
      interviewData
    );
    return response.data;
  },

  /**
   * Update an existing interview
   */
  updateInterview: async (
    id: number,
    interviewData: UpdateInterviewRequest
  ) => {
    const response = await ApiService.put<Interview, UpdateInterviewRequest>(
      `/v1/admin/interviews/${id}`,
      interviewData
    );
    return response.data;
  },

  /**
   * Delete an interview
   */
  deleteInterview: async (id: number) => {
    const response = await ApiService.delete<boolean>(
      `/v1/admin/interviews/${id}`
    );
    return response.data;
  },

  /**
   * Update interview status
   */
  updateInterviewStatus: async (id: number, status: string) => {
    const response = await ApiService.patch<Interview, Record<string, unknown>>(
      `/v1/admin/interviews/${id}/status?status=${status}`
    );
    return response.data;
  },

  /**
   * Cancel an interview
   */
  cancelInterview: async (id: number, reason: string) => {
    const response = await ApiService.patch<Interview, Record<string, unknown>>(
      `/v1/admin/interviews/${id}/cancel?reason=${encodeURIComponent(reason)}`
    );
    return response.data;
  },

  /**
   * Get interview statistics
   */
  getInterviewStats: async () => {
    const response = await ApiService.get<InterviewStats>(
      "/v1/admin/interviews/stats"
    );

    // Ensure all null values are converted to 0
    const stats = response.data;
    return {
      totalInterviews: stats.totalInterviews ?? 0,
      scheduledInterviews: stats.scheduledInterviews ?? 0,
      inProgressInterviews: stats.inProgressInterviews ?? 0,
      completedInterviews: stats.completedInterviews ?? 0,
      cancelledInterviews: stats.cancelledInterviews ?? 0,
      averageRating: stats.averageRating ?? 0,
      todayInterviews: stats.todayInterviews ?? 0,
      thisWeekInterviews: stats.thisWeekInterviews ?? 0,
      thisMonthInterviews: stats.thisMonthInterviews ?? 0,
    };
  },

  /**
   * Legacy method for backward compatibility - fetch interviews
   */
  fetchInterviews: async (params?: Record<string, unknown>) => {
    // Convert legacy params to new filter format
    const filter: InterviewFilter = {
      page: params?.page as number,
      size: params?.size as number,
      search: params?.search as string,
      status: params?.status as string[],
    };

    return InterviewService.searchInterviews(filter);
  },

  /**
   * Legacy method for backward compatibility - change interview status
   */
  changeInterviewStatus: async (id: number, status: string) => {
    return InterviewService.updateInterviewStatus(id, status);
  },
};

export default InterviewService;
