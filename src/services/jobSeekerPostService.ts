import ApiService from "@/services/ApiService";
import { ApiDetailResponse, ApiListResponse } from "@/types/common";
// import { JobSeekerPost } from "@/types/jobSeekerPost"; // Nếu có type riêng, thêm vào

interface IJobSeekerPostService {
  getList: (params?: Record<string, unknown>) => Promise<ApiListResponse<any>>;
  getDetail: (id: string) => Promise<ApiDetailResponse<any>>;
  create: (data: any) => Promise<unknown>;
  update: (id: string, data: any) => Promise<unknown>;
  delete: (id: string) => Promise<unknown>;
  getStatistics: () => Promise<unknown>;
}

const basePath = "v1/admin/job-seeker-posts";

const JobSeekerPostService: IJobSeekerPostService = {
  getList: async (params) => {
    return await ApiService.post(`${basePath}/search`, params);
  },
  getDetail: async (id) => {
    return await ApiService.get(`${basePath}/${id}`);
  },
  create: async (data) => {
    return await ApiService.post(`${basePath}`, data);
  },
  update: async (id, data) => {
    return await ApiService.put(`${basePath}/${id}`, data);
  },
  delete: async (id) => {
    return await ApiService.delete(`${basePath}/${id}`);
  },
  getStatistics: async () => {
    return await ApiService.get(`${basePath}/statistics`);
  },
};

export default JobSeekerPostService;
