"use client";

import { LoadingBar } from "@/components/ui/loadings/LoadingBar";
import { AuthContext } from "@/contexts/AuthContext";
import { Image } from "antd";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { ReactNode, useContext } from "react";

export default function AuthLayout({ children }: { children: ReactNode }) {
  const authContext = useContext(AuthContext);
  const router = useRouter();

  if (authContext?.loading) return <LoadingBar />;
  // if (authContext?.isAuthenticated) {
  //   router.replace("/");
  //   return <LoadingBar />;
  // }

  return (
    <div className="flex min-h-screen">
      {/* Left side - Form */}
      <div className="w-full lg:w-1/2 flex flex-col p-10 justify-center items-center">
        <div className="max-w-md mx-auto w-full">
          {/* Main content - Auth forms */}
          <div className="flex-grow">{children}</div>

          {/* Footer links */}
          <div className="mt-12 flex justify-center space-x-6 text-sm text-gray-500">
            <Link href="#">Terms</Link>
            <Link href="#">Plans</Link>
            <Link href="#">Contact Us</Link>
          </div>
        </div>
      </div>

      {/* Right side - Branding/Showcase */}
      <div className="hidden lg:flex w-1/2 bg-indigo-800 text-white p-12 flex-col justify-between relative overflow-hidden">
        <div className="relative z-10">
          {/* Logo in white */}
          <div className="flex justify-center mb-8">
            <Image src="/dark-logo.svg" alt="Flexin" width={180} height={50} />
          </div>

          {/* Floating UI mockups */}
          <div className="relative h-96">
            {/* Dashboard mockup */}
            <div className="absolute top-20 right-40 rounded-lg border border-white/20 bg-white overflow-hidden w-64">
              <div className="bg-white h-6 flex items-center px-2">
                <div className="w-2 h-2 rounded-full bg-red-500 mr-1"></div>
                <div className="w-2 h-2 rounded-full bg-yellow-500 mr-1"></div>
                <div className="w-2 h-2 rounded-full bg-green-500"></div>
              </div>
              <div className="p-3 bg-blue-50">
                <div className="w-full h-3 bg-gray-200 rounded-sm mb-2"></div>
                <div className="w-3/4 h-3 bg-gray-200 rounded-sm mb-2"></div>
                <div className="flex space-x-2 mt-4">
                  <div className="w-16 h-16 bg-blue-100 rounded-sm"></div>
                  <div className="w-16 h-16 bg-blue-100 rounded-sm"></div>
                </div>
              </div>
            </div>

            {/* Profile mockup */}
            <div className="absolute top-10 left-30 bg-white rounded-lg border border-white/20 overflow-hidden w-64">
              <div className="bg-indigo-700 h-20 flex justify-center relative">
                <div className="w-16 h-16 bg-white rounded-full absolute -bottom-8 border-4 border-white"></div>
              </div>
              <div className="mt-10 p-3 text-center bg-white">
                <div className="w-3/4 h-3 bg-gray-200 rounded-sm mx-auto mb-2"></div>
                <div className="w-1/2 h-2 bg-gray-200 rounded-sm mx-auto mb-4"></div>
                <div className="flex justify-center space-x-2 mb-4">
                  <div className="w-8 h-8 bg-blue-100 rounded-full"></div>
                  <div className="w-8 h-8 bg-purple-100 rounded-full"></div>
                  <div className="w-8 h-8 bg-blue-100 rounded-full"></div>
                </div>
              </div>
            </div>

            {/* Stats mockup */}
            <div className="absolute  bottom-0 right-70 bg-white rounded-lg border border-white/20 overflow-hidden w-72">
              <div className="bg-indigo-700 text-white px-3 py-2 text-sm">
                Dashboard
              </div>
              <div className="p-4 bg-white text-gray-800">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-xs text-gray-600">Total Jobs</div>
                    <div className="text-lg font-medium text-gray-900">
                      1,254
                    </div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-600">Applications</div>
                    <div className="text-lg font-medium text-indigo-600">
                      3,829
                    </div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-600">Success Rate</div>
                    <div className="text-lg font-medium text-green-600">
                      67 %
                    </div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-600">Interviews</div>
                    <div className="text-lg font-medium text-indigo-600">
                      428
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Tagline */}
          <div className="mt-12 text-center">
            <h2 className="text-3xl font-bold mb-4">
              Connect, Recruit, Succeed
            </h2>
            <p className="text-white/80 mb-8">
              The modern platform where employers and job seekers meet.
              Streamline your recruitment process, discover top talent, and
              build your professional career with our comprehensive toolset.
            </p>

            {/* Stats counter */}
            <div className="flex justify-center space-x-16 mt-8">
              <div className="text-center">
                <div className="text-xs text-white/70 mb-1">Employers</div>
                <div className="text-2xl font-bold">10k+</div>
              </div>
              <div className="text-center">
                <div className="text-xs text-white/70 mb-1">Job Seekers</div>
                <div className="text-2xl font-bold">150k+</div>
              </div>
              <div className="text-center">
                <div className="text-xs text-white/70 mb-1">Satisfaction</div>
                <div className="text-2xl font-bold">95%</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
