"use client";

import { useNotification } from "@/contexts/NotiContext";
import { useContext } from "react";
import { AuthContext } from "@/contexts/AuthContext";
import { SignInFormValues } from "@/types/auth";
import {
  EyeInvisibleOutlined,
  EyeOutlined,
  LockOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { Button, Form, Input, Typography } from "antd";
import { useRouter } from "next/navigation";
import { useState } from "react";

const { Title, Text } = Typography;

export default function SignIn() {
  const router = useRouter();
  const notification = useNotification();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const authContext = useContext(AuthContext);

  const onFinish = async (values: SignInFormValues) => {
    setLoading(true);
    try {
      await authContext?.login(values);

      notification.notifySuccess("Login successful!");

      router.push("/");
    } catch (error) {
      console.log("🚀 ~ onFinish ~ error:", error);
      notification.notifyError("Login failed!", "");
      setLoading(false);
    }
  };

  return (
    <>
      <Title level={2} className="mb-6 text-center">
        Sign In
      </Title>

      <Form form={form} name="signin" onFinish={onFinish} layout="vertical">
        <Form.Item
          name="email"
          label="Email"
          rules={[
            { required: true, message: "Please input your email!" },
            { type: "email", message: "Please enter a valid email address!" },
          ]}
        >
          <Input
            prefix={<UserOutlined className="text-gray-400" />}
            placeholder="Email address"
          />
        </Form.Item>

        <Form.Item
          name="password"
          label="Password"
          rules={[{ required: true, message: "Please input your password!" }]}
        >
          <Input.Password
            prefix={<LockOutlined className="text-gray-400" />}
            placeholder="Password"
            iconRender={(visible) =>
              visible ? <EyeOutlined /> : <EyeInvisibleOutlined />
            }
          />
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            className="w-full"
          >
            Continue
          </Button>
        </Form.Item>
      </Form>

      <div className="mt-4 text-center">
        <Text type="secondary" className="text-xs">
          Your connection is secure and encrypted 🔒
        </Text>
      </div>
    </>
  );
}
