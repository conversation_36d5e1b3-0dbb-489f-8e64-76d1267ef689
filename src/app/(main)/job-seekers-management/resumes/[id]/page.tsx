"use client";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useState, useEffect, useContext } from "react";
import { Card, Typography, message, Button, Spin, Modal, App } from "antd";
import { EditOutlined } from "@ant-design/icons";
import { useTranslations } from "next-intl";
import { AuthContext } from "@/contexts/AuthContext";
import { getResumeById, setResumeIsActive } from "@/services/resumeService";
import { Resume } from "@/types/resume";
import ResumeView from "@/components/features/users/job-seeker-resumes/ResumeView";

const { Title } = Typography;

const ResumeDetailPage = () => {
  const params = useParams();
  const router = useRouter();
  const t = useTranslations("route");
  const tCommon = useTranslations("common");
  const authContext = useContext(AuthContext);
  const access = authContext?.access;
  const { modal } = App.useApp();

  const [resume, setResume] = useState<Resume | null>(null);
  const [loading, setLoading] = useState(true);
  const [activateLoading, setActivateLoading] = useState(false);

  const resumeId = params.id as string;

  useEffect(() => {
    const fetchResume = async () => {
      if (!resumeId) return;

      try {
        setLoading(true);
        const resumeData = await getResumeById(resumeId);
        setResume(resumeData);
      } catch (error) {
        console.error("Error fetching resume:", error);
        message.error("Failed to load resume details");
      } finally {
        setLoading(false);
      }
    };

    fetchResume();
  }, [resumeId]);

  if (!access?.viewUser) {
    return (
      <Card>
        <Typography.Text type="danger">
          {tCommon("status.no_permission")}
        </Typography.Text>
      </Card>
    );
  }

  const handleEdit = () => {
    router.push(`/job-seekers-management/resumes/${resumeId}/edit`);
  };

  const handleActivate = () => {
    if (!resume) return;
    modal.confirm({
      title: resume.isActive
        ? "Deactivate this resume?"
        : "Activate this resume?",
      content: resume.isActive
        ? "Are you sure you want to deactivate this resume?"
        : "Are you sure you want to set this resume as active?",
      okText: resume.isActive ? "Deactivate" : "Activate",
      okType: resume.isActive ? "default" : "primary",
      cancelText: "Cancel",
      onOk: async () => {
        setActivateLoading(true);
        try {
          const isActive = !resume.isActive;
          await setResumeIsActive(resumeId, isActive);
          setResume({ ...resume, isActive });
          message.success(
            isActive ? "Resume activated!" : "Resume deactivated!"
          );
        } catch (err) {
          message.error("Failed to update resume status");
        } finally {
          setActivateLoading(false);
        }
      },
    });
  };

  if (loading) {
    return (
      <Card>
        <div className="flex justify-center items-center min-h-[400px]">
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  if (!resume) {
    return (
      <Card>
        <div className="text-center">
          <Typography.Text type="danger">Resume not found</Typography.Text>
        </div>
      </Card>
    );
  }

  return (
    <App>
      <div className="space-y-4">
        <Card>
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center space-x-4">
              <Title level={4} className="mb-0">
                {resume.name}
              </Title>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                type="primary"
                icon={<EditOutlined />}
                onClick={handleEdit}
                size="large"
              >
                Edit Resume
              </Button>
              <Button
                type={resume.isActive ? "default" : "primary"}
                danger={resume.isActive}
                loading={activateLoading}
                onClick={handleActivate}
                size="large"
              >
                {resume.isActive ? "Deactivate Resume" : "Activate Resume"}
              </Button>
            </div>
          </div>

          <ResumeView resume={resume} />
        </Card>
      </div>
    </App>
  );
};

export default ResumeDetailPage;
