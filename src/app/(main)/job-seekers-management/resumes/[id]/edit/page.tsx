"use client";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useState, useEffect, useContext } from "react";
import {
  Card,
  Form,
  Button,
  message,
  Spin,
  Row,
  Col,
  Typography,
  Space,
  Input,
  Switch,
  DatePicker,
  Select,
  Checkbox,
  InputNumber,
  Divider,
} from "antd";
import {
  ArrowLeftOutlined,
  SaveOutlined,
  PlusOutlined,
  DeleteOutlined,
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  EnvironmentOutlined,
  LinkedinOutlined,
  GlobalOutlined,
  BankOutlined,
  BookOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";
import { useTranslations } from "next-intl";
import { AuthContext } from "@/contexts/AuthContext";
import { getResumeById, updateResume } from "@/services/resumeService";
import { Resume } from "@/types/resume";
import dayjs from "dayjs";
import { LANGUAGE_LEVELS } from "@/constants/languageLevels";

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const availableDays = [
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday",
];

const timeSlots = ["Morning", "Afternoon", "Evening", "Night"];

const ResumeEditPage = () => {
  const params = useParams();
  const router = useRouter();
  const t = useTranslations("route");
  const tCommon = useTranslations("common");
  const authContext = useContext(AuthContext);
  const access = authContext?.access;

  const [form] = Form.useForm();
  const [resume, setResume] = useState<Resume | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  const resumeId = params.id as string;

  useEffect(() => {
    const fetchResume = async () => {
      if (!resumeId) return;

      try {
        setLoading(true);
        const resumeData = await getResumeById(resumeId);
        setResume(resumeData);

        if (resumeData) {
          // Populate form with resume data
          form.setFieldsValue({
            name: resumeData.name,
            description: resumeData.description,
            skills: resumeData.skills?.join(", "),
            isActive: resumeData.isActive,
            contactInfo: {
              phoneNumber:
                resumeData.contactInfo?.phoneNumber ||
                resumeData.contactInfo?.phone,
              email: resumeData.contactInfo?.email,
              address: resumeData.contactInfo?.address,
              linkedInUrl: resumeData.contactInfo?.linkedInUrl,
              portfolioUrl: resumeData.contactInfo?.portfolioUrl,
            },
            workExperiences: resumeData.workExperiences?.map((exp) => ({
              ...exp,
              startDate: exp.startDate ? dayjs(exp.startDate) : null,
              endDate: exp.endDate ? dayjs(exp.endDate) : null,
            })),
            educations: resumeData.educations?.map((edu) => ({
              ...edu,
              startDate: edu.startDate ? dayjs(edu.startDate) : null,
              endDate: edu.endDate ? dayjs(edu.endDate) : null,
            })),
            languages: resumeData.languages || [],
            partTimePreference: resumeData.partTimePreference || {},
          });
        }
      } catch (error) {
        console.error("Error fetching resume:", error);
        message.error("Failed to load resume details");
      } finally {
        setLoading(false);
      }
    };

    fetchResume();
  }, [resumeId, form]);

  const handleSave = async () => {
    try {
      setSaving(true);
      const values = await form.validateFields();

      // Transform form data back to resume format
      const formData = {
        ...values,
        skills:
          values.skills
            ?.split(",")
            .map((skill: string) => skill.trim())
            .filter(Boolean) || [],
        workExperiences: values.workExperiences?.map((exp: any) => ({
          ...exp,
          startDate: exp.startDate ? exp.startDate.format("YYYY-MM-DD") : null,
          endDate: exp.endDate ? exp.endDate.format("YYYY-MM-DD") : null,
        })),
        educations: values.educations?.map((edu: any) => ({
          ...edu,
          startDate: edu.startDate ? edu.startDate.format("YYYY-MM-DD") : null,
          endDate: edu.endDate ? edu.endDate.format("YYYY-MM-DD") : null,
        })),
      };

      await updateResume(resumeId, formData);
      message.success("Resume updated successfully!");

      // Navigate back to detail page
      router.push(`/job-seekers-management/resumes/${resumeId}`);
    } catch (error: any) {
      console.error("Error saving resume:", error);

      // Handle validation errors
      if (error?.errorFields && error.errorFields.length > 0) {
        const firstError = error.errorFields[0];
        const fieldName = Array.isArray(firstError.name)
          ? firstError.name.join(".")
          : firstError.name;
        const errorMsg = firstError.errors?.[0] || "This field is required";

        message.error(`Validation Error: ${errorMsg}`);

        // Scroll to the first error field
        form.scrollToField(firstError.name);
        return;
      }

      // Handle API errors
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to save resume. Please check your input and try again.";

      message.error(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  const handleGoBack = () => {
    router.push(`/job-seekers-management/resumes/${resumeId}`);
  };

  if (!access?.viewUser) {
    return (
      <Card>
        <Typography.Text type="danger">
          {tCommon("status.no_permission")}
        </Typography.Text>
      </Card>
    );
  }

  if (loading) {
    return (
      <Card>
        <div className="flex justify-center items-center min-h-[400px]">
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  if (!resume) {
    return (
      <Card>
        <div className="text-center">
          <Typography.Text type="danger">Resume not found</Typography.Text>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Title level={3} style={{ margin: 0 }}>
                Edit Resume: {resume.name}
              </Title>
            </Space>
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSave}
              loading={saving}
              size="large"
            >
              Save Changes
            </Button>
          </Col>
        </Row>
      </Card>

      {/* Form */}
      <Form form={form} layout="vertical" requiredMark={false}>
        <Row gutter={[24, 0]}>
          {/* Left Column */}
          <Col xs={24} lg={12}>
            {/* Basic Information */}
            <Card
              title={
                <Title level={4} style={{ margin: 0 }}>
                  <UserOutlined /> Basic Information
                </Title>
              }
              style={{ marginBottom: 24 }}
            >
              <Form.Item
                label="Resume Name"
                name="name"
                rules={[
                  { required: true, message: "Please enter resume name" },
                ]}
              >
                <Input placeholder="Enter resume name" size="large" />
              </Form.Item>

              <Form.Item label="Description" name="description">
                <TextArea
                  placeholder="Enter resume description"
                  rows={4}
                  showCount
                  maxLength={500}
                />
              </Form.Item>

              <Form.Item
                label="Skills"
                name="skills"
                rules={[{ required: true, message: "Please enter skills" }]}
              >
                <Input
                  placeholder="Enter skills separated by commas (e.g., React, Node.js, TypeScript)"
                  size="large"
                />
              </Form.Item>

              <Form.Item name="isActive" valuePropName="checked">
                <Space>
                  <Switch />
                  <Text>Set as Active Resume</Text>
                </Space>
              </Form.Item>
            </Card>

            {/* Contact Information */}
            <Card
              title={
                <Title level={4} style={{ margin: 0 }}>
                  <PhoneOutlined /> Contact Information
                </Title>
              }
              style={{ marginBottom: 24 }}
            >
              <Form.Item
                label="Phone Number"
                name={["contactInfo", "phoneNumber"]}
                rules={[
                  { required: true, message: "Please enter phone number" },
                ]}
              >
                <Input
                  prefix={<PhoneOutlined />}
                  placeholder="Enter phone number"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                label="Email"
                name={["contactInfo", "email"]}
                rules={[
                  { required: true, message: "Please enter email" },
                  { type: "email", message: "Please enter valid email" },
                ]}
              >
                <Input
                  prefix={<MailOutlined />}
                  placeholder="Enter email address"
                  size="large"
                />
              </Form.Item>

              <Form.Item label="Address" name={["contactInfo", "address"]}>
                <TextArea
                  prefix={<EnvironmentOutlined />}
                  placeholder="Enter address"
                  rows={2}
                />
              </Form.Item>

              <Form.Item
                label="LinkedIn URL"
                name={["contactInfo", "linkedInUrl"]}
              >
                <Input
                  prefix={<LinkedinOutlined />}
                  placeholder="Enter LinkedIn profile URL"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                label="Portfolio URL"
                name={["contactInfo", "portfolioUrl"]}
              >
                <Input
                  prefix={<GlobalOutlined />}
                  placeholder="Enter portfolio website URL"
                  size="large"
                />
              </Form.Item>
            </Card>

            {/* Languages */}
            <Card
              title={
                <Title level={4} style={{ margin: 0 }}>
                  <GlobalOutlined /> Languages
                </Title>
              }
              style={{ marginBottom: 24 }}
            >
              <Form.List name="languages">
                {(fields, { add, remove }) => (
                  <>
                    {fields.map(({ key, name, ...restField }) => (
                      <Card key={key} type="inner" style={{ marginBottom: 16 }}>
                        <Row gutter={16}>
                          <Col span={12}>
                            <Form.Item
                              {...restField}
                              name={[name, "language"]}
                              label="Language"
                              rules={[
                                {
                                  required: true,
                                  message: "Please enter language",
                                },
                              ]}
                            >
                              <Input placeholder="e.g., English" />
                            </Form.Item>
                          </Col>
                          <Col span={10}>
                            <Form.Item
                              {...restField}
                              name={[name, "level"]}
                              label="Level"
                              rules={[
                                {
                                  required: true,
                                  message: "Please select level",
                                },
                              ]}
                            >
                              <Select placeholder="Select level">
                                {LANGUAGE_LEVELS.map((level) => (
                                  <Option key={level.value} value={level.value}>
                                    {level.label}
                                  </Option>
                                ))}
                              </Select>
                            </Form.Item>
                          </Col>
                          <Col span={2}>
                            <Form.Item label=" ">
                              <Button
                                type="text"
                                danger
                                icon={<DeleteOutlined />}
                                onClick={() => remove(name)}
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                      </Card>
                    ))}
                    <Button
                      type="dashed"
                      onClick={() => add()}
                      icon={<PlusOutlined />}
                      block
                    >
                      Add Language
                    </Button>
                  </>
                )}
              </Form.List>
            </Card>
          </Col>

          {/* Right Column */}
          <Col xs={24} lg={12}>
            {/* Work Experience */}
            <Card
              title={
                <Title level={4} style={{ margin: 0 }}>
                  <BankOutlined /> Work Experience
                </Title>
              }
              style={{ marginBottom: 24 }}
            >
              <Form.List name="workExperiences">
                {(fields, { add, remove }) => (
                  <>
                    {fields.map(({ key, name, ...restField }) => (
                      <Card key={key} type="inner" style={{ marginBottom: 16 }}>
                        <Row gutter={16}>
                          <Col span={12}>
                            <Form.Item
                              {...restField}
                              name={[name, "position"]}
                              label="Position"
                              rules={[
                                {
                                  required: true,
                                  message: "Please enter position",
                                },
                              ]}
                            >
                              <Input placeholder="e.g., Software Developer" />
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              {...restField}
                              name={[name, "company"]}
                              label="Company"
                              rules={[
                                {
                                  required: true,
                                  message: "Please enter company",
                                },
                              ]}
                            >
                              <Input placeholder="e.g., Tech Company Inc." />
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              {...restField}
                              name={[name, "startDate"]}
                              label="Start Date"
                              rules={[
                                {
                                  required: true,
                                  message: "Please select start date",
                                },
                              ]}
                            >
                              <DatePicker style={{ width: "100%" }} />
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              {...restField}
                              name={[name, "endDate"]}
                              label="End Date"
                            >
                              <DatePicker
                                style={{ width: "100%" }}
                                placeholder="Current"
                              />
                            </Form.Item>
                          </Col>
                          <Col span={22}>
                            <Form.Item
                              {...restField}
                              name={[name, "description"]}
                              label="Description"
                            >
                              <TextArea
                                rows={3}
                                placeholder="Describe your responsibilities and achievements"
                              />
                            </Form.Item>
                          </Col>
                          <Col span={2}>
                            <Form.Item label=" ">
                              <Button
                                type="text"
                                danger
                                icon={<DeleteOutlined />}
                                onClick={() => remove(name)}
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                      </Card>
                    ))}
                    <Button
                      type="dashed"
                      onClick={() => add()}
                      icon={<PlusOutlined />}
                      block
                    >
                      Add Work Experience
                    </Button>
                  </>
                )}
              </Form.List>
            </Card>

            {/* Education */}
            <Card
              title={
                <Title level={4} style={{ margin: 0 }}>
                  <BookOutlined /> Education
                </Title>
              }
              style={{ marginBottom: 24 }}
            >
              <Form.List name="educations">
                {(fields, { add, remove }) => (
                  <>
                    {fields.map(({ key, name, ...restField }) => (
                      <Card key={key} type="inner" style={{ marginBottom: 16 }}>
                        <Row gutter={16}>
                          <Col span={12}>
                            <Form.Item
                              {...restField}
                              name={[name, "degree"]}
                              label="Degree"
                              rules={[
                                {
                                  required: true,
                                  message: "Please enter degree",
                                },
                              ]}
                            >
                              <Input placeholder="e.g., Bachelor of Science" />
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              {...restField}
                              name={[name, "institution"]}
                              label="Institution"
                              rules={[
                                {
                                  required: true,
                                  message: "Please enter institution",
                                },
                              ]}
                            >
                              <Input placeholder="e.g., University of Technology" />
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              {...restField}
                              name={[name, "fieldOfStudy"]}
                              label="Field of Study"
                            >
                              <Input placeholder="e.g., Computer Science" />
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              {...restField}
                              name={[name, "startDate"]}
                              label="Start Date"
                              rules={[
                                {
                                  required: true,
                                  message: "Please select start date",
                                },
                              ]}
                            >
                              <DatePicker style={{ width: "100%" }} />
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              {...restField}
                              name={[name, "endDate"]}
                              label="End Date"
                            >
                              <DatePicker
                                style={{ width: "100%" }}
                                placeholder="Current"
                              />
                            </Form.Item>
                          </Col>
                          <Col span={10}>
                            <Form.Item
                              {...restField}
                              name={[name, "description"]}
                              label="Description"
                            >
                              <TextArea
                                rows={2}
                                placeholder="Additional details"
                              />
                            </Form.Item>
                          </Col>
                          <Col span={2}>
                            <Form.Item label=" ">
                              <Button
                                type="text"
                                danger
                                icon={<DeleteOutlined />}
                                onClick={() => remove(name)}
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                      </Card>
                    ))}
                    <Button
                      type="dashed"
                      onClick={() => add()}
                      icon={<PlusOutlined />}
                      block
                    >
                      Add Education
                    </Button>
                  </>
                )}
              </Form.List>
            </Card>
          </Col>
        </Row>

        {/* Part-time Preferences (Full Width) */}
        <Card
          title={
            <Title level={4} style={{ margin: 0 }}>
              <ClockCircleOutlined /> Part-time Preferences
            </Title>
          }
          style={{ marginBottom: 24 }}
        >
          <Row gutter={[24, 16]}>
            <Col xs={24} sm={12} md={6}>
              <Form.Item
                label="Min Hourly Rate"
                name={["partTimePreference", "minHourlyRate"]}
              >
                <InputNumber
                  style={{ width: "100%" }}
                  placeholder="Min rate"
                  min={0}
                  formatter={(value) =>
                    `VND ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                  }
                  parser={(value) => value!.replace(/\$\s?|(,*)/g, "")}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item
                label="Max Hours Per Week"
                name={["partTimePreference", "maxHoursPerWeek"]}
              >
                <InputNumber
                  style={{ width: "100%" }}
                  placeholder="Max hours"
                  min={1}
                  max={168}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item
                label="Max Travel Distance"
                name={["partTimePreference", "maxTravelDistance"]}
              >
                <InputNumber
                  style={{ width: "100%" }}
                  placeholder="Distance (km)"
                  min={0}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item
                name={["partTimePreference", "remoteOnly"]}
                valuePropName="checked"
              >
                <Space>
                  <Switch />
                  <Text>Remote Only</Text>
                </Space>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                label="Available Days"
                name={["partTimePreference", "availableDays"]}
              >
                <Checkbox.Group
                  defaultValue={resume?.partTimePreference?.availableDays}
                >
                  <Row>
                    {availableDays.map((day) => (
                      <Col span={8} key={day}>
                        <Checkbox value={day}>{day}</Checkbox>
                      </Col>
                    ))}
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                label="Available Time Slots"
                name={["partTimePreference", "availableTimeSlots"]}
              >
                <Checkbox.Group>
                  <Row>
                    {timeSlots.map((slot) => (
                      <Col span={12} key={slot}>
                        <Checkbox value={slot}>{slot}</Checkbox>
                      </Col>
                    ))}
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                label="Preferred Job Types"
                name={["partTimePreference", "preferredJobTypes"]}
              >
                <Select
                  mode="tags"
                  placeholder="Enter job types"
                  style={{ width: "100%" }}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                label="Preferred Locations"
                name={["partTimePreference", "preferredLocations"]}
              >
                <Select
                  mode="tags"
                  placeholder="Enter preferred locations"
                  style={{ width: "100%" }}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name={["partTimePreference", "isStudent"]}
                valuePropName="checked"
                noStyle
              >
                <Switch />
              </Form.Item>
              <Text style={{ marginLeft: 8 }}>Currently a Student</Text>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                label="Study Major"
                name={["partTimePreference", "studyMajor"]}
              >
                <Input placeholder="Enter study major" />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label="Additional Notes"
                name={["partTimePreference", "additionalNotes"]}
              >
                <TextArea
                  rows={3}
                  placeholder="Any additional information or requirements"
                  showCount
                  maxLength={1000}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Save Button */}
        <Card>
          <Row justify="center">
            <Space size="large">
              <Button size="large" onClick={handleGoBack}>
                Cancel
              </Button>
              <Button
                type="primary"
                size="large"
                icon={<SaveOutlined />}
                onClick={handleSave}
                loading={saving}
              >
                Save Resume
              </Button>
            </Space>
          </Row>
        </Card>
      </Form>
    </div>
  );
};

export default ResumeEditPage;
