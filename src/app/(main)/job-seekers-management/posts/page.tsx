"use client";

import JobSeekerTable from "@/components/features/posts/tables/job-seeker/JobSeekerTable";
import { Card, Typography } from "antd";
import { useTranslations } from "next-intl";
import { useContext } from "react";
import { AuthContext } from "@/contexts/AuthContext";

const JobSeekerPostsPage = () => {
  const t = useTranslations("posts");
  const tCommon = useTranslations("common");
  const authContext = useContext(AuthContext);
  const access = authContext?.access;

  if (!access?.viewJobSeekerPost) {
    return (
      <Card>
        <Typography.Text type="danger">
          {tCommon("status.no_permission")}
        </Typography.Text>
      </Card>
    );
  }

  return (
    <Card>
      <Typography.Title level={4}>
        {t("post_management") + " - " + tCommon("roles.job_seeker")}
      </Typography.Title>
      <JobSeekerTable />
    </Card>
  );
};

export default JobSeekerPostsPage;
