"use client";

import UserFormModal from "@/components/features/users/UserFormModal";
import BaseTable from "@/components/ui/tables/BaseTable";
import { PermissionEnum } from "@/constants/_permissionEnum";
import { AuthContext } from "@/contexts/AuthContext";
import AccountService from "@/services/userService";
import { IUser } from "@/types/user";
import { utils } from "@/utils";
import { UserRole } from "@/constants/userRole";
import { Card, message } from "antd";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useContext, useRef, useState } from "react";

export default function JobSeekerUsersPage() {
  const router = useRouter();
  const t = useTranslations("user");
  const tCommon = useTranslations("common");
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [currentUser, setCurrentUser] = useState<IUser | null>(null);
  const tableRef = useRef<{ refetch: () => void }>(null);
  const authContext = useContext(AuthContext);
  const access = authContext?.access;

  const columns = [
    { title: tCommon("fields.id"), dataIndex: "id" },
    { title: tCommon("fields.name"), dataIndex: "name" },
    { title: tCommon("fields.phone"), dataIndex: "phoneNumber" },
    { title: tCommon("fields.email"), dataIndex: "email" },
    { title: tCommon("fields.role"), dataIndex: "role" },
    {
      title: t("email_verified"),
      dataIndex: "isEmailVerified",
      render: (value: boolean) => (value ? "✔️" : "❌"),
    },
    {
      title: t("phone_verified"),
      dataIndex: "isPhoneVerified",
      render: (value: boolean) => (value ? "✔️" : "❌"),
    },
    {
      title: tCommon("fields.created_at"),
      dataIndex: "createdAt",
      render: utils.formatTableDate,
    },
    {
      title: tCommon("fields.last_login"),
      dataIndex: "lastLogin",
      render: utils.formatTableDate,
    },
  ];

  const filters = [
    {
      key: "name",
      label: t("name_filter"),
      type: "text" as const,
    },
    {
      key: "status",
      label: t("status_filter"),
      type: "select" as const,
      options: [
        { value: true, label: tCommon("states.active") },
        { value: false, label: tCommon("states.inactive") },
      ],
    },
  ];

  const handleEdit = (record: IUser) => {
    setCurrentUser(record);
    setIsEditModalVisible(true);
  };

  const handleCreate = () => {
    setIsEditModalVisible(true);
  };

  const handleDelete = async (record: IUser) => {
    try {
      await AccountService.delete(record.id || "");
      message.success(t("user_deleted_successfully", { name: record.name }));
      handleFormSuccess();
    } catch (error) {
      message.error(
        t("failed_to_delete_user", {
          error: error instanceof Error ? error.message : String(error),
        })
      );
      throw error;
    }
  };

  const navigateToUserDetail = (record: IUser) => {
    router.push(`/job-seekers-management/list/${record.id}`);
  };

  const handleFormSuccess = () => {
    setIsEditModalVisible(false);
    if (tableRef.current) {
      tableRef.current.refetch();
    }
  };

  const handleCloseUserFormModal = () => {
    setIsEditModalVisible(false);
    setCurrentUser(null);
  };

  // API function with role filter
  const getJobSeekerUsers = (params: any) => {
    return AccountService.getList({ ...params, role: UserRole.JOB_SEEKER });
  };

  return (
    <Card>
      <BaseTable<IUser>
        ref={tableRef}
        api={getJobSeekerUsers}
        columns={columns}
        rowKey="id"
        title="Job Seeker Users"
        initialParams={{ pageSize: 10 }}
        createBtnText={t("add_user")}
        onCreate={access && access[PermissionEnum.USER_CREATE] && handleCreate}
        onEdit={access && access[PermissionEnum.USER_UPDATE] && handleEdit}
        onDelete={access && access[PermissionEnum.USER_DELETE] && handleDelete}
        onRowClick={navigateToUserDetail}
        filters={filters}
      />

      <UserFormModal
        isVisible={isEditModalVisible}
        onCancel={handleCloseUserFormModal}
        onSuccess={handleFormSuccess}
        currentUser={currentUser}
        lockedUserType={UserRole.JOB_SEEKER}
      />
    </Card>
  );
}
