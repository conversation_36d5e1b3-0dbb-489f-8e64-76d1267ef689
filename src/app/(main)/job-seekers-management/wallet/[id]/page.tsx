"use client";

import React from "react";
import { <PERSON>, But<PERSON> } from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";
import { useRouter, useParams } from "next/navigation";
import WalletDetail from "@/components/features/wallet/WalletDetail";
import BasePageTitle from "@/components/ui/common/BasePageTitle";

export default function WalletDetailPage() {
  const router = useRouter();
  const params = useParams();
  const walletId = params.id ? parseInt(params.id as string) : undefined;

  const handleGoBack = () => {
    router.back();
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <BasePageTitle
          title="Wallet Details"
          subtitle="View detailed wallet information and manage points"
        />
        <Button icon={<ArrowLeftOutlined />} onClick={handleGoBack}>
          Back
        </Button>
      </div>

      <Card>
        {walletId && (
          <WalletDetail walletId={walletId} onClose={handleGoBack} />
        )}
      </Card>
    </div>
  );
}
