"use client";

import WalletTable from "@/components/features/wallet/WalletTable";
import WalletService from "@/services/walletService";
import { AuthContext } from "@/contexts/AuthContext";
import { Card, Typography } from "antd";
import { useTranslations } from "next-intl";
import { useContext } from "react";

export default function JobSeekerWalletPage() {
  const tWallet = useTranslations("wallet");
  const tCommon = useTranslations("common");
  const authContext = useContext(AuthContext);
  const access = authContext?.access;

  if (!access?.viewWallet) {
    return (
      <Card>
        <Typography.Text type="danger">
          {tCommon("status.no_permission")}
        </Typography.Text>
      </Card>
    );
  }
  return (
    <div>
      <Card>
        <WalletTable
          api={WalletService.getJobSeekerWallets}
          tTitle={tWallet("job_seeker_wallets")}
        />
      </Card>
    </div>
  );
}
