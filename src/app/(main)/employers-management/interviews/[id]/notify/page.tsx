// app/(main)/admin/interviews/[id]/notify/page.tsx
"use client";
import React, { useEffect, useState } from "react";
import {
  Card,
  Button,
  Skeleton,
  Result,
  Typography,
  Breadcrumb,
  Flex,
} from "antd";
import {
  ArrowLeftOutlined,
  HomeOutlined,
  SendOutlined,
} from "@ant-design/icons";
import { useRouter, useParams } from "next/navigation";
import { Interview } from "@/types/interview";
import InterviewService from "@/services/interviewService";
import Link from "next/link";
import NotificationForm from "@/components/features/interviews/InterviewNotificationForm";

const { Title, Text } = Typography;

const InterviewNotificationPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const interviewId = params.id as string;

  const [interview, setInterview] = useState<Interview | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchInterviewDetails = async () => {
      setLoading(true);
      setError(null);

      try {
        if (!interviewId) {
          setError("Interview ID is required");
          setLoading(false);
          return;
        }

        const interviewData = await InterviewService.fetchInterviewById(
          interviewId
        );
        setInterview(interviewData);
      } catch (err) {
        console.error("Error fetching interview details:", err);
        setError("Failed to load interview details. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchInterviewDetails();
  }, [interviewId]);

  const handleGoBack = () => {
    router.push(`/admin/interviews/${interviewId}`);
  };

  const handleSuccess = () => {
    router.push(`/admin/interviews/${interviewId}?notification=sent`);
  };

  return (
    <div className="p-6">
      {/* Breadcrumb navigation */}
      <Breadcrumb
        className="mb-4"
        items={[
          {
            title: (
              <Link href="/admin/dashboard">
                <HomeOutlined /> Dashboard
              </Link>
            ),
          },
          {
            title: <Link href="/admin/interviews">Interviews</Link>,
          },
          {
            title: (
              <Link href={`/admin/interviews/${interviewId}`}>
                {interviewId}
              </Link>
            ),
          },
          {
            title: "Send Notification",
          },
        ]}
      />

      {/* Back button and title */}
      <Flex justify="space-between" align="center" className="mb-4">
        <Button icon={<ArrowLeftOutlined />} onClick={handleGoBack}>
          Back to Interview
        </Button>
      </Flex>

      <Title level={3} className="mb-4">
        <SendOutlined /> Send Interview Notification
      </Title>

      {/* Main content */}
      {loading ? (
        <Card>
          <Skeleton active paragraph={{ rows: 10 }} />
        </Card>
      ) : error ? (
        <Result
          status="error"
          title="Error"
          subTitle={error}
          extra={[
            <Button
              type="primary"
              key="retry"
              onClick={() => window.location.reload()}
            >
              Try Again
            </Button>,
            <Button key="back" onClick={handleGoBack}>
              Go Back
            </Button>,
          ]}
        />
      ) : interview ? (
        <>
          <Card className="mb-4">
            <Flex align="center" gap="middle">
              <SendOutlined style={{ fontSize: 24, color: "#1890ff" }} />
              <div>
                <Title level={5} className="m-0">
                  Sending Notification for Interview
                </Title>
                <Text type="secondary">
                  Regarding {interview.jobPostTitle} position -{" "}
                  {interview.candidateName} and {interview.employerName}
                </Text>
              </div>
            </Flex>
          </Card>

          <NotificationForm
            interviewId={interviewId}
            candidateName={interview.candidateName}
            employerName={interview.employerName}
            onSuccess={handleSuccess}
            onCancel={handleGoBack}
          />
        </>
      ) : (
        <Result
          status="404"
          title="Interview Not Found"
          subTitle="The interview you're looking for doesn't exist or has been removed."
          extra={
            <Button
              type="primary"
              onClick={() => router.push("/admin/interviews")}
            >
              Back to Interview List
            </Button>
          }
        />
      )}
    </div>
  );
};

export default InterviewNotificationPage;
