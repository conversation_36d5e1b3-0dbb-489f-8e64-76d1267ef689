// app/(main)/admin/interviews/[id]/page.tsx
"use client";
import InterviewDetail from "@/components/features/interviews/InterviewDetail";
import InterviewService from "@/services/interviewService";
import { Interview } from "@/types/interview";
import { <PERSON><PERSON>, Card, Result, Skeleton } from "antd";
import { useParams, useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";

const InterviewDetailPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const interviewId = params.id as string;

  const [interview, setInterview] = useState<Interview | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);

  const fetchInterviewDetails = async () => {
    setLoading(true);
    setError(null);

    try {
      if (!interviewId) {
        setError("Interview ID is required");
        setLoading(false);
        return;
      }

      const interviewData = await InterviewService.fetchInterviewById(
        interviewId
      );
      setInterview(interviewData);
    } catch (err) {
      console.error("Error fetching interview details:", err);
      setError("Failed to load interview details. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchInterviewDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [interviewId, refreshKey]);

  const handleRefresh = () => {
    setRefreshKey((prevKey) => prevKey + 1);
  };

  const handleGoBack = () => {
    router.push("/features/interviews");
  };

  return (
    <Card>
      {/* Main content */}
      {loading ? (
        <Card>
          <Skeleton active paragraph={{ rows: 10 }} />
        </Card>
      ) : error ? (
        <Result
          status="error"
          title="Error"
          subTitle={error}
          extra={[
            <Button type="primary" key="retry" onClick={handleRefresh}>
              Try Again
            </Button>,
            <Button key="back" onClick={handleGoBack}>
              Go Back
            </Button>,
          ]}
        />
      ) : interview ? (
        <InterviewDetail interview={interview} onRefresh={handleRefresh} />
      ) : (
        <Result
          status="404"
          title="Interview Not Found"
          subTitle="The interview you're looking for doesn't exist or has been removed."
          extra={
            <Button type="primary" onClick={handleGoBack}>
              Back to Interview List
            </Button>
          }
        />
      )}
    </Card>
  );
};

export default InterviewDetailPage;
