// app/(main)/admin/interviews/[id]/feedback/page.tsx
"use client";
import React, { useEffect, useState } from "react";
import {
  Card,
  Button,
  Skeleton,
  Result,
  Typography,
  Breadcrumb,
  Flex,
} from "antd";
import {
  ArrowLeftOutlined,
  HomeOutlined,
  BulbOutlined,
} from "@ant-design/icons";
import { useRouter, useParams } from "next/navigation";
import { Interview } from "@/types/interview";
import InterviewService from "@/services/interviewService";
import InterviewFeedbackForm from "@/components/features/interviews/InterviewFeedbackForm";
import Link from "next/link";

const { Title, Text } = Typography;

const InterviewFeedbackPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const interviewId = params.id as string;

  const [interview, setInterview] = useState<Interview | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchInterviewDetails = async () => {
      setLoading(true);
      setError(null);

      try {
        if (!interviewId) {
          setError("Interview ID is required");
          setLoading(false);
          return;
        }

        const interviewData = await InterviewService.fetchInterviewById(
          interviewId
        );
        setInterview(interviewData);
      } catch (err) {
        console.error("Error fetching interview details:", err);
        setError("Failed to load interview details. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchInterviewDetails();
  }, [interviewId]);

  const handleGoBack = () => {
    router.push(`/admin/interviews/${interviewId}`);
  };

  const handleSuccess = () => {
    router.push(`/admin/interviews/${interviewId}?feedback=submitted`);
  };

  return (
    <div className="p-6">
      {/* Breadcrumb navigation */}
      <Breadcrumb
        className="mb-4"
        items={[
          {
            title: (
              <Link href="/admin/dashboard">
                <HomeOutlined /> Dashboard
              </Link>
            ),
          },
          {
            title: <Link href="/admin/interviews">Interviews</Link>,
          },
          {
            title: (
              <Link href={`/admin/interviews/${interviewId}`}>
                {interviewId}
              </Link>
            ),
          },
          {
            title: "Feedback",
          },
        ]}
      />

      {/* Back button and title */}
      <Flex justify="space-between" align="center" className="mb-4">
        <Button icon={<ArrowLeftOutlined />} onClick={handleGoBack}>
          Back to Interview
        </Button>
      </Flex>

      <Title level={3} className="mb-4">
        <BulbOutlined /> Submit Interview Feedback
      </Title>

      {/* Main content */}
      {loading ? (
        <Card>
          <Skeleton active paragraph={{ rows: 10 }} />
        </Card>
      ) : error ? (
        <Result
          status="error"
          title="Error"
          subTitle={error}
          extra={[
            <Button
              type="primary"
              key="retry"
              onClick={() => window.location.reload()}
            >
              Try Again
            </Button>,
            <Button key="back" onClick={handleGoBack}>
              Go Back
            </Button>,
          ]}
        />
      ) : interview ? (
        interview.feedbackProvided ? (
          <Result
            status="warning"
            title="Feedback Already Provided"
            subTitle="This interview already has feedback. You can view it on the interview details page."
            extra={
              <Button type="primary" onClick={handleGoBack}>
                Back to Interview Details
              </Button>
            }
          />
        ) : (
          <>
            <Card className="mb-4">
              <Flex align="center" gap="middle">
                <BulbOutlined style={{ fontSize: 24, color: "#faad14" }} />
                <div>
                  <Title level={5} className="m-0">
                    Submitting Feedback for Interview
                  </Title>
                  <Text type="secondary">
                    For {interview.candidateName} regarding the{" "}
                    {interview.jobPostTitle} position
                  </Text>
                </div>
              </Flex>
            </Card>

            <InterviewFeedbackForm
              interviewId={interviewId}
              onSuccess={handleSuccess}
              onCancel={handleGoBack}
            />
          </>
        )
      ) : (
        <Result
          status="404"
          title="Interview Not Found"
          subTitle="The interview you're looking for doesn't exist or has been removed."
          extra={
            <Button
              type="primary"
              onClick={() => router.push("/admin/interviews")}
            >
              Back to Interview List
            </Button>
          }
        />
      )}
    </div>
  );
};

export default InterviewFeedbackPage;
