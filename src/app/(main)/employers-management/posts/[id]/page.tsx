"use client";
import AnalyticsTab from "@/components/features/posts/AnalyticsTab";
import ApplicationsTab from "@/components/features/posts/ApplicationsTab";
import PostDetailsTab from "@/components/features/posts/PostDetailsTab";
import PostStatusCard from "@/components/features/posts/StatusCard";
import PostService from "@/services/postService";
import type { JobApplication } from "@/types/application";
import type { Post, PostStatus } from "@/types/post";
import {
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import {
  Alert,
  Button,
  Card,
  Flex,
  Popconfirm,
  Skeleton,
  Space,
  Tabs,
  Typography,
  message,
} from "antd";
import { useParams, useRouter } from "next/navigation";
import React, { useEffect, useState, useContext } from "react";
import { AuthContext } from "@/contexts/AuthContext";
import { PermissionEnum } from "@/constants/_permissionEnum";

const { Title } = Typography;

const PostDetailPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const postId = params.id as string;
  const authContext = useContext(AuthContext);
  const access = authContext?.access;

  const [post, setPost] = useState<Post>({} as Post);
  const [applications, setApplications] = useState<JobApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPostDetails = async () => {
      try {
        setLoading(true);
        const postData = await PostService.getDetail(postId);
        setPost(postData.data);
        setError(null);
      } catch (err) {
        console.error("Error fetching post details:", err);
        setError("Failed to load post details. Please try again.");
      } finally {
        setLoading(false);
      }
    };
    fetchPostDetails();
  }, [postId]);

  const items = [
    {
      key: "details",
      label: "Post Details",
      children: <PostDetailsTab post={post} />,
    },
    {
      key: "applications",
      label: (
        <span>
          <UserOutlined /> Applications ({applications.length})
        </span>
      ),
      children: <ApplicationsTab postId={+post.id} />,
    },
    {
      key: "analytics",
      label: (
        <span>
          <EyeOutlined /> Analytics
        </span>
      ),
      children: <AnalyticsTab post={post} />,
    },
  ];

  const handleEdit = () => {
    router.push(`/employers-management/posts/edit/${postId}`);
  };

  const handleDelete = async () => {
    try {
      await PostService.delete(postId);
      message.success("Post deleted successfully");
      router.push("/employers-management/posts");
    } catch (err) {
      console.error("Error deleting post:", err);
      message.error("Failed to delete post");
    }
  };

  const handleStatusChange = async (newStatus: PostStatus) => {
    try {
      if (!post) return;

      await PostService.update(postId, { status: newStatus });
      setPost({ ...post, status: newStatus });
      message.success(`Post status updated to ${newStatus}`);
    } catch (err) {
      console.error("Error updating post status:", err);
      message.error("Failed to update post status");
    }
  };

  if (!access?.viewPost) {
    return <Alert message="No permission" type="error" />;
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton active paragraph={{ rows: 1 }} />
        <Skeleton.Button
          active
          size="large"
          shape="square"
          style={{ width: 200, marginBottom: 16 }}
        />
        <Skeleton active paragraph={{ rows: 6 }} />
      </div>
    );
  }

  if (error) {
    return <Alert message="Error" description={error} type="error" />;
  }

  if (!post) {
    return <Alert message="Post not found" type="warning" />;
  }

  return (
    <Card className="space-y-6">
      <Flex justify="space-between" align="center">
        <Title level={3}>Post Detail</Title>
        <Space>
          {access[PermissionEnum.POST_UPDATE] && (
            <Button icon={<EditOutlined />} onClick={handleEdit}>
              Edit
            </Button>
          )}
          {access[PermissionEnum.POST_DELETE] && (
            <Popconfirm
              title="Delete this post?"
              description="Are you sure you want to delete this post? This action cannot be undone."
              onConfirm={handleDelete}
              okText="Yes"
              cancelText="No"
              icon={<ExclamationCircleOutlined style={{ color: "red" }} />}
            >
              <Button danger icon={<DeleteOutlined />}>
                Delete
              </Button>
            </Popconfirm>
          )}
        </Space>
      </Flex>

      {/* Status and Actions */}
      <PostStatusCard post={post} onStatusChange={handleStatusChange} />

      <Tabs defaultActiveKey="details" items={items} />
    </Card>
  );
};

export default PostDetailPage;
