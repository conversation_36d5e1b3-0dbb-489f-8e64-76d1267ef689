"use client";

import { useContext } from "react";
import { AuthContext } from "@/contexts/AuthContext";
import { PermissionEnum } from "@/constants/_permissionEnum";
import PostFormPageSingleLayout from "@/components/features/posts/PostFormPageSingleLayout";

export default function NewPostPageWrapper() {
  const authContext = useContext(AuthContext);
  const access = authContext?.access;
  if (!access?.[PermissionEnum.POST_CREATE]) {
    return <div>Bạn không có quyền tạo bài đăng mới.</div>;
  }
  return <PostFormPageSingleLayout isEdit={false} />;
}
