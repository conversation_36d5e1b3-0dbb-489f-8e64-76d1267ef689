"use client";

import { useContext } from "react";
import { AuthContext } from "@/contexts/AuthContext";
import { PermissionEnum } from "@/constants/_permissionEnum";
import PostFormPage from "@/components/features/posts/PostFormPage";

export default function NewPostPageWrapper() {
  const authContext = useContext(AuthContext);
  const access = authContext?.access;
  if (!access?.[PermissionEnum.POST_CREATE]) {
    return <div>Bạn không có quyền tạo bài đăng mới.</div>;
  }
  return <PostFormPage isEdit={false} />;
}
