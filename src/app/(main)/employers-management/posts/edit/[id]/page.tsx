"use client";
import { useContext } from "react";
import { AuthContext } from "@/contexts/AuthContext";
import { PermissionEnum } from "@/constants/_permissionEnum";
import PostFormPage from "@/components/features/posts/PostFormPage";
import { useTranslations } from "next-intl";
import { Alert } from "antd";

export default function EditPostPageWrapper() {
  const authContext = useContext(AuthContext);
  const access = authContext?.access;
  const tCommon = useTranslations("common");

  if (!access?.[PermissionEnum.POST_UPDATE]) {
    return <Alert message={tCommon("status.no_permission")} type="error" />;
  }
  return <PostFormPage isEdit={true} />;
}
