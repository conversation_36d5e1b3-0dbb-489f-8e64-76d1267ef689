"use client";
import { useEffect, useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import {
  Form,
  Input,
  Button,
  DatePicker,
  Select,
  message,
  Card,
  Spin,
} from "antd";
import { useTranslations } from "next-intl";
import UserService from "@/services/userService";
import AuthService from "@/services/authService";
import AddressForm<PERSON>ield from "@/components/features/address/AddressFormField";
import { useNotification } from "@/contexts/NotiContext";
import dayjs from "dayjs";

const genderOptions = [
  { value: "male", label: "gender_male" },
  { value: "female", label: "gender_female" },
  { value: "other", label: "gender_other" },
];

const EditProfilePage = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [userDetail, setUserDetail] = useState<any>(null);
  const [form] = Form.useForm();
  const router = useRouter();
  const tProfile = useTranslations("profile");
  const tUser = useTranslations("user");
  const notification = useNotification();

  useEffect(() => {
    const fetchUserDetail = async () => {
      try {
        // Lấy user từ localStorage
        const localUser =
          typeof window !== "undefined" ? localStorage.getItem("user") : null;
        if (!localUser) return;

        const parsedUser = JSON.parse(localUser);
        const userId = parsedUser.id;

        if (!userId) return;

        // Call API để lấy thông tin chi tiết user
        const response = await UserService.getDetail(userId);
        setUserDetail(response.data as any);

        // Fill form với data từ API
        const userData = response.data as any;
        form.setFieldsValue({
          name: userData.name || "",
          phoneNumber: userData.phoneNumber || "",
          dateOfBirth: userData.dateOfBirth
            ? dayjs(userData.dateOfBirth)
            : null,
          gender: userData.gender || undefined,
          // Convert UserAddress to AddressFormData format
          address: userData.address
            ? {
                provinceCode: userData.address.provinceCode || null,
                districtCode: userData.address.districtCode || null,
                wardCode: userData.address.wardCode || null,
                detailAddress: userData.address.detailAddress || "",
              }
            : {
                provinceCode: null,
                districtCode: null,
                wardCode: null,
                detailAddress: "",
              },
        });
      } catch (error) {
        console.error("Error fetching user detail:", error);
        message.error(tProfile("load_error"));
      } finally {
        setFetchLoading(false);
      }
    };

    fetchUserDetail();
  }, [form, tProfile]);

  const onFinish = async (values: any) => {
    if (!userDetail) return;

    setLoading(true);
    try {
      // Convert to the exact payload structure requested
      const updateProfilePayload = {
        name: values.name,
        phoneNumber: values.phoneNumber,
        dateOfBirth: values.dateOfBirth
          ? values.dateOfBirth.format("YYYY-MM-DD")
          : "",
        gender: values.gender,
        address: {
          provinceCode: values.address.provinceCode,
          districtCode: values.address.districtCode,
          wardCode: values.address.wardCode,
          detailAddress: values.address.detailAddress,
        },
      };

      // Call new API endpoint
      await AuthService.updateProfile(updateProfilePayload);
      notification.notifySuccess(tProfile("update_success"));
      router.push("/profile");
    } catch (error) {
      message.error(tProfile("update_error"));
    } finally {
      setLoading(false);
    }
  };

  if (fetchLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="max-w-xl mx-auto py-8">
      <Card title={tProfile("edit_title")}>
        <Form form={form} layout="vertical" onFinish={onFinish}>
          <Form.Item
            name="name"
            label={tProfile("name")}
            rules={[
              {
                required: true,
                message: tProfile("name") + " " + tProfile("password_required"),
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item name="phoneNumber" label={tProfile("phone_number")}>
            <Input />
          </Form.Item>
          <Form.Item name="dateOfBirth" label={tProfile("dob")}>
            <DatePicker style={{ width: "100%" }} />
          </Form.Item>
          <Form.Item name="gender" label={tProfile("gender")}>
            <Select
              options={genderOptions.map((opt) => ({
                value: opt.value,
                label: tProfile(opt.label),
              }))}
            />
          </Form.Item>
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {tProfile("address")}
            </label>
            <AddressFormField name="address" required={false} />
          </div>
          <Form.Item>
            <div className="flex justify-end gap-2">
              <Button onClick={() => router.push("/profile")}>
                {tProfile("cancel")}
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {tProfile("save")}
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default EditProfilePage;
