"use client";

import ContractDetails from "@/components/features/contracts/ContractDetails";
import { Card } from "antd";
import { useParams } from "next/navigation";
import { UserRole } from "@/constants/userRole";

export default function ContractDetailsPage() {
  const { id } = useParams();

  return (
    <Card>
      <ContractDetails
        contractId={id as string}
        userRole={UserRole.ADMIN}
        userId={""}
      />
    </Card>
  );
}
