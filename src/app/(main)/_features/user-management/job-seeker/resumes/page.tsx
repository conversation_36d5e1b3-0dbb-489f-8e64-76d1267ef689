"use client";
import ResumeTable from "@/components/features/users/job-seeker-resumes/ResumeTable";
import ResumeFormModal from "@/components/features/users/job-seeker-resumes/ResumeFormModal";
import {
  fetchAllResumes,
  saveResume,
  deleteResume,
} from "@/services/resumeService";
import { Card, Typography, message } from "antd";
import { useTranslations } from "next-intl";
import { useContext, useState } from "react";
import { AuthContext } from "@/contexts/AuthContext";
import { Resume } from "@/types/resume";
import { ExtendedUser } from "@/types/user";
import { PermissionEnum } from "@/constants/_permissionEnum";

const ResumeManagementPage = () => {
  const t = useTranslations("route");
  const tCommon = useTranslations("common");
  const authContext = useContext(AuthContext);
  const access = authContext?.access;

  const [modalOpen, setModalOpen] = useState(false);
  const [editingResume, setEditingResume] = useState<Resume | null>(null);
  const [userInfo, setUserInfo] = useState<ExtendedUser | null>(null);

  if (!access?.viewUser) {
    return (
      <Card>
        <Typography.Text type="danger">
          {tCommon("status.no_permission")}
        </Typography.Text>
      </Card>
    );
  }

  // Fake userInfo for ResumeFormModal (id, name, email)
  const getUserInfo = (resume?: Resume): ExtendedUser => ({
    id: resume?.userId || "",
    ulid: "",
    token: "",
    refreshToken: "",
    name: "",
    phoneNumber: "",
    email: "",
    profilePicture: "",
    role: "",
    isEmailVerified: false,
    isPhoneVerified: false,
    createdAt: "",
    lastLogin: "",
  });

  const handleCreate = () => {
    setEditingResume(null);
    setUserInfo(getUserInfo());
    setModalOpen(true);
  };

  const handleEdit = (resume: Resume) => {
    setEditingResume(resume);
    setUserInfo(getUserInfo(resume));
    setModalOpen(true);
  };

  const handleDelete = async (resume: Resume) => {
    try {
      await deleteResume(resume.id);
      message.success("Deleted successfully");
    } catch (err) {
      message.error("Delete failed");
    }
  };

  return (
    <Card>
      <Typography.Title level={4}>{t("resumes")}</Typography.Title>
      <ResumeTable
        api={fetchAllResumes}
        onCreate={
          access?.[PermissionEnum.RESUME_CREATE] ? handleCreate : undefined
        }
        onEdit={access?.[PermissionEnum.RESUME_UPDATE] ? handleEdit : undefined}
        onDelete={
          access?.[PermissionEnum.RESUME_DELETE] ? handleDelete : undefined
        }
        access={access}
        onView={() => {}}
      />
      {modalOpen && userInfo && (
        <ResumeFormModal
          currentResume={editingResume}
          isFormModalVisible={modalOpen}
          setIsFormModalVisible={setModalOpen}
          userInfo={userInfo}
        />
      )}
    </Card>
  );
};

export default ResumeManagementPage;
