"use client";

import MainBreadcrumb from "@/components/layout/breadcrum";
import MainFooter from "@/components/layout/footer";
import MainHeader from "@/components/layout/header";
import Sidebar from "@/components/layout/sidebar";
import { Layout } from "antd";
import React from "react";
import ProtectedRoute from "@/routes/ProtectedRoute";

const { Content } = Layout;

const RootLayout = ({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) => {
  return (
    <ProtectedRoute>
      <Layout className="h-screen overflow-hidden">
        <Sidebar />
        <Layout>
          <MainHeader />
          <Content className="mx-4 overflow-auto h-[calc(100vh-134px)] pb-5">
            <MainBreadcrumb />
            {children}
          </Content>
          <MainFooter />
        </Layout>
      </Layout>
    </ProtectedRoute>
  );
};

export default RootLayout;
