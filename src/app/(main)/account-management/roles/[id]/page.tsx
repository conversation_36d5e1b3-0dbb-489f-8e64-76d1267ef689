/* eslint-disable react-hooks/exhaustive-deps */
"use client";
import RoleFormModal from "@/components/features/roles/RoleFormModal";
import BasePageTitle from "@/components/ui/common/BasePageTitle";
import BaseTable from "@/components/ui/tables/BaseTable";
import { PermissionEnum } from "@/constants/_permissionEnum";
import { AuthContext } from "@/contexts/AuthContext";
import { useNotification } from "@/contexts/NotiContext";
import RoleService from "@/services/roleService";
import { Role, RoleUser } from "@/types/role";
import { Button, Card, Col, Empty, Row, Space, Typography } from "antd";
import { useTranslations } from "next-intl";
import { useParams, useRouter } from "next/navigation";
import { useContext, useEffect, useState } from "react";

const { Text, Title } = Typography;

export default function RoleDetail() {
  const params = useParams();
  const router = useRouter();
  const tUser = useTranslations("user");
  const tCommon = useTranslations("common");
  const [role, setRole] = useState<Role | null>(null);
  const [loading, setLoading] = useState(true);
  const [formModalVisible, setFormModalVisible] = useState(false);

  const authContext = useContext(AuthContext);
  const access = authContext?.access;
  const notification = useNotification();

  const id = params?.id || "";

  useEffect(() => {
    fetchRoleData();
  }, [id]);

  const fetchRoleData = async () => {
    setLoading(true);
    try {
      const roles = await RoleService.getDetail(id as string);

      setRole(roles.data);
    } catch (error) {
      console.log("🚀 ~ fetchRoleData ~ error:", error);
      notification.notifyError("Failed to load role details.");
    } finally {
      setLoading(false);
    }
  };

  const handleEditRole = () => {
    setFormModalVisible(true);
  };

  const handleFormSuccess = () => {
    fetchRoleData(); // Refresh role data
  };

  const columns = [
    { title: tCommon("fields.id"), dataIndex: "id" },
    { title: tCommon("fields.name"), dataIndex: "name" },
    { title: tCommon("fields.phone"), dataIndex: "phoneNumber" },
    { title: tCommon("fields.email"), dataIndex: "email" },
    {
      title: tUser("email_verified"),
      dataIndex: "isEmailVerified",
      render: (value: boolean) => (value ? "✔️" : "❌"),
    },
    {
      title: tUser("phone_verified"),
      dataIndex: "isPhoneVerified",
      render: (value: boolean) => (value ? "✔️" : "❌"),
    },
  ];

  const permissionsByModule: Record<string, string[]> = {};

  role?.permissions.forEach((permission) => {
    const [action, module] = permission.name.split("_");
    if (!permissionsByModule[module]) {
      permissionsByModule[module] = [];
    }
    if (!permissionsByModule[module].includes(action)) {
      permissionsByModule[module].push(action);
    }
  });

  return (
    <div>
      <div className="flex justify-between items-center mb-2">
        <BasePageTitle title="Role Details" />
      </div>

      {loading && !role ? (
        <div className="text-center py-8">Loading role details...</div>
      ) : !role ? (
        <Empty description="Role not found" />
      ) : (
        <Row gutter={16}>
          {/* Role Details Card */}
          <Col span={6}>
            <Card className="mb-6">
              <Title level={4} className="mb-4">
                {role.name}
              </Title>

              <Space direction="vertical" size="large" className="w-full">
                {/* Permissions List */}
                <div>
                  {Object.entries(permissionsByModule).map(
                    ([module, actions]) => (
                      <div key={module} className="mb-4">
                        <Title level={5} className="mb-2">
                          {module.toUpperCase()}
                        </Title>
                        <Space size="small" wrap>
                          {actions.map((action) => (
                            <Text key={action}>• {action.toUpperCase()}</Text>
                          ))}
                        </Space>
                      </div>
                    )
                  )}
                </div>

                {/* Actions */}
                {access && access[PermissionEnum.ROLE_UPDATE] && (
                  <div className="flex gap-4 mt-2">
                    <Button type="primary" onClick={handleEditRole}>
                      Edit Role
                    </Button>
                  </div>
                )}
              </Space>
            </Card>
          </Col>

          {/* Users Assigned to Role */}
          <Col span={18}>
            <Card title="User Assigned">
              <BaseTable<RoleUser>
                api={(params) => RoleService.getRoleUsers(role!.id, params)}
                columns={columns}
                rowKey="id"
                initialParams={{ page: 1, pageSize: 10 }}
                onRowClick={(record) => {
                  router.push(`/account-management/users/${record.id}`);
                }}
                showSearch
                disabledRowCheckbox
              />
            </Card>
          </Col>
        </Row>
      )}

      {role && (
        <RoleFormModal
          isVisible={formModalVisible}
          onClose={() => setFormModalVisible(false)}
          selectedRole={role}
          onSuccess={handleFormSuccess}
        />
      )}
    </div>
  );
}
