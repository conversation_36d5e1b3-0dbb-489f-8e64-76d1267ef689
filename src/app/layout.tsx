// app/layout.tsx
import "@ant-design/v5-patch-for-react-19";
import { LoadingBar } from "@/components/ui/loadings/LoadingBar";
import { NavigationEvents } from "@/routes/NavigationEvents";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { AntdThemeProvider } from "./theme-provider";
import { NextIntlClientProvider } from "next-intl";
import { getLocale } from "next-intl/server";
import { LanguageProvider } from "@/contexts/LanguageContext";
import "./globals.css";
import { NotificationProvider } from "@/contexts/NotiContext";
import { AuthProvider } from "@/contexts/AuthContext";

export const metadata = {
  title: "Flexin",
  icons: {
    icon: "/favicon.svg",
  },
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const locale = await getLocale();

  return (
    <html lang={locale}>
      <body>
        <LoadingBar />
        <NavigationEvents />
        <LanguageProvider>
          <ThemeProvider>
            <AntdThemeProvider>
              <NotificationProvider>
                <AuthProvider>
                  <NextIntlClientProvider>{children}</NextIntlClientProvider>
                </AuthProvider>
              </NotificationProvider>
            </AntdThemeProvider>
          </ThemeProvider>
        </LanguageProvider>
      </body>
    </html>
  );
}
