export enum ContractType {
  LONG_TERM = "LONG_TERM",
  SHORT_TERM = "SHORT_TERM",
  INTERNSHIP = "INTERNSHIP",
  SEASON = "SEASON",
}

export interface ContractTypeInfo {
  value: ContractType;
  label: string;
  description?: string;
}

export const CONTRACT_TYPE_OPTIONS: ContractTypeInfo[] = [
  {
    value: ContractType.LONG_TERM,
    label: "Long Term",
    description: "Long-term employment contract",
  },
  {
    value: ContractType.SHORT_TERM,
    label: "Short Term",
    description: "Short-term employment contract",
  },
  {
    value: ContractType.INTERNSHIP,
    label: "Internship",
    description: "Internship program",
  },
  {
    value: ContractType.SEASON,
    label: "Seasonal",
    description: "Seasonal employment",
  },
];

// Helper function to get contract type info by value
export const getContractTypeInfo = (value: ContractType): ContractTypeInfo | undefined => {
  return CONTRACT_TYPE_OPTIONS.find(option => option.value === value);
};

// Helper function to get label by value
export const getContractTypeLabel = (value: ContractType): string => {
  const info = getContractTypeInfo(value);
  return info ? info.label : value;
};

// Helper function to get options for Select component
export const getContractTypeSelectOptions = () => {
  return CONTRACT_TYPE_OPTIONS.map(option => ({
    value: option.value,
    label: option.label,
  }));
};
