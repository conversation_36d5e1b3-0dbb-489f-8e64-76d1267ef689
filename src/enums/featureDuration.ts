export enum FeatureDuration {
  THREE_DAYS = "THREE_DAYS",
  FIVE_DAYS = "FIVE_DAYS",
  SEVEN_DAYS = "SEVEN_DAYS",
  TEN_DAYS = "TEN_DAYS",
}

export interface FeatureDurationInfo {
  value: FeatureDuration;
  day: number;
  costInPoints: number;
  label: string;
}

export const FEATURE_DURATION_OPTIONS: FeatureDurationInfo[] = [
  {
    value: FeatureDuration.THREE_DAYS,
    day: 3,
    costInPoints: 50,
    label: "3 days",
  },
  {
    value: FeatureDuration.FIVE_DAYS,
    day: 5,
    costInPoints: 80,
    label: "5 days",
  },
  {
    value: FeatureDuration.SEVEN_DAYS,
    day: 7,
    costInPoints: 100,
    label: "7 days",
  },
  {
    value: FeatureDuration.TEN_DAYS,
    day: 10,
    costInPoints: 150,
    label: "10 days",
  },
];

// Helper function to get duration info by value
export const getFeatureDurationInfo = (
  value: FeatureDuration
): FeatureDurationInfo | undefined => {
  return FEATURE_DURATION_OPTIONS.find((option) => option.value === value);
};

// Helper function to get label by value
export const getFeatureDurationLabel = (value: FeatureDuration): string => {
  const info = getFeatureDurationInfo(value);
  return info ? info.label : value;
};

// Helper function to get options for Select component
export const getFeatureDurationSelectOptions = () => {
  return FEATURE_DURATION_OPTIONS.map((option) => ({
    value: option.value,
    label: `${option.label} (${option.costInPoints} points)`,
  }));
};
