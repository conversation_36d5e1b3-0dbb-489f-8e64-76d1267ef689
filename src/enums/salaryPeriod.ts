export enum SalaryPeriod {
  HOUR = "HOUR",
  DAY = "DAY",
  MONTH = "MONTH",
  SHIFT = "SHIFT",
  WEEK = "WEEK",
}

export interface SalaryPeriodInfo {
  value: SalaryPeriod;
  label: string;
  shortLabel?: string;
}

export const SALARY_PERIOD_OPTIONS: SalaryPeriodInfo[] = [
  {
    value: SalaryPeriod.HOUR,
    label: "Per Hour",
    shortLabel: "/hour",
  },
  {
    value: SalaryPeriod.DAY,
    label: "Per Day",
    shortLabel: "/day",
  },
  {
    value: SalaryPeriod.WEEK,
    label: "Per Week",
    shortLabel: "/week",
  },
  {
    value: SalaryPeriod.MONTH,
    label: "Per Month",
    shortLabel: "/month",
  },
  {
    value: SalaryPeriod.SHIFT,
    label: "Per Shift",
    shortLabel: "/shift",
  },
];

// Helper function to get salary period info by value
export const getSalaryPeriodInfo = (value: SalaryPeriod): SalaryPeriodInfo | undefined => {
  return SALARY_PERIOD_OPTIONS.find(option => option.value === value);
};

// Helper function to get label by value
export const getSalaryPeriodLabel = (value: SalaryPeriod): string => {
  const info = getSalaryPeriodInfo(value);
  return info ? info.label : value;
};

// Helper function to get short label by value (for display in tables)
export const getSalaryPeriodShortLabel = (value: SalaryPeriod): string => {
  const info = getSalaryPeriodInfo(value);
  return info ? (info.shortLabel || info.label) : value;
};

// Helper function to get options for Select component
export const getSalaryPeriodSelectOptions = () => {
  return SALARY_PERIOD_OPTIONS.map(option => ({
    value: option.value,
    label: option.label,
  }));
};
