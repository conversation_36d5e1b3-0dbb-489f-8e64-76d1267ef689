// types/post.ts
import { FeatureDuration } from "@/enums/featureDuration";
import { ContractType } from "@/enums/contractType";
import { SalaryPeriod } from "@/enums/salaryPeriod";

export interface Post {
  id: string;
  title: string;
  description: string;
  location: string;
  companyName: string;
  branchId: number;
  salary: {
    min: number;
    max: number;
    currency: string;
    period: SalaryPeriod;
  };
  status: string;
  postDate: string;
  expireDate: string;
  applicationCount: number;
  newApplications: number;
  viewCount: number;
  jobType: JobType;
  workType: WorkType;
  experience: {
    min: number;
    max: number;
  };
  positions: string;
  skills: Array<string>;
  benefits: Array<string>;
  requiredDocuments: Array<{
    name: string;
    required: boolean;
  }>;
  rejectionReason: string;
  experienceLevel: ExperienceLevel;
  workingInformation: Array<{
    workHours: string;
    workDays: string;
  }>;
  isFeatureJob: boolean;
  featuredDuration?: FeatureDuration;
  urgentHiring: boolean;
}

export type PostStatus = "Đang hiển thị" | "Bản nháp" | "Đã đóng" | "Hết hạn";
export type WorkType = "Tại chỗ" | "Từ xa" | "Kết hợp";
export type JobType = "Bán thời gian" | "Toàn thời gian";
export type ExperienceLevel =
  | "Không yêu cầu"
  | "Dưới 1 năm"
  | "1-2 năm"
  | "2-5 năm"
  | "Trên 5 năm";

export interface RequiredDocument {
  name: string;
  required: boolean;
}

export interface PostFilters {
  search?: string;
  status?: PostStatus;
  workType?: WorkType;
  jobType?: JobType;
  experienceLevel?: ExperienceLevel;
  isFeatured?: boolean;
  urgentHiring?: boolean;
  dateRange?: [string, string];
  page?: number;
  pageSize?: number;
}

export interface PostStatistics {
  total: number;
  active: number;
  expired: number;
  draft: number;
  closed: number;
  totalApplications: number;
  newApplications: number;
}

export enum JobSeekerPostStatus {
  DRAFT = "draft",
  ACTIVE = "active",
  EXPIRED = "expired",
  PAUSED = "paused",
  CLOSED = "closed",
  REOPEN = "reopen",
  REJECTED = "rejected",
  PENDING = "pending",
}

export enum JobSeekerPostWorkingDay {
  MONDAY = "Monday",
  TUESDAY = "Tuesday",
  WEDNESDAY = "Wednesday",
  THURSDAY = "Thursday",
  FRIDAY = "Friday",
  SATURDAY = "Saturday",
  SUNDAY = "Sunday",
}

export enum JobSeekerPostWorkingShift {
  MORNING = "Morning",
  AFTERNOON = "Afternoon",
  EVENING = "Evening",
}

export interface JobSeekerPost {
  id: number;
  title: string;
  industry: string;
  description: string;
  jobSeekerId: number;
  salary: {
    id: number;
    min: number;
    max: number | null;
    currency: string;
    period: SalaryPeriod;
  };
  skills: string[];
  languages: string[];
  educationLevel: string;
  educationDetail: string;
  startTime: string;
  endTime: string;
  workingHourPerDay: number;
  workingDays: JobSeekerPostWorkingDay[];
  workingShifts: JobSeekerPostWorkingShift[];
  experiences: Array<{
    id: number;
    industry: string;
    yearOfExperience: number;
  }>;
  jobType: string;
  contractType: ContractType;
  workType: string;
  location: {
    id: number;
    provinceCode: number;
    districtCode: number;
    wardCode: number;
    detailAddress: string | null;
    provinceName: string;
    districtName: string;
    wardName: string;
  };
  opportunityCount: number;
  newOpportunityCount: number;
  viewCount: number;
  featureDuration?: FeatureDuration;
  interviewRequestCount: number;
  activeDate: string | null;
  status: JobSeekerPostStatus;
  isFeatureJob: boolean;
  isEnableEmailNotification: boolean;
  isEnableInformation: boolean;
  isAutoAcceptInterviewInvitation: boolean;
  isReady: boolean;
}
