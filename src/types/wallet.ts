import { ApiResponseAddress } from "./address";

export interface WalletUser {
  id: number;
  ulid: string;
  name: string;
  email: string;
  phoneNumber: string;
  profilePicture: string;
  address: ApiResponseAddress;
}

export interface BasicUserInfo {
  id: number;
  name: string;
  email?: string;
  phone?: string;
  photoUrl?: string;
}

export interface WalletStatistics {
  totalTransactions: number;
  totalPointsEarned: number;
  totalPointsSpent: number;
  totalPaymentRequests: number;
  totalAmountPaid: number;
  lastTransactionDate?: string;
  lastPaymentDate?: string;
  mostUsedTransactionType?: string;
  pendingPaymentRequests: number;
  successfulPaymentRequests: number;
  failedPaymentRequests: number;
}

export interface AdminWalletResponse {
  id: number;
  user: BasicUserInfo;
  point: number;
  createdAt: string;
  lastModifiedAt?: string;
  createdBy?: string;
  lastModifiedBy?: string;
  statistics?: WalletStatistics;
}

export interface Wallet {
  id: number;
  user: WalletUser;
  point: number;
}

export interface WalletListResponse {
  data: Wallet[];
  errors?: Array<{
    code: number;
    message: string;
  }>;
  message: string;
  statusCode: number;
  success: boolean;
}

export interface AdminWalletFilter {
  userId?: number;
  userName?: string;
  userEmail?: string;
  userRole?: string; // JOB_SEEKER, EMPLOYER
  minPoints?: number;
  maxPoints?: number;
  createdFrom?: string;
  createdTo?: string;
  lastModifiedFrom?: string;
  lastModifiedTo?: string;
  createdBy?: string[];
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: string;
}

export interface PersonalTransactionResponse {
  id: number;
  type: string;
  point: number;
  description?: string;
  createdAt: string;
  status?: string;
}

export interface PersonalTransactionFilter {
  type?: string;
  fromDate?: string;
  toDate?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: string;
}
