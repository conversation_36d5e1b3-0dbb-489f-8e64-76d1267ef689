// types/notifications.ts
export interface Notification {
  id: string;
  user_id: string;
  user_name: string;
  user_email: string;
  type: NotificationType;
  channel: NotificationChannel;
  title: string;
  content: string;
  status: NotificationStatus;
  sent_at: string;
  read_at?: string;
  clicked_at?: string;
  failed_reason?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export type NotificationType =
  | "welcome"
  | "verification"
  | "password_reset"
  | "job_match"
  | "application_status"
  | "interview_invitation"
  | "payment_confirmation"
  | "point_purchase"
  | "subscription_renewal"
  | "system_maintenance"
  | "policy_update";

export type NotificationChannel = "email" | "sms" | "push" | "in_app";

export type NotificationStatus =
  | "pending"
  | "sent"
  | "delivered"
  | "read"
  | "clicked"
  | "failed"
  | "cancelled";

export interface NotificationStats {
  total_sent: number;
  total_delivered: number;
  total_read: number;
  total_clicked: number;
  total_failed: number;
  delivery_rate: number;
  open_rate: number;
  click_rate: number;
}

export interface NotificationFilters {
  type?: NotificationType;
  channel?: NotificationChannel;
  status?: NotificationStatus;
  user_id?: string;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface CreateNotificationRequest {
  user_ids: string[];
  type: NotificationType;
  channel: NotificationChannel;
  title: string;
  content: string;
  scheduled_at?: string;
  metadata?: Record<string, any>;
}
