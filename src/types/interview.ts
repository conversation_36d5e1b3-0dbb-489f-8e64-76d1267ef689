// types/interview/types.ts

/**
 * Interview status enum - matching server values
 */
export enum InterviewStatus {
  SCHEDULED = "scheduled",
  IN_PROGRESS = "in_progress",
  COMPLETED = "completed",
  CANCELLED = "cancelled",
}

/**
 * Interview type enum
 */
export enum InterviewType {
  VIDEO_CALL = "Video Call",
  PHONE_CALL = "Phone Call",
  IN_PERSON = "In Person",
}

/**
 * Basic user info interface - matching server BasicUserInfoResponse
 */
export interface BasicUserInfo {
  id: number;
  name: string;
  email?: string;
  phone?: string;
  photoUrl?: string;
}

/**
 * Admin interview interface - matching server AdminInterviewResponse
 */
export interface Interview {
  id: number;
  employer: BasicUserInfo;
  jobSeeker: BasicUserInfo;
  scheduledTime: string;
  durationMinutes: number;
  status: string;
  meetingLink?: string;
  meetingId?: string;
  meetingCode?: string;
  meetingPassword?: string;
  notes?: string;
  reason?: string;
  startedAt?: string;
  endedAt?: string;
  cancellationReason?: string;
  feedback?: string;
  employerRating?: number;
  jobSeekerRating?: number;
  postId?: number;
  postTitle?: string;
  cancelFrom?: string;
  isActive?: boolean;
  createdAt: string;
  createdBy?: string;
  lastModifiedAt?: string;
  lastModifiedBy?: string;
  additionalData?: Record<string, any>;
}

/**
 * Create admin interview request - matching server CreateAdminInterviewRequest
 */
export interface CreateInterviewRequest {
  employerId: number;
  jobSeekerId: number;
  scheduledTime: string;
  durationMinutes?: number;
  status?: string;
  meetingLink?: string;
  meetingId?: string;
  meetingCode?: string;
  meetingPassword?: string;
  notes?: string;
  reason?: string;
  postId?: number;
  postTitle?: string;
  isActive?: boolean;
  additionalData?: Record<string, any>;
}

/**
 * Update admin interview request - matching server UpdateAdminInterviewRequest
 */
export interface UpdateInterviewRequest extends CreateInterviewRequest {
  id: number;
}

/**
 * Interview feedback interface
 */
export interface InterviewFeedback {
  rating?: number; // 1-5
  communication?: number; // 1-5
  technicalSkills?: number; // 1-5
  culturalFit?: number; // 1-5
  experience?: number; // 1-5
  strengths?: string[];
  weaknesses?: string[];
  notes?: string;
  decision?: "Offer" | "Consider" | "Reject";
  rejectionReason?: string;
  offerDetails?: {
    salary?: string;
    startDate?: string;
    notes?: string;
  };
  submittedBy?: string;
  submittedAt?: string;
}

/**
 * Interview statistics interface - matching server AdminInterviewResponse.InterviewStats
 */
export interface InterviewStats {
  totalInterviews: number;
  scheduledInterviews: number;
  inProgressInterviews: number;
  completedInterviews: number;
  cancelledInterviews: number;
  averageRating: number;
  todayInterviews: number;
  thisWeekInterviews: number;
  thisMonthInterviews: number;
}

/**
 * Interview filter interface - matching server InterviewFilter
 */
export interface InterviewFilter {
  status?: string[];
  type?: string;
  employerId?: number;
  jobSeekerId?: number;
  jobPostId?: number;
  startDateRange?: string;
  endDateRange?: string;
  search?: string;
  page?: number;
  size?: number;
  sortBy?: string;
  sortOrder?: string;
}

/**
 * Legacy interview form data interface for backward compatibility
 */
export interface InterviewFormData {
  employerId: number;
  jobSeekerId: number;
  scheduledTime: string;
  durationMinutes?: number;
  status?: string;
  meetingLink?: string;
  notes?: string;
  postId?: number;
}
