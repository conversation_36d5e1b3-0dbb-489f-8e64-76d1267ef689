// types/permission.ts
export interface Permission {
  id: string;
  name: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}

export interface PermissionFormData {
  name: string;
  description: string;
}

// Initial permission list for reference
export const INITIAL_PERMISSIONS = [
  {
    name: "view_user",
    description: "Can view user details",
  },
  {
    name: "create_user",
    description: "Can create new users",
  },
  {
    name: "update_user",
    description: "Can update user details",
  },
  {
    name: "delete_user",
    description: "Can delete users",
  },
  {
    name: "view_post",
    description: "Can view post details",
  },
  {
    name: "create_post",
    description: "Can create new posts",
  },
  {
    name: "update_post",
    description: "Can update post details",
  },
  {
    name: "delete_post",
    description: "Can delete posts",
  },
  {
    name: "view_transaction",
    description: "Can view transaction details",
  },
  {
    name: "manage_content",
    description: "Can manage site content",
  },
  {
    name: "view_reports",
    description: "Can view system reports",
  },
  {
    name: "configure_system",
    description: "Can configure system settings",
  },
];
