import { ReactNode } from "react";

export interface RouteBase {
  icon: ReactNode;
  label: string;
  access?: string;
}

export interface SimpleRoute extends RouteBase {
  path: string;
}

export interface KeyedRoute extends RouteBase {
  key: string;
  path: string;
}

export interface NestedRoute extends RouteBase {
  key: string;
  children: Record<string, Route>;
}

export type Route = SimpleRoute | KeyedRoute | NestedRoute;

export type Routes = Record<string, Route>;
