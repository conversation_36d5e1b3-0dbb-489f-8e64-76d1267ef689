// types/address.ts
export interface Province {
  name: string;
  code: number;
  divisionType: string;
  codename: string;
  phoneCode: number;
  districts?: District[];
}

export interface District {
  name: string;
  code: number;
  codename: string;
  divisionType: string;
  provinceCode: number;
  wards?: Ward[];
}

export interface Ward {
  name: string;
  code: number;
  codename: string;
  divisionType: string;
  districtCode: number;
}

export interface AddressFormData {
  provinceCode: number | null;
  districtCode: number | null;
  wardCode: number | null;
  detailAddress: string;
}

export interface AddressDisplayData extends AddressFormData {
  provinceName?: string;
  districtName?: string;
  wardName?: string;
}

export interface ApiResponseAddress {
  id: number;
  detailAddress: string;
  ward: Ward;
  district: District;
  province: Province;
}

export interface UserAddress {
  id: number;
  detailAddress: string;
  provinceCode: number;
  districtCode: number;
  wardCode: number;
  provinceName: string;
  districtName: string;
  wardName: string;
}
