// mocks/notifications.ts
import { Notification, NotificationStats } from "@/types/notifications";

export const mockNotifications: Notification[] = [
  {
    id: "notif_001",
    user_id: "user_001",
    user_name: "<PERSON>",
    user_email: "<EMAIL>",
    type: "welcome",
    channel: "email",
    title: "Welcome to Flexi!",
    content:
      "Welcome to Flexi! We're excited to have you join our job platform. Start exploring opportunities today.",
    status: "read",
    sent_at: "2025-01-20T10:00:00Z",
    read_at: "2025-01-20T10:15:00Z",
    clicked_at: "2025-01-20T10:20:00Z",
    created_at: "2025-01-20T09:55:00Z",
    updated_at: "2025-01-20T10:20:00Z",
    metadata: {
      campaign_id: "welcome_campaign_001",
      template_id: "welcome_template",
    },
  },
  {
    id: "notif_002",
    user_id: "user_002",
    user_name: "<PERSON>",
    user_email: "<EMAIL>",
    type: "job_match",
    channel: "push",
    title: "New Job Matches Available",
    content:
      "We found 3 new job opportunities that match your profile. Check them out now!",
    status: "delivered",
    sent_at: "2025-01-20T14:30:00Z",
    created_at: "2025-01-20T14:25:00Z",
    updated_at: "2025-01-20T14:30:00Z",
    metadata: {
      job_count: 3,
      matched_skills: ["JavaScript", "React", "Node.js"],
    },
  },
  {
    id: "notif_003",
    user_id: "user_003",
    user_name: "Mike Johnson",
    user_email: "<EMAIL>",
    type: "application_status",
    channel: "email",
    title: "Application Status Update",
    content:
      "Your application for Senior Developer position at TechCorp has been reviewed.",
    status: "failed",
    sent_at: "2025-01-20T16:00:00Z",
    failed_reason: "Invalid email address",
    created_at: "2025-01-20T15:55:00Z",
    updated_at: "2025-01-20T16:00:00Z",
    metadata: {
      job_id: "job_123",
      company_name: "TechCorp",
      application_id: "app_456",
    },
  },
  {
    id: "notif_004",
    user_id: "user_004",
    user_name: "Sarah Wilson",
    user_email: "<EMAIL>",
    type: "interview_invitation",
    channel: "sms",
    title: "Interview Invitation",
    content:
      "Congratulations! You have been invited for an interview tomorrow at 2 PM.",
    status: "read",
    sent_at: "2025-01-20T18:00:00Z",
    read_at: "2025-01-20T18:05:00Z",
    created_at: "2025-01-20T17:55:00Z",
    updated_at: "2025-01-20T18:05:00Z",
    metadata: {
      interview_date: "2025-01-21T14:00:00Z",
      interview_type: "video_call",
      meeting_link: "https://meet.example.com/interview-123",
    },
  },
  {
    id: "notif_005",
    user_id: "user_005",
    user_name: "David Brown",
    user_email: "<EMAIL>",
    type: "payment_confirmation",
    channel: "email",
    title: "Payment Confirmation",
    content:
      "Your payment of $29.99 for Premium subscription has been processed successfully.",
    status: "sent",
    sent_at: "2025-01-20T20:00:00Z",
    created_at: "2025-01-20T19:55:00Z",
    updated_at: "2025-01-20T20:00:00Z",
    metadata: {
      payment_amount: 29.99,
      payment_method: "credit_card",
      subscription_type: "premium",
    },
  },
  {
    id: "notif_006",
    user_id: "user_006",
    user_name: "Lisa Anderson",
    user_email: "<EMAIL>",
    type: "verification",
    channel: "email",
    title: "Verify Your Account",
    content:
      "Please verify your account by clicking the link we sent to your email address.",
    status: "pending",
    created_at: "2025-01-20T21:00:00Z",
    updated_at: "2025-01-20T21:00:00Z",
    metadata: {
      verification_token: "abc123def456",
      expires_at: "2025-01-21T21:00:00Z",
    },
    sent_at: "2025-01-20T22:00:00Z",
  },
  {
    id: "notif_007",
    user_id: "user_007",
    user_name: "Tom Wilson",
    user_email: "<EMAIL>",
    type: "system_maintenance",
    channel: "in_app",
    title: "Scheduled Maintenance Notice",
    content:
      "We will be performing scheduled maintenance tonight from 2 AM to 4 AM. The platform may be temporarily unavailable.",
    status: "clicked",
    sent_at: "2025-01-20T22:00:00Z",
    read_at: "2025-01-20T22:10:00Z",
    clicked_at: "2025-01-20T22:15:00Z",
    created_at: "2025-01-20T21:55:00Z",
    updated_at: "2025-01-20T22:15:00Z",
    metadata: {
      maintenance_start: "2025-01-21T02:00:00Z",
      maintenance_end: "2025-01-21T04:00:00Z",
      affected_services: ["job_search", "messaging"],
    },
  },
  {
    id: "notif_008",
    user_id: "user_008",
    user_name: "Emma Davis",
    user_email: "<EMAIL>",
    type: "point_purchase",
    channel: "push",
    title: "Points Purchase Confirmation",
    content:
      "500 points have been added to your account successfully. Use them to boost your job posts!",
    status: "delivered",
    sent_at: "2025-01-20T23:00:00Z",
    created_at: "2025-01-20T22:58:00Z",
    updated_at: "2025-01-20T23:00:00Z",
    metadata: {
      points_purchased: 500,
      points_cost: 9.99,
      new_balance: 750,
    },
  },
];

export const mockNotificationStats: NotificationStats = {
  total_sent: 1250,
  total_delivered: 1180,
  total_read: 890,
  total_clicked: 445,
  total_failed: 70,
  delivery_rate: 94.4,
  open_rate: 75.4,
  click_rate: 50.0,
};

export const mockAnalyticsData = {
  timeline: [
    {
      date: "2025-01-14",
      sent: 120,
      delivered: 115,
      read: 85,
      clicked: 42,
      failed: 5,
    },
    {
      date: "2025-01-15",
      sent: 135,
      delivered: 128,
      read: 95,
      clicked: 48,
      failed: 7,
    },
    {
      date: "2025-01-16",
      sent: 98,
      delivered: 92,
      read: 70,
      clicked: 35,
      failed: 6,
    },
    {
      date: "2025-01-17",
      sent: 142,
      delivered: 135,
      read: 102,
      clicked: 51,
      failed: 7,
    },
    {
      date: "2025-01-18",
      sent: 156,
      delivered: 148,
      read: 112,
      clicked: 56,
      failed: 8,
    },
    {
      date: "2025-01-19",
      sent: 178,
      delivered: 169,
      read: 128,
      clicked: 64,
      failed: 9,
    },
    {
      date: "2025-01-20",
      sent: 195,
      delivered: 185,
      read: 140,
      clicked: 70,
      failed: 10,
    },
  ],
  channels: [
    {
      channel: "Email",
      count: 650,
      percentage: 52.0,
    },
    {
      channel: "Push",
      count: 380,
      percentage: 30.4,
    },
    {
      channel: "SMS",
      count: 150,
      percentage: 12.0,
    },
    {
      channel: "In-App",
      count: 70,
      percentage: 5.6,
    },
  ],
};

// Mock API response format
export const mockNotificationListResponse = {
  data: mockNotifications,
  paginationMetadata: {
    totalItems: mockNotifications.length,
    currentPage: 1,
    totalPages: 1,
    itemsPerPage: 10,
  },
};

// Utility function to generate mock notification for testing
export const generateMockNotification = (
  overrides: Partial<Notification> = {}
): Notification => {
  const baseNotification: Notification = {
    id: `notif_${Date.now()}`,
    user_id: "user_test",
    user_name: "Test User",
    user_email: "<EMAIL>",
    type: "welcome",
    channel: "email",
    title: "Test Notification",
    content: "This is a test notification content.",
    status: "sent",
    sent_at: new Date().toISOString(),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    metadata: {},
  };

  return { ...baseNotification, ...overrides };
};
