// mockApplications.ts
import dayjs from "dayjs";
import { JobApplication, ApplicationStatus } from "@/types/application";

// Helper function to generate random IDs
const generateId = () => Math.random().toString(36).substring(2, 10);

// Helper to get random item from array
const getRandomItem = <T>(items: T[]): T =>
  items[Math.floor(Math.random() * items.length)];

// Helper to get random integer between min and max (inclusive)
const getRandomInt = (min: number, max: number): number =>
  Math.floor(Math.random() * (max - min + 1)) + min;

// Generate a set of mock job applications for a specific job
export const generateMockApplicationsForJob = (
  jobId: string,
  count: number = 10
): JobApplication[] => {
  const applications: JobApplication[] = [];

  const statuses: ApplicationStatus[] = [
    "Mới",
    "Đã xem",
    "Lịch phỏng vấn",
    "Đã phỏng vấn",
    "Đã mời làm việc",
    "Đ<PERSON> từ chối",
  ];
  const experiences = ["Dưới 1 năm", "1-2 năm", "2-5 năm", "Trên 5 năm"];
  const availabilities = ["Ngay lập tức", "Sau 2 tuần", "Sau 1 tháng"];
  const distances = ["2 km", "5 km", "10 km", "15 km", "20+ km"];
  const salaries = [
    "$600 - $800",
    "$800 - $1000",
    "$1000 - $1500",
    "$1500 - $2000",
    "$2000+",
  ];

  const allSkills = [
    "JavaScript",
    "TypeScript",
    "React",
    "Angular",
    "Vue",
    "Node.js",
    "Python",
    "Java",
    "C#",
    "PHP",
    "Ruby",
    "SQL",
    "NoSQL",
    "AWS",
    "Azure",
    "Docker",
    "Kubernetes",
    "Git",
    "CI/CD",
    "HTML/CSS",
    "Communication",
    "Team Leadership",
    "Project Management",
    "Agile",
    "Scrum",
    "UI/UX Design",
    "Mobile Development",
    "Data Analysis",
    "Machine Learning",
    "DevOps",
  ];

  // Names for mock candidates
  const firstNames = [
    "Nguyen",
    "Tran",
    "Le",
    "Pham",
    "Hoang",
    "Huynh",
    "Vu",
    "Dang",
    "Bui",
    "Do",
  ];
  const middleNames = [
    "Van",
    "Thi",
    "Duc",
    "Minh",
    "Quang",
    "Tuan",
    "Thanh",
    "Ngoc",
    "Hoai",
    "Hong",
  ];
  const lastNames = [
    "Anh",
    "Binh",
    "Cuong",
    "Dung",
    "Em",
    "Giang",
    "Hai",
    "Hung",
    "Linh",
    "Mai",
  ];

  for (let i = 0; i < count; i++) {
    const firstName = getRandomItem(firstNames);
    const middleName = getRandomItem(middleNames);
    const lastName = getRandomItem(lastNames);
    const fullName = `${firstName} ${middleName} ${lastName}`;

    // Generate random skills (3-5 skills per applicant)
    const skillCount = getRandomInt(3, 5);
    const skills: string[] = [];
    for (let j = 0; j < skillCount; j++) {
      const skill = getRandomItem(allSkills);
      if (!skills.includes(skill)) {
        skills.push(skill);
      }
    }

    // Generate random application date (within last 30 days)
    const daysAgo = getRandomInt(0, 30);
    const applicationDate = dayjs()
      .subtract(daysAgo, "day")
      .format("YYYY-MM-DD");

    // For candidates with interview scheduled, generate an interview date
    const status = getRandomItem(statuses);
    let interviewDate;
    if (status === "Lịch phỏng vấn") {
      interviewDate = dayjs()
        .add(getRandomInt(1, 10), "day")
        .format("YYYY-MM-DD");
    }

    // Random match score between 60 and 95
    const matchScore = getRandomInt(60, 95);

    // Generate notes for some applications
    let notes;
    if (getRandomInt(0, 1) === 1) {
      const noteOptions = [
        "Ứng viên có kinh nghiệm phù hợp với vị trí",
        "Cần phỏng vấn kỹ về các kỹ năng technical",
        "Ứng viên đang làm việc tại công ty cạnh tranh",
        "Có kinh nghiệm làm việc ở nước ngoài",
        "Ứng viên mong muốn làm việc từ xa 2 ngày/tuần",
      ];
      notes = getRandomItem(noteOptions);
    }

    applications.push({
      id: generateId(),
      jobId,
      candidateName: fullName,
      photoUrl: `https://randomuser.me/api/portraits/${
        getRandomInt(0, 1) ? "men" : "women"
      }/${getRandomInt(1, 99)}.jpg`,
      status,
      matchScore,
      applicationDate,
      experience: getRandomItem(experiences),
      skills,
      availability: getRandomItem(availabilities),
      interviewDate,
      notes,
      resumeUrl: `https://example.com/resumes/${generateId()}.pdf`,
      distance: getRandomItem(distances),
      salary: getRandomItem(salaries),
      isShortlisted: getRandomInt(0, 3) === 0, // 25% chance to be shortlisted
    });
  }

  return applications;
};

// Filter applications based on given filters
export const filterMockApplications = (
  applications: JobApplication[],
  filters: {
    status?: ApplicationStatus;
    matchScoreMin?: number;
    dateRange?: [string, string];
    isShortlisted?: boolean;
    search?: string;
    page?: number;
    pageSize?: number;
  } = {}
): { data: JobApplication[]; total: number } => {
  let filteredApplications = [...applications];

  if (filters.search) {
    const searchTerm = filters.search.toLowerCase();
    filteredApplications = filteredApplications.filter(
      (app) =>
        app.candidateName.toLowerCase().includes(searchTerm) ||
        app.skills?.some((skill) => skill.toLowerCase().includes(searchTerm))
    );
  }

  if (filters.status) {
    filteredApplications = filteredApplications.filter(
      (app) => app.status === filters.status
    );
  }

  if (filters.matchScoreMin !== undefined) {
    filteredApplications = filteredApplications.filter(
      (app) => app.matchScore ?? 0 >= (filters.matchScoreMin ?? 0)
    );
  }

  if (filters.isShortlisted !== undefined) {
    filteredApplications = filteredApplications.filter(
      (app) => app.isShortlisted === filters.isShortlisted
    );
  }

  if (filters.dateRange && filters.dateRange.length === 2) {
    const startDate = dayjs(filters.dateRange[0]);
    const endDate = dayjs(filters.dateRange[1]);
    filteredApplications = filteredApplications.filter((app) => {
      const appDate = dayjs(app.applicationDate);
      return appDate.isAfter(startDate) && appDate.isBefore(endDate);
    });
  }

  // Sort applications by application date (newest first)
  filteredApplications.sort(
    (a, b) => dayjs(b.applicationDate).unix() - dayjs(a.applicationDate).unix()
  );

  // Handle pagination
  const page = filters.page || 1;
  const pageSize = filters.pageSize || 10;
  const startIndex = (page - 1) * pageSize;
  const paginatedApplications = filteredApplications.slice(
    startIndex,
    startIndex + pageSize
  );

  return {
    data: paginatedApplications,
    total: filteredApplications.length,
  };
};

// Generate application statistics
export const getMockApplicationStatistics = (
  applications: JobApplication[]
) => {
  return {
    total: applications.length,
    new: applications.filter((app) => app.status === "Mới").length,
    viewed: applications.filter((app) => app.status === "Đã xem").length,
    scheduled: applications.filter((app) => app.status === "Lịch phỏng vấn")
      .length,
    interviewed: applications.filter((app) => app.status === "Đã phỏng vấn")
      .length,
    hired: applications.filter((app) => app.status === "Đã mời làm việc")
      .length,
    rejected: applications.filter((app) => app.status === "Đã từ chối").length,
    shortlisted: applications.filter((app) => app.isShortlisted).length,
  };
};
