// components/layout/MainHeader.tsx
"use client";

import { routes } from "@/routes";
import type { MenuProps } from "antd";
import { Layout, Menu, Avatar, Dropdown } from "antd";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  LogoutOutlined,
  SettingOutlined,
  UserOutlined,
} from "@ant-design/icons";
import ThemeToggle from "@/components/ui/common/ThemeToggle";
import { useTheme } from "@/contexts/ThemeContext";
import LanguageSwitcher from "@/components/ui/common/LanguageSwitcher";
import { findActiveRouteKeys } from "@/utils/routeUtils";
import { useTranslations } from "next-intl";
import { useAuth } from "@/contexts/AuthContext";

const { Header } = Layout;

const MainHeader = () => {
  const pathname = usePathname();
  const { theme: colorTheme } = useTheme();
  const t = useTranslations("route");
  const { logout } = useAuth();

  // Generate menu items from top-level routes
  const headerItems: MenuProps["items"] = Object.keys(routes)
    .filter((key) => {
      const route = routes[key as keyof typeof routes];
      return "path" in route || "key" in route;
    })
    .map((key) => {
      const route = routes[key as keyof typeof routes];

      if ("children" in route && route.children) {
        const childrenItems = Object.keys(route.children).map((childKey) => {
          const childRoute =
            route.children[childKey as keyof typeof route.children];
          return {
            key: childKey,
            icon: childRoute.icon,
            label: <Link href={childRoute.path}>{t(childRoute.label)}</Link>,
          };
        });

        return {
          key,
          label: t(route.label),
          children: childrenItems,
        };
      }

      return {
        key,
        label:
          "path" in route ? (
            <Link href={route.path}>{t(route.label)}</Link>
          ) : (
            t(route.label)
          ),
      };
    });

  // Use the shared utility to get the selected key
  const { selectedKeys } = findActiveRouteKeys(pathname);

  // User menu items for dropdown
  const userMenuItems: MenuProps["items"] = [
    {
      key: "profile",
      icon: <UserOutlined />,
      label: <Link href="/profile">Profile</Link>,
    },
    {
      key: "settings",
      icon: <SettingOutlined />,
      label: <Link href="/settings">Settings</Link>,
    },
    {
      key: "logout",
      icon: <LogoutOutlined />,
      label: "Logout",
      onClick: logout,
    },
  ];

  return (
    <Header
      className={`
    sticky top-0 z-10 w-full flex items-center justify-between !p-0 h-16
    shadow-md
    ${colorTheme === "dark" ? "bg-[#141414]" : "!bg-white"}
  `}
    >
      <div className="flex items-center gap-3 mr-4 ml-auto">
        <ThemeToggle />
        <LanguageSwitcher />
        <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
          <Avatar
            icon={<UserOutlined />}
            className="cursor-pointer"
            style={{
              backgroundColor: colorTheme === "dark" ? "#1f1f1f" : "",
            }}
          />
        </Dropdown>
      </div>
    </Header>
  );
};

export default MainHeader;
