"use client";

import React, { useState, useEffect } from "react";
import { Typography } from "antd";

const { Text } = Typography;

interface SidebarClockProps {
  collapsed: boolean;
}

const SidebarClock: React.FC<SidebarClockProps> = ({ collapsed }) => {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("vi-VN", {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("vi-VN", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  if (collapsed) {
    return (
      <div
        style={{
          position: "absolute",
          bottom: 0,
          left: 0,
          right: 0,
          padding: "12px 8px",
          backgroundColor: "#1f1f1f",
          borderTop: "1px solid #303030",
          textAlign: "center",
        }}
      >
        <Text
          style={{
            color: "#ffffff",
            fontSize: "9px",
            fontWeight: "600",
            fontFamily: "monospace",
            display: "block",
            marginBottom: "2px",
          }}
        >
          {formatDate(currentTime).slice(0, 5)}
        </Text>
        <Text
          style={{
            color: "#ffffff",
            fontSize: "9px",
            fontWeight: "600",
            fontFamily: "monospace",
            display: "block",
          }}
        >
          {formatTime(currentTime).slice(0, 5)}
        </Text>
      </div>
    );
  }

  return (
    <div
      style={{
        position: "absolute",
        bottom: 0,
        left: 0,
        right: 0,
        margin: "12px",
        padding: "12px 16px",
        backgroundColor: "#262626",
        borderRadius: "8px",
        border: "1px solid #303030",
      }}
    >
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Text
          style={{
            color: "#ffffff",
            fontSize: "16px",
            fontWeight: "600",
            fontFamily: "monospace",
          }}
        >
          {formatDate(currentTime)}
        </Text>

        <Text
          style={{
            color: "#ffffff",
            fontSize: "16px",
            fontWeight: "600",
            fontFamily: "monospace",
          }}
        >
          {formatTime(currentTime)}
        </Text>
      </div>
    </div>
  );
};

export default SidebarClock;
