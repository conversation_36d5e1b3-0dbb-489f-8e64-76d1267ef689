// components/layout/Sidebar.tsx
"use client";

import React, {
  useState,
  useEffect,
  useMemo,
  useCallback,
  useContext,
} from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import type { MenuProps } from "antd";
import { Image, Layout, Menu, theme, Button } from "antd";
import { routes } from "@/routes";
import { Routes } from "@/types/routes";
import { MenuFoldOutlined, MenuUnfoldOutlined } from "@ant-design/icons";
import { findActiveRouteKeys, findParentKey } from "@/utils/routeUtils";
import { useTranslations } from "next-intl";
import { AuthContext } from "@/contexts/AuthContext";
import SidebarClock from "./SidebarClock";

const { Sider } = Layout;

type MenuItem = Required<MenuProps>["items"][number];

const Sidebar = () => {
  const pathname = usePathname();
  const t = useTranslations("route");
  const [collapsed, setCollapsed] = useState(false);
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  const authContext = useContext(AuthContext);
  const userAccess = authContext?.access;
  const { token } = theme.useToken();

  // Extract functions for creating menu items
  const getItem = useCallback(
    (
      label: React.ReactNode,
      key: React.Key,
      icon?: React.ReactNode,
      children?: MenuItem[]
    ): MenuItem => {
      return {
        key,
        icon,
        children,
        label,
      } as MenuItem;
    },
    []
  );

  // Build menu items with memoization
  const buildMenuItems = useCallback(
    (routesObj: Routes): MenuItem[] => {
      return Object.keys(routesObj)
        .map((key) => {
          const route = routesObj[key];

          if (route.access && (!userAccess || !userAccess[route.access])) {
            return null;
          }

          if ("children" in route) {
            const children = buildMenuItems(route.children);
            if (!children.length) return null;
            return getItem(t(route.label), route.key, route.icon, children);
          }

          return getItem(
            <Link href={route.path}>{t(route.label)}</Link>,
            "key" in route ? route.key : key,
            route.icon
          );
        })
        .filter(Boolean);
    },
    [getItem, t, userAccess]
  );

  // Memoize menu items to prevent unnecessary re-renders
  const items = useMemo(() => buildMenuItems(routes), [buildMenuItems]);

  // Find active routes based on current path
  useEffect(() => {
    const { selectedKeys: newSelectedKeys, openKeys: newOpenKeys } =
      findActiveRouteKeys(pathname, collapsed);

    setSelectedKeys(newSelectedKeys);
    setOpenKeys(newOpenKeys);
  }, [pathname, collapsed]);

  const handleMenuClick: MenuProps["onClick"] = (e) => {
    setSelectedKeys([e.key]);
    // Don't auto-collapse when clicking submenu items
  };

  const onOpenChange: MenuProps["onOpenChange"] = (keys) => {
    setOpenKeys(keys);
  };

  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
    // Clear open keys when collapsing
    if (!collapsed) {
      setOpenKeys([]);
    } else {
      // Restore open keys from selected keys' parent when expanding
      const parentKey = findParentKey(selectedKeys[0]);
      if (parentKey) {
        setOpenKeys([parentKey]);
      }
    }
  };

  return (
    <div style={{ position: "relative" }}>
      <Sider
        style={{
          overflow: "auto",
          height: "100vh",
          position: "sticky",
          top: 0,
          left: 0,
          bottom: 0,
          borderRight: `1px solid ${token.colorBorder}`,
          scrollbarWidth: "thin",
          paddingBottom: collapsed ? "80px" : "120px", // Make space for clock
        }}
        collapsed={collapsed}
        width={250}
        theme="dark"
        collapsedWidth={80}
      >
        <div
          style={{
            height: "64px",
            width: "100%",
            display: "flex",
            alignItems: "center",
            justifyContent: collapsed ? "center" : "flex-start",
            paddingLeft: collapsed ? 0 : "20px",
          }}
        >
          {collapsed ? (
            <Image
              src="/favicon.svg"
              alt="Logo"
              width={32}
              height={32}
              preview={false}
            />
          ) : (
            <Image
              src="/dark-logo.svg"
              alt="Logo"
              width={100}
              height={32}
              preview={false}
            />
          )}
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={selectedKeys}
          openKeys={openKeys}
          onOpenChange={onOpenChange}
          onClick={handleMenuClick}
          items={items}
        />

        {/* Real-time Clock */}
        <SidebarClock collapsed={collapsed} />
      </Sider>
      <Button
        type="text"
        icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
        onClick={toggleCollapsed}
        style={{
          position: "absolute",
          right: "-16px",
          top: "72px",
          borderRadius: "50%",
          width: "32px",
          height: "32px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          cursor: "pointer",
          zIndex: 10,
          color: token.colorTextSecondary,
          backgroundColor: token.colorBgElevated,
          border: `1px solid ${token.colorBorder}`,
          boxShadow: token.boxShadowSecondary,
        }}
        size="small"
        aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
      />
    </div>
  );
};

export default Sidebar;
