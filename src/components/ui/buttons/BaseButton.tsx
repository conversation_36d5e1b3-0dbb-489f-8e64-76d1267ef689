"use client";
import React from "react";
import { Button } from "antd";
import { ButtonProps } from "antd/lib/button";
import { SizeType } from "antd/lib/config-provider/SizeContext";

interface BaseButtonProps extends ButtonProps {
  label?: React.ReactNode;
  className?: string;
  icon?: React.ReactNode;
  onClick?: React.MouseEventHandler<HTMLElement>;
  size?: SizeType;
  type?: "primary" | "default" | "dashed" | "link" | "text";
  loading?: boolean | { delay?: number };
  disabled?: boolean;
  block?: boolean;
  danger?: boolean;
  ghost?: boolean;
  shape?: "default" | "circle" | "round";
  htmlType?: "button" | "submit" | "reset";
  id?: string;
}

const BaseButton: React.FC<BaseButtonProps> = ({
  label,
  className = "",
  icon,
  onClick,
  size = "middle",
  type = "default",
  loading = false,
  disabled = false,
  block = false,
  danger = false,
  ghost = false,
  shape = "default",
  htmlType = "button",
  id,
  children,
  ...props
}) => {
  const buttonContent = (
    <Button
      className={`flex items-center justify-center ${className}`}
      icon={icon}
      onClick={onClick}
      size={size}
      type={type}
      loading={loading}
      disabled={disabled}
      block={block}
      danger={danger}
      ghost={ghost}
      shape={shape}
      htmlType={htmlType}
      id={id}
      {...props}
    >
      {label || children}
    </Button>
  );

  return buttonContent;
};

export default BaseButton;
