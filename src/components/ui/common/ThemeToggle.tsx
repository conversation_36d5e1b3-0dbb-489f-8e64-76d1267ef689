// components/ui/ThemeToggle.tsx
"use client";

import React from "react";
import { Button } from "antd";
import { useTheme } from "@/contexts/ThemeContext";
import { MoonOutlined, SunOutlined } from "@ant-design/icons";

const ThemeToggle: React.FC = () => {
  const { theme, toggleTheme } = useTheme();

  return (
    <Button
      type="text"
      icon={theme === "dark" ? <SunOutlined /> : <MoonOutlined />}
      onClick={toggleTheme}
      aria-label={`Switch to ${theme === "dark" ? "light" : "dark"} mode`}
    />
  );
};

export default ThemeToggle;
