// components/ui/common/LanguageSwitcher.tsx
"use client";

import { Select } from "antd";
import { useLanguage } from "@/contexts/LanguageContext";
import { useTranslations } from "next-intl";

const LanguageSwitcher = () => {
  const { locale, setLocale } = useLanguage();
  const tCommon = useTranslations("common");

  return (
    <Select
      value={locale}
      onChange={(value) => setLocale(value as "en" | "vi")}
      options={[
        { value: "en", label: tCommon("languages.english") },
        { value: "vi", label: tCommon("languages.vietnamese") },
      ]}
      suffixIcon={null}
      popupMatchSelectWidth={false}
    />
  );
};

export default LanguageSwitcher;
