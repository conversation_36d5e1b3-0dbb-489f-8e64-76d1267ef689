"use client";
import { Button, Space, Input, DatePicker, Select, Form } from "antd";
import { FilterOutlined, ReloadOutlined } from "@ant-design/icons";
import { useState, useEffect } from "react";
import BaseModal from "@/components/ui/modals/BaseModal";
import { useTranslations } from "next-intl";

export type FilterValue = string | number | boolean | [string, string] | null;

export interface FilterConfig {
  key: string;
  label: string;
  type: "text" | "select" | "date" | "dateRange" | "number";
  options?: { value: string | number | boolean; label: string }[];
  placeholder?: string;
}

interface TableFiltersProps {
  filters: FilterConfig[];
  onFilterChange: (filters: Record<string, FilterValue>) => void;
  initialValues?: Record<string, FilterValue>;
}

const TableFilter = ({
  filters,
  onFilterChange,
  initialValues = {},
}: TableFiltersProps) => {
  const tCommon = useTranslations("common");
  const tTable = useTranslations("table");

  const [filterModalVisible, setFilterModalVisible] = useState(false);
  const [filterValues, setFilterValues] = useState<Record<string, FilterValue>>(
    initialValues || {}
  );
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue(filterValues);
  }, [filterValues, form]);

  const handleFilterSubmit = (values: Record<string, FilterValue>) => {
    // Clean up empty values
    const cleanValues = Object.fromEntries(
      Object.entries(values).filter(
        ([, v]) => v !== undefined && v !== null && v !== ""
      )
    );
    setFilterValues(cleanValues);
    setFilterModalVisible(false);
    onFilterChange(cleanValues);
  };

  const clearFilters = () => {
    form.resetFields();
    setFilterValues({});
    onFilterChange({});
  };

  const hasFilters = Object.keys(filterValues).length > 0;

  const renderFilterInput = (filter: FilterConfig) => {
    switch (filter.type) {
      case "text":
        return (
          <Input
            placeholder={
              filter.placeholder ||
              tTable("enter_placeholder", { label: filter.label.toLowerCase() })
            }
          />
        );
      case "number":
        return (
          <Input
            type="number"
            placeholder={
              filter.placeholder ||
              tTable("enter_placeholder", { label: filter.label.toLowerCase() })
            }
          />
        );
      case "select":
        return (
          <Select
            placeholder={
              filter.placeholder ||
              tTable("select_placeholder", {
                label: filter.label.toLowerCase(),
              })
            }
            allowClear
            style={{ width: "100%" }}
            options={filter.options || []}
          />
        );
      case "date":
        return <DatePicker style={{ width: "100%" }} />;
      case "dateRange":
        return <DatePicker.RangePicker style={{ width: "100%" }} />;
      default:
        return (
          <Input
            placeholder={
              filter.placeholder ||
              tTable("enter_placeholder", { label: filter.label.toLowerCase() })
            }
          />
        );
    }
  };

  if (filters.length === 0) {
    return null;
  }

  return (
    <>
      <Space>
        <Button
          icon={<FilterOutlined />}
          onClick={() => setFilterModalVisible(true)}
          type={hasFilters ? "primary" : "default"}
        >
          {tCommon("actions.filter")}
        </Button>
        {hasFilters && (
          <Button icon={<ReloadOutlined />} onClick={clearFilters}>
            {tTable("clear_filters")}
          </Button>
        )}
      </Space>

      {/* Filter Modal */}
      <BaseModal
        isVisible={filterModalVisible}
        title={tTable("filter_data")}
        onClose={() => setFilterModalVisible(false)}
        onSubmit={form.submit}
        submitText={tTable("apply_filters")}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFilterSubmit}
          initialValues={filterValues}
        >
          {filters.map((filter) => (
            <Form.Item key={filter.key} name={filter.key} label={filter.label}>
              {renderFilterInput(filter)}
            </Form.Item>
          ))}
        </Form>
      </BaseModal>
    </>
  );
};

export default TableFilter;
