// components/ui/descriptions/BaseDescription.tsx
"use client";

import React, { ReactNode } from "react";
import { Descriptions, DescriptionsProps } from "antd";
import { Typography } from "antd";

const { Text } = Typography;

export interface DescriptionItem {
  label: string;
  value: ReactNode;
  span?: number;
  labelStyle?: React.CSSProperties;
  contentStyle?: React.CSSProperties;
}

interface BaseDescriptionProps extends Omit<DescriptionsProps, "items"> {
  title?: React.ReactNode;
  items: DescriptionItem[];
  bordered?: boolean;
  column?: number;
  layout?: "horizontal" | "vertical";
  className?: string;
  clickable?: boolean;
}

const BaseDescription: React.FC<BaseDescriptionProps> = ({
  title,
  items,
  bordered = false,
  column = 1,
  layout = "horizontal",
  className = "",
  clickable = false,
  ...props
}) => {
  const renderValue = (value: ReactNode): ReactNode => {
    if (value === null || value === undefined || value === "") {
      return <Text type="secondary">N/A</Text>;
    }
    return value;
  };

  return (
    <Descriptions
      title={title}
      bordered={bordered}
      column={column}
      layout={layout}
      className={`w-full ${className}`}
      {...props}
    >
      {items.map((item, index) => (
        <Descriptions.Item
          key={`${item.label}-${index}`}
          label={item.label}
          span={item.span}
          className={clickable ? "cursor-pointer hover:bg-gray-50" : ""}
        >
          {renderValue(item.value)}
        </Descriptions.Item>
      ))}
    </Descriptions>
  );
};

export default BaseDescription;
