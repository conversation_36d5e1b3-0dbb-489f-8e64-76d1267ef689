"use client";
import { DatePicker, Form } from "antd";
import { DatePickerProps } from "antd/lib/date-picker";
import { Rule } from "antd/lib/form";
import React from "react";
import { useTranslations } from "next-intl";

interface BaseDatePickerProps extends DatePickerProps {
  label?: string;
  name?: string;
  required?: boolean;
  error?: string;
  helpText?: string;
  rules?: Rule[];
  className?: string;
}

const BaseDatePicker: React.FC<BaseDatePickerProps> = ({
  label,
  name,
  required = false,
  error,
  helpText,
  rules = [],
  className = "",
  placeholder,
  ...props
}) => {
  const tCommon = useTranslations("common");
  const status = error ? "error" : "";

  // Add required rule if required prop is true
  if (required && !rules.some((rule) => "required" in rule)) {
    rules.push({
      required: true,
      message: `${tCommon("placeholders.please_select")} ${label || tCommon("placeholders.select_date")}`,
    });
  }

  return (
    <Form.Item
      label={label}
      required={required}
      help={error || helpText}
      validateStatus={status}
      className={`w-full ${className}`}
      name={name}
      rules={rules}
    >
      <DatePicker
        placeholder={placeholder || tCommon("placeholders.select_date")}
        status={status}
        style={{ width: "100%" }}
        {...props}
      />
    </Form.Item>
  );
};

export default BaseDatePicker;
