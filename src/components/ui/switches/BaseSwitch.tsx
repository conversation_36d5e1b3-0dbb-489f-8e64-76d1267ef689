"use client";
import { Form, Switch } from "antd";
import { Rule } from "antd/lib/form";
import { SwitchProps } from "antd/lib/switch";
import React from "react";
import { useTranslations } from "next-intl";

interface BaseSwitchProps extends SwitchProps {
  label?: string;
  name?: string;
  required?: boolean;
  error?: string;
  helpText?: string;
  rules?: Rule[];
  className?: string;
  checkedText?: React.ReactNode;
  unCheckedText?: React.ReactNode;
  description?: string;
}

const BaseSwitch: React.FC<BaseSwitchProps> = ({
  label,
  name,
  required = false,
  error,
  helpText,
  rules = [],
  className = "",
  checkedText,
  unCheckedText,
  description,
  ...props
}) => {
  const tCommon = useTranslations("common");
  const status = error ? "error" : "";

  // Add required rule if required prop is true
  if (required && !rules.some((rule) => "required" in rule)) {
    rules.push({
      required: true,
      message: `${tCommon("placeholders.please_select")} ${label || ""}`.trim(),
      transform: (value) => !!value,
    });
  }

  return (
    <Form.Item
      label={label}
      required={required}
      help={error || helpText}
      validateStatus={status}
      className={`${className}`}
      name={name}
      rules={rules}
      valuePropName="checked"
    >
      <Switch
        {...props}
        checkedChildren={checkedText}
        unCheckedChildren={unCheckedText}
        className="inline-block"
      />
      {description && (
        <span className="text-gray-500 ml-3 text-sm">{description}</span>
      )}
    </Form.Item>
  );
};

export default BaseSwitch;
