"use client";
import React, { ReactNode } from "react";
import { <PERSON><PERSON>, Button } from "antd";
import type { FormInstance } from "antd/es/form";
import { useTranslations } from "next-intl";

interface BaseModalProps {
  title: string;
  isVisible: boolean;
  onClose: () => void;
  onSubmit?: () => void | Promise<void>;
  children: ReactNode;
  width?: number | string;
  form?: FormInstance;
  submitText?: string;
  cancelText?: string;
  confirmLoading?: boolean;
  destroyOnClose?: boolean;
  footer?: ReactNode | null;
  className?: string;
  rootClassName?: string;
  contentClassName?: string;
  headerClassName?: string;
  bodyClassName?: string;
  footerClassName?: string;
  okButtonProps?: object;
  cancelButtonProps?: object;
}

const BaseModal: React.FC<BaseModalProps> = ({
  title,
  isVisible,
  onClose,
  onSubmit,
  children,
  width = 800,
  form,
  submitText,
  cancelText,
  confirmLoading = false,
  destroyOnClose = true,
  footer,
  className,
  rootClassName,
  contentClassName,
  okButtonProps,
  cancelButtonProps,
}) => {
  const tCommon = useTranslations("common");
  const handleSubmit = async () => {
    if (onSubmit) {
      await onSubmit();
    }
  };

  const defaultFooter = (
    <div className="flex justify-end gap-2">
      <Button onClick={onClose} {...cancelButtonProps}>
        {cancelText || tCommon("actions.cancel")}
      </Button>
      {onSubmit && (
        <Button
          type="primary"
          onClick={handleSubmit}
          loading={confirmLoading}
          {...okButtonProps}
        >
          {submitText || tCommon("actions.save")}
        </Button>
      )}
    </div>
  );

  return (
    <Modal
      title={title}
      open={isVisible}
      onCancel={onClose}
      footer={footer === undefined ? defaultFooter : footer}
      width={width}
      destroyOnHidden={destroyOnClose}
      className={className}
      rootClassName={rootClassName}
      modalRender={(node) => <div className={contentClassName}>{node}</div>}
      closeIcon={
        <span className="text-gray-500 hover:text-gray-700 text-xl">
          &times;
        </span>
      }
      centered
    >
      <div className="max-h-[600px] overflow-y-auto">{children}</div>
    </Modal>
  );
};

export default BaseModal;
