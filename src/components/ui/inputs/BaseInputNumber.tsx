"use client";
import { Flex, Form, InputNumber } from "antd";
import { Rule } from "antd/lib/form";
import { InputNumberProps } from "antd/lib/input-number";
import React from "react";

interface BaseInputNumberProps extends InputNumberProps {
  label?: string;
  name?: string;
  required?: boolean;
  error?: string;
  helpText?: string;
  rules?: Rule[];
  className?: string;
}

const BaseInputNumber: React.FC<BaseInputNumberProps> = ({
  label,
  name,
  required = false,
  error,
  helpText,
  rules = [],
  className = "",
  placeholder = "Enter number",
  ...props
}) => {
  const status = error ? "error" : "";

  // Add required rule if required prop is true
  if (required && !rules.some((rule) => "required" in rule)) {
    rules.push({
      required: true,
      message: `Please enter ${label || "a number"}`,
    });
  }

  return (
    <Form.Item
      label={label}
      required={required}
      help={error || helpText}
      validateStatus={status}
      className={`w-full ${className}`}
      name={name}
      rules={rules}
    >
      <InputNumber
        placeholder={placeholder}
        status={status}
        style={{ width: "100%" }}
        {...props}
      />
    </Form.Item>
  );
};

export default BaseInputNumber;
