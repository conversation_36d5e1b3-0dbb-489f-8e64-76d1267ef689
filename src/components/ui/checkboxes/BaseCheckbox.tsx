"use client";
import { Checkbox, Flex, Form } from "antd";
import { CheckboxProps } from "antd/lib/checkbox";
import { Rule } from "antd/lib/form";
import React from "react";
import { useTranslations } from "next-intl";

interface BaseCheckboxProps extends Omit<CheckboxProps, "name"> {
  label?: string;
  name?: string | (string | number)[];
  required?: boolean;
  error?: string;
  helpText?: string;
  rules?: Rule[];
  className?: string;
  checkboxLabel?: React.ReactNode;
}

const BaseCheckbox: React.FC<BaseCheckboxProps> = ({
  label,
  name,
  required = false,
  error,
  helpText,
  rules = [],
  className = "",
  checkboxLabel,
  ...props
}) => {
  const tCommon = useTranslations("common");
  const status = error ? "error" : "";

  // Add required rule if required prop is true
  if (required && !rules.some((rule) => "required" in rule)) {
    rules.push({
      required: true,
      message: `${tCommon("placeholders.please_select")} ${label || ""}`.trim(),
      transform: (value) => !!value,
    });
  }

  return (
    <Form.Item
      label={label}
      required={required}
      help={error || helpText}
      validateStatus={status}
      className={`w-full ${className}`}
      name={name}
      rules={rules}
      valuePropName="checked"
    >
      <Flex vertical gap="small">
        <Checkbox {...props}>{checkboxLabel}</Checkbox>
      </Flex>
    </Form.Item>
  );
};

export default BaseCheckbox;
