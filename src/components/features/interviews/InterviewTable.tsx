// components/features/interviews/InterviewTable.tsx
"use client";
import BaseTable from "@/components/ui/tables/BaseTable";
import { FilterConfig } from "@/components/ui/tables/TableFilter";
import {
  INTERVIEW_STATUS_OPTIONS,
  INTERVIEW_TYPE_OPTIONS,
} from "@/constants/interview";
import InterviewService from "@/services/interviewService";
import { Interview, InterviewStatus, InterviewType } from "@/types/interview";
import {
  CalendarOutlined,
  ClockCircleOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { Avatar, Space, Tag } from "antd";
import dayjs from "dayjs";
import { useRouter } from "next/navigation";
import React, { useState, useContext } from "react";
import {
  InterviewStatusBadge,
  InterviewTypeBadge,
} from "./InterviewStatusBadge";
import { UserRole } from "@/constants/userRole";
import { AuthContext } from "@/contexts/AuthContext";
import { PermissionEnum } from "@/constants/_permissionEnum";

interface InterviewTableProps {
  onEditInterview?: (interview: Interview) => void;
  userRole?: UserRole;
}

const InterviewTable: React.FC<InterviewTableProps> = ({
  onEditInterview,
  userRole = UserRole.ADMIN,
}) => {
  const router = useRouter();
  const [refreshKey, setRefreshKey] = useState(0);
  const authContext = useContext(AuthContext);
  const access = authContext?.access;

  // Define columns for the interview table
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 100,
      render: (id: number) => <span className="font-mono text-xs">#{id}</span>,
    },
    {
      title: "Job Seeker",
      key: "jobSeeker",
      render: (_: unknown, record: Interview) =>
        userRole !== UserRole.JOB_SEEKER ? (
          <div className="flex items-center">
            <Avatar
              src={record.jobSeeker?.photoUrl}
              icon={<UserOutlined />}
              className="mr-2"
            />
            <div>
              <div>{record.jobSeeker?.name}</div>
              <div className="text-xs text-gray-500">
                ID: {record.jobSeeker?.id}
              </div>
            </div>
          </div>
        ) : null,
    },
    {
      title: "Employer",
      key: "employer",
      render: (_: unknown, record: Interview) =>
        userRole !== UserRole.EMPLOYER ? (
          <div>
            <div>{record.employer?.name}</div>
            <div className="text-xs text-gray-500">
              ID: {record.employer?.id}
            </div>
          </div>
        ) : null,
    },
    {
      title: "Job Post",
      key: "jobPost",
      render: (_: unknown, record: Interview) => (
        <div>
          <div className="truncate max-w-xs">{record.postTitle || "N/A"}</div>
          <div className="text-xs text-gray-500">
            ID: {record.postId || "N/A"}
          </div>
        </div>
      ),
    },
    {
      title: "Schedule",
      key: "schedule",
      render: (_: unknown, record: Interview) => {
        const scheduledDate = dayjs(record.scheduledTime);
        const now = dayjs();
        const isToday =
          scheduledDate.format("YYYY-MM-DD") === now.format("YYYY-MM-DD");
        const isSoon =
          isToday &&
          scheduledDate.diff(now, "hour") < 2 &&
          scheduledDate.diff(now, "hour") >= 0;

        return (
          <Space direction="vertical" size="small">
            <div>
              <CalendarOutlined className="mr-1" />
              <span className={isToday ? "font-bold" : ""}>
                {scheduledDate.format("MMM D, YYYY h:mm A")}
              </span>
              {isToday && (
                <Tag
                  color={isSoon ? "red" : "blue"}
                  className="ml-2"
                  style={{ fontSize: "10px" }}
                >
                  {isSoon ? "Very Soon" : "Today"}
                </Tag>
              )}
            </div>
            <div>
              <ClockCircleOutlined className="mr-1" /> {record.durationMinutes}{" "}
              minutes
            </div>
          </Space>
        );
      },
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      render: (status: string) => (
        <InterviewStatusBadge status={status as InterviewStatus} />
      ),
    },
    {
      title: "Meeting Link",
      key: "meetingLink",
      render: (_: unknown, record: Interview) =>
        record.meetingLink ? (
          <a
            href={record.meetingLink}
            target="_blank"
            rel="noopener noreferrer"
          >
            <Tag color="blue">Join Meeting</Tag>
          </a>
        ) : (
          <Tag color="gray">No Link</Tag>
        ),
    },
    {
      title: "Feedback",
      key: "feedback",
      width: 100,
      render: (_: unknown, record: Interview) => (
        <Tag color={record.feedback ? "green" : "orange"}>
          {record.feedback ? "Provided" : "Pending"}
        </Tag>
      ),
    },
  ];

  // Filter configurations
  const filters: FilterConfig[] = [
    {
      key: "status",
      label: "Status",
      type: "select",
      options: INTERVIEW_STATUS_OPTIONS.map((status) => ({
        value: status.value,
        label: status.label,
      })),
    },
    {
      key: "type",
      label: "Type",
      type: "select",
      options: INTERVIEW_TYPE_OPTIONS.map((type) => ({
        value: type.value,
        label: type.label,
      })),
    },
    {
      key: "dateRange",
      label: "Date Range",
      type: "dateRange",
    },
    {
      key: "feedbackProvided",
      label: "Feedback",
      type: "select",
      options: [
        { value: "true", label: "Provided" },
        { value: "false", label: "Pending" },
      ],
    },
  ];

  const handleViewInterview = (record: Interview) => {
    router.push(`/features/interviews/${record.id}`);
  };

  const handleDeleteInterview = async (record: Interview) => {
    try {
      await InterviewService.deleteInterview(record.id);
      setRefreshKey((prevKey) => prevKey + 1);
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  };

  // Filter columns based on user role
  const filteredColumns = columns.filter((col) => {
    if (userRole === UserRole.EMPLOYER && col.key === "employer") return false;
    if (userRole === UserRole.JOB_SEEKER && col.key === "jobSeeker")
      return false;
    return true;
  });

  return (
    <BaseTable<Interview>
      api={InterviewService.searchInterviews}
      columns={filteredColumns}
      rowKey="id"
      key={`interview-table-${refreshKey}`}
      initialParams={{ sortBy: "scheduledTime", sortOrder: "desc" }}
      title="Interview Management"
      createBtnText="Schedule Interview"
      showSearch={true}
      searchPlaceholder="Search by job seeker, employer or job title..."
      onRowClick={handleViewInterview}
      onEdit={
        access?.[PermissionEnum.INTERVIEW_UPDATE] ? onEditInterview : undefined
      }
      onDelete={
        access?.[PermissionEnum.INTERVIEW_DELETE]
          ? handleDeleteInterview
          : undefined
      }
      filters={filters}
    />
  );
};

export default InterviewTable;
