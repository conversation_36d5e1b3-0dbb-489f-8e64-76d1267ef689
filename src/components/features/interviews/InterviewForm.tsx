"use client";
import BaseButton from "@/components/ui/buttons/BaseButton";
import BaseDatePicker from "@/components/ui/datepickers/BaseDatePicker";
import BaseInput from "@/components/ui/inputs/BaseInput";
import BaseTextArea from "@/components/ui/inputs/BaseTextArea";
import BaseSelect from "@/components/ui/selects/BaseSelect";
import {
  INTERVIEW_DURATION_OPTIONS,
  INTERVIEW_STATUS_OPTIONS,
  INTERVIEW_TYPE_OPTIONS,
} from "@/constants/interview";
import { UserRole } from "@/constants/userRole";
import ApiService from "@/services/ApiService";
import InterviewService from "@/services/interviewService";
import {
  Interview,
  CreateInterviewRequest,
  UpdateInterviewRequest,
  InterviewType,
  InterviewStatus,
} from "@/types/interview";
import { Card, Col, Divider, Form, message, Row } from "antd";
import dayjs from "dayjs";
import React, { useEffect, useState } from "react";

interface InterviewFormProps {
  interview?: Interview;
  onSuccess: () => void;
  onCancel: () => void;
}

interface JobPost {
  id: number;
  title: string;
}

interface User {
  id: number;
  name: string;
  role: UserRole;
}

const InterviewForm: React.FC<InterviewFormProps> = ({
  interview,
  onSuccess,
  onCancel,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [interviewType, setInterviewType] = useState<InterviewType>(
    InterviewType.VIDEO_CALL
  );
  const [jobPosts, setJobPosts] = useState<{ value: number; label: string }[]>(
    []
  );
  const [candidates, setCandidates] = useState<
    { value: number; label: string }[]
  >([]);
  const [employers, setEmployers] = useState<
    { value: number; label: string }[]
  >([]);

  // Fetch job posts, candidates, and employers for dropdown options
  useEffect(() => {
    const fetchOptions = async () => {
      try {
        // Fetch job posts
        const jobPostsResponse = await ApiService.get<{ data: JobPost[] }>(
          "/api/posts",
          { limit: 100 }
        );
        if (jobPostsResponse.data.data) {
          setJobPosts(
            jobPostsResponse.data.data.map((post: JobPost) => ({
              value: post.id,
              label: post.title,
            }))
          );
        }

        // Fetch candidates (job seekers)
        const candidatesResponse = await ApiService.get<{ data: User[] }>(
          "/api/users",
          { role: UserRole.JOB_SEEKER, limit: 100 }
        );
        if (candidatesResponse.data.data) {
          setCandidates(
            candidatesResponse.data.data.map((user: User) => ({
              value: user.id,
              label: user.name,
            }))
          );
        }

        // Fetch employers
        const employersResponse = await ApiService.get<{ data: User[] }>(
          "/api/users",
          { role: UserRole.EMPLOYER, limit: 100 }
        );
        if (employersResponse.data.data) {
          setEmployers(
            employersResponse.data.data.map((user: User) => ({
              value: user.id,
              label: user.name,
            }))
          );
        }
      } catch (error) {
        console.error("Failed to fetch options:", error);
        message.error("Failed to load form options");

        // For demonstration purposes, add some mock data if API fails
        setJobPosts([
          { value: 1, label: "Front-end Developer" },
          { value: 2, label: "UX/UI Designer" },
          { value: 3, label: "Marketing Assistant" },
        ]);

        setCandidates([
          { value: 201, label: "Nguyễn Văn A" },
          { value: 202, label: "Trần Thị B" },
          { value: 203, label: "Lê Văn C" },
        ]);

        setEmployers([
          { value: 101, label: "Tech Innovations" },
          { value: 102, label: "Creative Solutions" },
          { value: 103, label: "Marketing Group" },
        ]);
      }
    };

    fetchOptions();
  }, []);

  // Set form initial values when interview data is available
  useEffect(() => {
    if (interview) {
      form.setFieldsValue({
        jobPostId: interview.postId,
        candidateId: interview.jobSeeker.id,
        employerId: interview.employer.id,
        status: interview.status,
        scheduledDate: interview.scheduledTime
          ? dayjs(interview.scheduledTime)
          : undefined,
        duration: interview.durationMinutes,
        meetingLink: interview.meetingLink,
        notes: interview.notes,
      });
      // Set interview type from constants if available
      const typeOption = INTERVIEW_TYPE_OPTIONS.find(
        (option) =>
          option.label === interview.status || option.value === interview.status
      );
      if (typeOption) {
        setInterviewType(typeOption.value as InterviewType);
      }
    }
  }, [interview, form]);

  const handleTypeChange = (value: InterviewType) => {
    setInterviewType(value);
  };

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);

      // Format the data for API
      if (!values.scheduledDate) {
        message.error("Please select a scheduled date and time");
        return;
      }

      const formattedValues = {
        employerId: values.employerId,
        jobSeekerId: values.candidateId,
        scheduledTime: dayjs(values.scheduledDate).toISOString(),
        durationMinutes: values.duration,
        status: values.status,
        meetingLink: values.meetingLink,
        notes: values.notes,
        postId: values.jobPostId,
        isActive: true,
      };

      if (interview) {
        // Update existing interview
        const updateData: UpdateInterviewRequest = {
          ...formattedValues,
          id: interview.id,
        };
        await InterviewService.updateInterview(interview.id, updateData);
        message.success("Interview updated successfully");
      } else {
        // Create new interview
        const createData: CreateInterviewRequest = formattedValues;
        await InterviewService.createInterview(createData);
        message.success("Interview scheduled successfully");
      }

      onSuccess();
    } catch (error) {
      console.error("Failed to save interview:", error);
      message.error("Failed to save interview");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          status: InterviewStatus.SCHEDULED,
          type: InterviewType.VIDEO_CALL,
          duration: 30,
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <BaseSelect
              label="Job Post"
              name="jobPostId"
              required
              options={jobPosts}
              placeholder="Select job post"
            />
          </Col>

          <Col span={12}>
            <BaseSelect
              label="Candidate"
              name="candidateId"
              required
              options={candidates}
              placeholder="Select candidate"
            />
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <BaseSelect
              label="Employer"
              name="employerId"
              required
              options={employers}
              placeholder="Select employer"
            />
          </Col>

          <Col span={12}>
            <BaseSelect
              label="Status"
              name="status"
              required
              options={INTERVIEW_STATUS_OPTIONS.map((status) => ({
                value: status.value,
                label: status.label,
              }))}
            />
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <BaseDatePicker
              label="Scheduled Date & Time"
              name="scheduledDate"
              required
              showTime
              format="YYYY-MM-DD HH:mm"
              placeholder="Select date and time"
            />
          </Col>

          <Col span={12}>
            <BaseSelect
              label="Duration (minutes)"
              name="duration"
              required
              options={INTERVIEW_DURATION_OPTIONS.map((option) => ({
                value: option.value,
                label: option.label,
              }))}
            />
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <BaseSelect
              label="Interview Type"
              name="type"
              required
              options={INTERVIEW_TYPE_OPTIONS.map((type) => ({
                value: type.value,
                label: type.label,
              }))}
              onChange={handleTypeChange}
            />
          </Col>

          <Col span={12}>
            {interviewType === InterviewType.IN_PERSON && (
              <BaseInput
                label="Location"
                name="location"
                required
                placeholder="Enter interview location"
              />
            )}

            {interviewType === InterviewType.VIDEO_CALL && (
              <BaseInput
                label="Meeting Link"
                name="meetingLink"
                required
                placeholder="Enter video conference link"
              />
            )}

            {interviewType === InterviewType.PHONE_CALL && (
              <BaseInput
                label="Contact Number"
                name="contactNumber"
                placeholder="Enter contact phone number"
              />
            )}
          </Col>
        </Row>

        <Divider />

        <BaseTextArea
          label="Notes"
          name="notes"
          placeholder="Enter any additional notes for this interview"
          rows={4}
          helpText="Include preparation instructions, required documents, or any other important information."
        />

        <div className="flex justify-end gap-2 mt-4">
          <BaseButton onClick={onCancel} label="Cancel" />
          <BaseButton
            type="primary"
            htmlType="submit"
            loading={loading}
            label={interview ? "Update Interview" : "Schedule Interview"}
          />
        </div>
      </Form>
    </Card>
  );
};

export default InterviewForm;
