"use client";
import { Interview } from "@/types/interview";
import React from "react";
import BaseModal from "@/components/ui/modals/BaseModal";
import BaseButton from "@/components/ui/buttons/BaseButton";

interface NotificationModalProps {
  interview: Interview;
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const NotificationModal: React.FC<NotificationModalProps> = ({
  interview,
  visible,
  onClose,
  onSuccess,
}) => {
  console.log("🚀 ~ interview:", interview);
  // Note: In a real implementation, you would replace this with a proper form
  // for sending notifications. For now, we'll show a placeholder.
  return (
    <BaseModal
      title="Send Notification"
      isVisible={visible}
      onClose={onClose}
      footer={null}
      width={600}
    >
      {/* This would be replaced with an actual notification form component */}
      <p>Notification form would be rendered here</p>
      <div className="flex justify-end gap-2 mt-4">
        <BaseButton onClick={onClose} label="Cancel" />
        <BaseButton
          type="primary"
          onClick={onSuccess}
          label="Send Notification"
        />
      </div>
    </BaseModal>
  );
};

export default NotificationModal;
