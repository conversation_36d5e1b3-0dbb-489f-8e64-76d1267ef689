"use client";
import { Interview } from "@/types/interview";
import React from "react";
import BaseModal from "@/components/ui/modals/BaseModal";
import BaseButton from "@/components/ui/buttons/BaseButton";

interface FeedbackModalProps {
  interview: Interview;
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const FeedbackModal: React.FC<FeedbackModalProps> = ({
  interview,
  visible,
  onClose,
  onSuccess,
}) => {
  console.log("🚀 ~ interview:", interview);
  // Note: In a real implementation, you would replace this with a proper form
  // and handle the submission logic. For now, we'll show a placeholder.
  return (
    <BaseModal
      title="Submit Interview Feedback"
      isVisible={visible}
      onClose={onClose}
      footer={null}
      width={600}
    >
      {/* This would be replaced with an actual feedback form component */}
      <p>Feedback form would be rendered here</p>
      <div className="flex justify-end gap-2 mt-4">
        <BaseButton onClick={onClose} label="Cancel" />
        <BaseButton
          type="primary"
          onClick={onSuccess}
          label="Submit Feedback"
        />
      </div>
    </BaseModal>
  );
};

export default FeedbackModal;
