"use client";
import { Interview } from "@/types/interview";
import React from "react";
import BaseModal from "@/components/ui/modals/BaseModal";
import InterviewForm from "../InterviewForm";

interface EditInterviewModalProps {
  interview: Interview;
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const EditInterviewModal: React.FC<EditInterviewModalProps> = ({
  interview,
  visible,
  onClose,
  onSuccess,
}) => {
  return (
    <BaseModal
      title="Edit Interview"
      isVisible={visible}
      onClose={onClose}
      width={800}
      footer={null}
    >
      <InterviewForm
        interview={interview}
        onSuccess={onSuccess}
        onCancel={onClose}
      />
    </BaseModal>
  );
};

export default EditInterviewModal;
