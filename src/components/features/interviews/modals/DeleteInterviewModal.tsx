"use client";
import { Interview } from "@/types/interview";
import React, { useState } from "react";
import BaseModal from "@/components/ui/modals/BaseModal";
import { message } from "antd";
import BaseButton from "@/components/ui/buttons/BaseButton";
import InterviewService from "@/services/interviewService";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";

interface DeleteInterviewModalProps {
  interview: Interview;
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const DeleteInterviewModal: React.FC<DeleteInterviewModalProps> = ({
  interview,
  visible,
  onClose,
  onSuccess,
}) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const tCommon = useTranslations("common");

  const handleDelete = async () => {
    try {
      setLoading(true);
      await InterviewService.deleteInterview(interview.id);
      message.success("Interview deleted successfully");
      onClose();
      router.push("/admin/interviews");
      onSuccess();
    } catch (error) {
      console.error("Failed to delete interview:", error);
      message.error("Failed to delete interview");
    } finally {
      setLoading(false);
    }
  };

  return (
    <BaseModal
      title={tCommon("messages.confirm_delete")}
      isVisible={visible}
      onClose={onClose}
      footer={[
        <BaseButton key="cancel" onClick={onClose} label={tCommon("actions.cancel")} />,
        <BaseButton
          key="delete"
          type="primary"
          danger
          loading={loading}
          onClick={handleDelete}
          label={tCommon("actions.delete")}
        />,
      ]}
    >
      <p>
        {tCommon("messages.delete_confirmation_message")}
      </p>
      <p>
        {tCommon("messages.delete_confirmation_warning")}
      </p>
    </BaseModal>
  );
};

export default DeleteInterviewModal;
