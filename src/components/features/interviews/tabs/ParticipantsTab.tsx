"use client";
import { Interview } from "@/types/interview";
import {
  Avatar,
  Card,
  Col,
  Descriptions,
  Divider,
  Flex,
  Row,
  Space,
  Typography,
} from "antd";
import {
  MailOutlined,
  PhoneOutlined,
  MessageOutlined,
  UserOutlined,
  TeamOutlined,
} from "@ant-design/icons";
import React from "react";
import { useRouter } from "next/navigation";
import BaseButton from "@/components/ui/buttons/BaseButton";

const { Title, Text } = Typography;

interface ParticipantsTabProps {
  interview: Interview;
}

const ParticipantsTab: React.FC<ParticipantsTabProps> = ({ interview }) => {
  const router = useRouter();

  return (
    <Row gutter={[24, 24]}>
      {/* Candidate Card */}
      <Col span={12}>
        <Card title="Candidate Information">
          <Flex align="center" gap="middle">
            <Avatar
              size={64}
              src={interview.photoUrl}
              icon={<UserOutlined />}
            />
            <div>
              <Title level={5}>{interview.candidateName}</Title>
              <Text type="secondary">ID: {interview.candidateId}</Text>
            </div>
          </Flex>
          <Divider />
          <Descriptions layout="vertical" column={1}>
            <Descriptions.Item label="Contact">
              <Space>
                <BaseButton
                  icon={<MailOutlined />}
                  size="small"
                  label="Email"
                />
                <BaseButton
                  icon={<PhoneOutlined />}
                  size="small"
                  label="Call"
                />
                <BaseButton
                  icon={<MessageOutlined />}
                  size="small"
                  label="Message"
                />
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="View Profile">
              <BaseButton
                type="link"
                onClick={() =>
                  router.push(`/admin/users/${interview.candidateId}`)
                }
                label="View full candidate profile"
              />
            </Descriptions.Item>
          </Descriptions>
        </Card>
      </Col>

      {/* Employer Card */}
      <Col span={12}>
        <Card title="Employer Information">
          <Flex align="center" gap="middle">
            <Avatar size={64} icon={<TeamOutlined />} />
            <div>
              <Title level={5}>{interview.employerName}</Title>
              <Text type="secondary">ID: {interview.employerId}</Text>
            </div>
          </Flex>
          <Divider />
          <Descriptions layout="vertical" column={1}>
            <Descriptions.Item label="Contact">
              <Space>
                <BaseButton
                  icon={<MailOutlined />}
                  size="small"
                  label="Email"
                />
                <BaseButton
                  icon={<PhoneOutlined />}
                  size="small"
                  label="Call"
                />
                <BaseButton
                  icon={<MessageOutlined />}
                  size="small"
                  label="Message"
                />
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="View Profile">
              <BaseButton
                type="link"
                onClick={() =>
                  router.push(`/admin/users/${interview.employerId}`)
                }
                label="View full employer profile"
              />
            </Descriptions.Item>
          </Descriptions>
        </Card>
      </Col>
    </Row>
  );
};

export default ParticipantsTab;
