"use client";
import BaseDescription from "@/components/ui/descriptions/BaseDescription";
import { Interview, InterviewType } from "@/types/interview";
import dayjs from "dayjs";
import React from "react";
import { InterviewStatusTag, InterviewTypeTag } from "../InterviewStatusBadge";

interface GeneralInfoTabProps {
  interview: Interview;
}

const GeneralInfoTab: React.FC<GeneralInfoTabProps> = ({ interview }) => {
  // Prepare description items
  const descriptionItems = [
    {
      label: "Interview ID",
      value: interview.id,
    },
    {
      label: "Job Post",
      value: interview.jobPostTitle,
    },
    {
      label: "Scheduled Date",
      value: dayjs(interview.scheduledDate).format("MMMM D, YYYY h:mm A"),
    },
    {
      label: "Duration",
      value: `${interview.duration} minutes`,
    },
    {
      label: "Status",
      value: <InterviewStatusTag status={interview.status} />,
    },
    {
      label: "Type",
      value: <InterviewTypeTag type={interview.type} />,
    },
  ];

  // Add conditional items based on interview type
  if (interview.type === InterviewType.IN_PERSON) {
    descriptionItems.push({
      label: "Location",
      value: interview.location || "No location specified",
    });
  }

  if (interview.type === InterviewType.VIDEO_CALL) {
    descriptionItems.push({
      label: "Meeting Link",
      value: (
        <a
          href={interview.meetingLink}
          target="_blank"
          rel="noopener noreferrer"
        >
          {interview.meetingLink}
        </a>
      ),
    });
  }

  // Add created/updated timestamps
  descriptionItems.push(
    {
      label: "Created At",
      value: dayjs(interview.createdAt).format("MMMM D, YYYY h:mm A"),
    },
    {
      label: "Last Updated",
      value: dayjs(interview.updatedAt).format("MMMM D, YYYY h:mm A"),
    }
  );

  return (
    <BaseDescription
      items={descriptionItems}
      bordered
      column={1}
      layout="horizontal"
    />
  );
};

export default GeneralInfoTab;
