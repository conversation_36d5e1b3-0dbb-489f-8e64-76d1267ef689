"use client";
import {
  CANDIDATE_PREPARATION_TIPS,
  EMPLOYER_PREPARATION_TIPS,
} from "@/constants/interview";
import { Interview } from "@/types/interview";
import { Card, Divider, Flex, Typography } from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import React from "react";

const { Title, Paragraph } = Typography;

interface NotesTabProps {
  interview: Interview;
}

const NotesTab: React.FC<NotesTabProps> = ({ interview }) => {
  // Format preparation tips based on participant type
  const preparationTips = interview.candidateName
    ? CANDIDATE_PREPARATION_TIPS
    : EMPLOYER_PREPARATION_TIPS;

  return (
    <Card>
      {interview.notes ? (
        <div>
          <Title level={5}>Interview Notes</Title>
          <Paragraph>{interview.notes}</Paragraph>
        </div>
      ) : (
        <Flex vertical align="center" gap="middle">
          <InfoCircleOutlined style={{ fontSize: 48, color: "#faad14" }} />
          <Title level={4}>No Notes Available</Title>
          <Paragraph>
            There are no additional notes for this interview. You can add notes
            by editing the interview.
          </Paragraph>
        </Flex>
      )}

      <Divider />

      <Title level={5}>Preparation Tips</Title>
      <ul>
        {preparationTips.map((tip, index) => (
          <li key={index} className="mb-2">
            {tip}
          </li>
        ))}
      </ul>
    </Card>
  );
};

export default NotesTab;
