"use client";
import { Interview, InterviewStatus } from "@/types/interview";
import { Card, Descriptions, Flex, Rate, Tag, Typography } from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import React from "react";
import BaseButton from "@/components/ui/buttons/BaseButton";

const { Title, Paragraph } = Typography;

interface FeedbackResultsTabProps {
  interview: Interview;
  onAddFeedback: () => void;
}

const FeedbackResultsTab: React.FC<FeedbackResultsTabProps> = ({
  interview,
  onAddFeedback,
}) => {
  const isCompleted = interview.status === InterviewStatus.COMPLETED;

  if (interview.feedback) {
    return (
      <Card>
        <Descriptions bordered column={1}>
          <Descriptions.Item label="Overall Rating">
            <Rate disabled value={interview.feedback.rating || 0} />
          </Descriptions.Item>
          <Descriptions.Item label="Communication">
            <Rate disabled value={interview.feedback.communication || 0} />
          </Descriptions.Item>
          <Descriptions.Item label="Technical Skills">
            <Rate disabled value={interview.feedback.technicalSkills || 0} />
          </Descriptions.Item>
          <Descriptions.Item label="Cultural Fit">
            <Rate disabled value={interview.feedback.culturalFit || 0} />
          </Descriptions.Item>
          <Descriptions.Item label="Experience">
            <Rate disabled value={interview.feedback.experience || 0} />
          </Descriptions.Item>
          <Descriptions.Item label="Strengths">
            {interview.feedback.strengths?.map((strength) => (
              <Tag color="green" key={strength}>
                {strength}
              </Tag>
            )) || "None provided"}
          </Descriptions.Item>
          <Descriptions.Item label="Areas for Improvement">
            {interview.feedback.weaknesses?.map((weakness) => (
              <Tag color="orange" key={weakness}>
                {weakness}
              </Tag>
            )) || "None provided"}
          </Descriptions.Item>
          <Descriptions.Item label="Notes">
            {interview.feedback.notes || "None provided"}
          </Descriptions.Item>
          <Descriptions.Item label="Decision">
            {interview.feedback.decision === "Offer" && (
              <Tag color="green">Offer</Tag>
            )}
            {interview.feedback.decision === "Consider" && (
              <Tag color="blue">Consider</Tag>
            )}
            {interview.feedback.decision === "Reject" && (
              <Tag color="red">Reject</Tag>
            )}
            {!interview.feedback.decision && (
              <Tag color="default">No decision</Tag>
            )}
          </Descriptions.Item>
          {interview.feedback.decision === "Reject" && (
            <Descriptions.Item label="Rejection Reason">
              {interview.feedback.rejectionReason || "No reason provided"}
            </Descriptions.Item>
          )}
          {interview.feedback.decision === "Offer" &&
            interview.feedback.offerDetails && (
              <>
                <Descriptions.Item label="Offered Salary">
                  {interview.feedback.offerDetails.salary || "Not specified"}
                </Descriptions.Item>
                <Descriptions.Item label="Start Date">
                  {interview.feedback.offerDetails.startDate
                    ? dayjs(interview.feedback.offerDetails.startDate).format(
                        "MMMM D, YYYY"
                      )
                    : "Not specified"}
                </Descriptions.Item>
                <Descriptions.Item label="Offer Notes">
                  {interview.feedback.offerDetails.notes || "None provided"}
                </Descriptions.Item>
              </>
            )}
          <Descriptions.Item label="Submitted By">
            {interview.feedback.submittedBy || "Unknown"}
          </Descriptions.Item>
          <Descriptions.Item label="Submitted At">
            {interview.feedback.submittedAt
              ? dayjs(interview.feedback.submittedAt).format(
                  "MMMM D, YYYY h:mm A"
                )
              : "Unknown"}
          </Descriptions.Item>
        </Descriptions>
      </Card>
    );
  }

  return (
    <Card>
      <Flex vertical align="center" gap="middle">
        <InfoCircleOutlined style={{ fontSize: 48, color: "#1890ff" }} />
        <Title level={4}>No Feedback Provided Yet</Title>
        <Paragraph>
          This interview does not have feedback yet. You can add feedback once
          the interview is completed.
        </Paragraph>
        {isCompleted && (
          <BaseButton
            type="primary"
            onClick={onAddFeedback}
            label="Add Feedback"
          />
        )}
      </Flex>
    </Card>
  );
};

export default FeedbackResultsTab;
