import { Contract } from "@/types/contract";
import { Typography } from "antd";
import dayjs from "dayjs";
import React, { FC } from "react";

const { Text, Paragraph } = Typography;

interface ContractTabContentProps {
  contract: Contract;
}

const DetailTab: FC<ContractTabContentProps> = ({ contract }) => {
  return (
    <>
      <Paragraph>
        <Text strong>Contract Summary:</Text>
      </Paragraph>
      <Paragraph>
        This is a {contract.contractType} contract between{" "}
        {contract.employerName} (Employer) and {contract.jobSeekerName} (Job
        Seeker) that runs from{" "}
        {dayjs(contract.startDate).format("MMMM D, YYYY")} to{" "}
        {dayjs(contract.endDate).format("MMMM D, YYYY")}.
      </Paragraph>
      <Paragraph>
        The job seeker will work {contract.workingHoursPerWeek} hours per week
        at a rate of {contract.hourlyRate} per hour, paid{" "}
        {contract.paymentFrequency}.
      </Paragraph>
      <Paragraph>
        <Text strong>Additional Terms:</Text>
        <br />
        {contract.additionalTerms.description as string}
      </Paragraph>
    </>
  );
};

export default DetailTab;
