import PaymentModal from "@/components/features/contracts/modals/PaymentModal";
import BaseTable from "@/components/ui/tables/BaseTable";
import ContractService from "@/services/contractService";
import { Tag } from "antd";
import dayjs from "dayjs";
import { useParams } from "next/navigation";
import { useState } from "react";

const PaymentTab = () => {
  const { id } = useParams();
  const [visible, setVisible] = useState(false);

  const paymentColumns = [
    {
      title: "Date",
      dataIndex: "date",
      key: "date",
      render: (date: string) => dayjs(date).format("MMM D, YYYY"),
    },
    {
      title: "Amount",
      dataIndex: "amount",
      key: "amount",
      render: (amount: number) => <span>${amount.toFixed(2)}</span>,
    },
    {
      title: "Description",
      dataIndex: "description",
      key: "description",
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      render: (status: string) => {
        const colors: Record<string, string> = {
          pending: "processing",
          completed: "success",
          failed: "error",
        };
        return <Tag color={colors[status]}>{status.toUpperCase()}</Tag>;
      },
    },
  ];

  return (
    <>
      <BaseTable
        api={ContractService.getPayments}
        columns={paymentColumns}
        rowKey="id"
        createBtnText="Add Payment"
        showActions={false}
        onCreate={() => setVisible(true)}
      />

      <PaymentModal
        contractId={id as string}
        visible={visible}
        setVisible={setVisible}
      />
    </>
  );
};

export default PaymentTab;
