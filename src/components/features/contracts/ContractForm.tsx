"use client";
import React, { useState, useEffect } from "react";
import {
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Button,
  Card,
  Row,
  Col,
  Typography,
  Space,
  message,
  Checkbox,
} from "antd";
import {
  UserOutlined,
  CalendarOutlined,
  DollarOutlined,
  FileTextOutlined,
  SaveOutlined,
  CloseOutlined,
} from "@ant-design/icons";
import { useRouter } from "next/navigation";
import dayjs from "dayjs";

import ContractService from "@/services/contractService";
import {
  Contract,
  contractTypeOptions,
  paymentFrequencyOptions,
  workDayOptions,
} from "@/types/contract";
import { UserRole } from "@/constants/userRole";
import DebounceSelect from "@/components/ui/selects/DebounceSelect";
import UserService from "@/services/userService";

const { Title } = Typography;
const { TextArea } = Input;

interface ContractFormProps {
  contract?: Contract;
  userRole?: UserRole;
}

const ContractForm: React.FC<ContractFormProps> = ({
  contract,
  userRole = UserRole.ADMIN,
}) => {
  const router = useRouter();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [employerOptions, setEmployerOptions] = useState<any[]>([]);
  const [jobSeekerOptions, setJobSeekerOptions] = useState<any[]>([]);
  const isEditing = !!contract;

  // Load initial options for edit mode
  useEffect(() => {
    if (isEditing && contract) {
      // Set initial employer option
      if (contract.employerId && contract.employerName) {
        setEmployerOptions([
          {
            label: `${contract.employerName} (ID: ${contract.employerId})`,
            value: contract.employerId,
          },
        ]);
      }

      // Set initial job seeker option
      if (contract.jobSeekerId && contract.jobSeekerName) {
        setJobSeekerOptions([
          {
            label: `${contract.jobSeekerName} (ID: ${contract.jobSeekerId})`,
            value: contract.jobSeekerId,
          },
        ]);
      }
    }
  }, [isEditing, contract]);

  // Reset form when contract data changes
  useEffect(() => {
    if (contract && isEditing) {
      form.setFieldsValue({
        ...contract,
        startDate: dayjs(contract.startDate),
        endDate: dayjs(contract.endDate),
        employerId: contract.employerId,
        jobSeekerId: contract.jobSeekerId,
      });
    }
  }, [contract, form, isEditing]);

  // Fetch options for dropdowns
  const fetchEmployerOptions = async (search: string) => {
    try {
      const response = await UserService.getList({
        role: UserRole.EMPLOYER,
        search,
        page: 0,
        limit: 20,
      });
      return {
        data: response.data,
      };
    } catch (error) {
      console.error("Error fetching employers:", error);
      return [];
    }
  };

  const fetchJobSeekerOptions = async (search: string) => {
    try {
      const response = await UserService.getList({
        role: UserRole.JOB_SEEKER,
        search,
        page: 0,
        limit: 20,
      });
      return {
        data: response.data,
      };
    } catch (error) {
      console.error("Error fetching job seekers:", error);
      return [];
    }
  };

  const initialValues = contract
    ? {
        ...contract,
        startDate: dayjs(contract.startDate),
        endDate: dayjs(contract.endDate),
        employerId: contract.employerId,
        jobSeekerId: contract.jobSeekerId,
      }
    : {
        contractType: "part-time",
        paymentFrequency: "biweekly",
        workDays: ["monday", "tuesday", "wednesday", "thursday", "friday"],
        workingHoursPerWeek: 40,
        isFeePaid: false,
        status: "draft",
      };

  const handleSubmit = async (values: any) => {
    console.log("🚀 ~ handleSubmit ~ values:", values);
    try {
      setLoading(true);

      // Transform form values to match backend API
      const contractData = {
        title: values.title,
        employerId: Number(values.employerId),
        jobSeekerId: Number(values.jobSeekerId),
        startDate: values.startDate.format("YYYY-MM-DD"),
        endDate: values.endDate.format("YYYY-MM-DD"),
        hourlyRate: Number(values.hourlyRate),
        paymentFrequency: values.paymentFrequency,
        workingHoursPerWeek: Number(values.workingHoursPerWeek),
        workDays: values.workDays || [],
        contractType: values.contractType,
        status: values.status || "draft",
        additionalTerms: values.additionalTerms || "", // Send as string
      };

      if (isEditing && contract) {
        await ContractService.updateContract(String(contract.id), contractData);
        message.success("Contract updated successfully!");
      } else {
        await ContractService.createContract(contractData);
        message.success("Contract created successfully!");
      }

      // Navigate back to contracts list based on user role
      if (userRole === UserRole.EMPLOYER) {
        router.push("/employers-management/contracts");
      } else if (userRole === UserRole.JOB_SEEKER) {
        router.push("/job-seekers-management/contracts");
      }
    } catch (error) {
      console.error("Error saving contract:", error);
      message.error("Failed to save contract");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    if (userRole === UserRole.EMPLOYER) {
      router.push("/employers-management/contracts");
    } else if (userRole === UserRole.JOB_SEEKER) {
      router.push("/job-seekers-management/contracts");
    } else {
      router.push("/_features/contracts");
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={3} style={{ margin: 0 }}>
              <FileTextOutlined />{" "}
              {isEditing ? "Edit Contract" : "Create New Contract"}
            </Title>
          </Col>
        </Row>
      </Card>

      <Form
        form={form}
        layout="vertical"
        initialValues={initialValues}
        onFinish={handleSubmit}
      >
        {/* Basic Information */}
        <Card
          title={
            <Title level={4} style={{ margin: 0 }}>
              <UserOutlined /> Basic Information
            </Title>
          }
          style={{ marginBottom: 24 }}
        >
          <Form.Item
            label="Contract Title"
            name="title"
            rules={[{ required: true, message: "Please enter contract title" }]}
          >
            <Input placeholder="e.g., Web Development Contract" size="large" />
          </Form.Item>

          <Row gutter={16}>
            <Col xs={24} md={12}>
              <Form.Item
                label="Contract Type"
                name="contractType"
                rules={[
                  { required: true, message: "Please select contract type" },
                ]}
              >
                <Select options={contractTypeOptions} size="large" />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item label="Status" name="status">
                <Select
                  options={[
                    { value: "draft", label: "Draft" },
                    { value: "offered", label: "Offered" },
                    { value: "active", label: "Active" },
                    { value: "completed", label: "Completed" },
                    { value: "terminated", label: "Terminated" },
                  ]}
                  size="large"
                />
              </Form.Item>
            </Col>
          </Row>

          <DebounceSelect
            name="employerId"
            label="Employer"
            fetchOptions={fetchEmployerOptions}
            placeholder="Search and select an employer..."
            required
            disabled={isEditing}
            options={isEditing ? employerOptions : undefined}
          />

          <DebounceSelect
            name="jobSeekerId"
            label="Job Seeker"
            fetchOptions={fetchJobSeekerOptions}
            placeholder="Search and select a job seeker..."
            required
            disabled={isEditing}
            options={isEditing ? jobSeekerOptions : undefined}
          />
        </Card>

        {/* Contract Duration */}
        <Card
          title={
            <Title level={4} style={{ margin: 0 }}>
              <CalendarOutlined /> Contract Duration
            </Title>
          }
          style={{ marginBottom: 24 }}
        >
          <Row gutter={16}>
            <Col xs={24} md={12}>
              <Form.Item
                label="Start Date"
                name="startDate"
                rules={[
                  { required: true, message: "Please select start date" },
                ]}
              >
                <DatePicker
                  style={{ width: "100%" }}
                  format="YYYY-MM-DD"
                  size="large"
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                label="End Date"
                name="endDate"
                rules={[{ required: true, message: "Please select end date" }]}
              >
                <DatePicker
                  style={{ width: "100%" }}
                  format="YYYY-MM-DD"
                  size="large"
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Payment Information */}
        <Card
          title={
            <Title level={4} style={{ margin: 0 }}>
              <DollarOutlined /> Payment Information
            </Title>
          }
          style={{ marginBottom: 24 }}
        >
          <Row gutter={16}>
            <Col xs={24} md={12}>
              <Form.Item
                label="Hourly Rate ($)"
                name="hourlyRate"
                rules={[
                  { required: true, message: "Please enter hourly rate" },
                ]}
              >
                <InputNumber
                  min={0}
                  step={0.01}
                  style={{ width: "100%" }}
                  size="large"
                  placeholder="e.g., 25.00"
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                label="Payment Frequency"
                name="paymentFrequency"
                rules={[
                  {
                    required: true,
                    message: "Please select payment frequency",
                  },
                ]}
              >
                <Select options={paymentFrequencyOptions} size="large" />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Work Schedule */}
        <Card
          title={
            <Title level={4} style={{ margin: 0 }}>
              <CalendarOutlined /> Work Schedule
            </Title>
          }
          style={{ marginBottom: 24 }}
        >
          <Form.Item
            label="Working Hours Per Week"
            name="workingHoursPerWeek"
            rules={[
              {
                required: true,
                message: "Please enter working hours per week",
              },
            ]}
          >
            <InputNumber
              min={1}
              max={168}
              style={{ width: "100%" }}
              size="large"
              placeholder="e.g., 40"
            />
          </Form.Item>

          <Form.Item label="Work Days" name="workDays">
            <Checkbox.Group options={workDayOptions} />
          </Form.Item>
        </Card>

        {/* Additional Information */}
        <Card
          title={
            <Title level={4} style={{ margin: 0 }}>
              <FileTextOutlined /> Additional Information
            </Title>
          }
          style={{ marginBottom: 24 }}
        >
          <Form.Item label="Additional Terms" name="additionalTerms">
            <TextArea
              rows={4}
              placeholder="Enter any additional terms or conditions..."
              showCount
              maxLength={1000}
            />
          </Form.Item>
        </Card>

        {/* Save Button */}
        <Card>
          <Row justify="center">
            <Space size="large">
              <Button
                size="large"
                onClick={handleCancel}
                icon={<CloseOutlined />}
              >
                Cancel
              </Button>
              <Button
                type="primary"
                size="large"
                icon={<SaveOutlined />}
                htmlType="submit"
                loading={loading}
              >
                {isEditing ? "Update Contract" : "Create Contract"}
              </Button>
            </Space>
          </Row>
        </Card>
      </Form>
    </div>
  );
};

export default ContractForm;
