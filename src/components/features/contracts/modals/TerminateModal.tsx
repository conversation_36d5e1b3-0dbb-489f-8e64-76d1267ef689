"use client";
import React from "react";
import { Form } from "antd";
import { FormInstance } from "antd/lib/form";
import BaseTextArea from "@/components/ui/inputs/BaseTextArea";
import BaseModal from "@/components/ui/modals/BaseModal";

interface TerminateModalProps {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  form: FormInstance;
  loading: boolean;
  onSubmit: () => Promise<void>;
}

const TerminateModal: React.FC<TerminateModalProps> = ({
  visible,
  setVisible,
  form,
  loading,
  onSubmit,
}) => {
  return (
    <BaseModal
      title="Terminate Contract"
      isVisible={visible}
      onClose={() => setVisible(false)}
      onSubmit={onSubmit}
      submitText="Terminate Contract"
      confirmLoading={loading}
    >
      <Form form={form} layout="vertical" onFinish={onSubmit}>
        <BaseTextArea
          name="reason"
          label="Reason for Termination"
          required={true}
          rows={4}
          placeholder="Enter reason for terminating the contract"
        />
      </Form>
    </BaseModal>
  );
};

export default TerminateModal;
