"use client";
import BaseDatePicker from "@/components/ui/datepickers/BaseDatePicker";
import BaseInputNumber from "@/components/ui/inputs/BaseInputNumber";
import BaseTextArea from "@/components/ui/inputs/BaseTextArea";
import BaseModal from "@/components/ui/modals/BaseModal";
import { Form } from "antd";
import React, { useState } from "react";

interface TimeEntryModalProps {
  contractId: string;
  visible: boolean;
  setVisible: (visible: boolean) => void;
}

const TimeEntryModal: React.FC<TimeEntryModalProps> = ({
  contractId,
  visible,
  setVisible,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = () => {
    setLoading(true);
    console.log("Submit: ", contractId);
    setVisible(false);
    setLoading(false);
  };

  return (
    <BaseModal
      title="Add Time Entry"
      isVisible={visible}
      onClose={() => setVisible(false)}
      onSubmit={handleSubmit}
      submitText="Submit"
      confirmLoading={loading}
    >
      <Form form={form} layout="vertical">
        <BaseDatePicker name="date" label="Date" required={true} />

        <BaseInputNumber
          name="hoursWorked"
          label="Hours Worked"
          min={0.5}
          step={0.5}
          required={true}
          placeholder="Enter hours worked"
        />

        <BaseTextArea
          name="description"
          label="Description"
          required={true}
          rows={4}
          placeholder="Enter description of work performed"
        />
      </Form>
    </BaseModal>
  );
};

export default TimeEntryModal;
