"use client";

import BaseTable, { BaseTableRef } from "@/components/ui/tables/BaseTable";
import { Wallet } from "@/types/wallet";
import { Avatar, Space, Typography, Tag, Button, Modal } from "antd";
import { EyeOutlined } from "@ant-design/icons";
import { useTranslations } from "next-intl";
import {
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
  useContext,
} from "react";
import WalletDetail from "./WalletDetail";
import { AuthContext } from "@/contexts/AuthContext";
import { PermissionEnum } from "@/constants/_permissionEnum";

const { Text } = Typography;

interface WalletTableProps {
  api: (params?: Record<string, unknown>) => Promise<any>;
  tTitle: string;
}

const WalletTable = forwardRef<BaseTableRef, WalletTableProps>(
  ({ api, tTitle }, ref) => {
    const tCommon = useTranslations("common");
    const tWallet = useTranslations("wallet");
    const tableRef = useRef<BaseTableRef>(null);
    const [selectedWallet, setSelectedWallet] = useState<Wallet | null>(null);
    const [detailModalVisible, setDetailModalVisible] = useState(false);
    const authContext = useContext(AuthContext);
    const access = authContext?.access;

    useImperativeHandle(ref, () => ({
      refetch: () => tableRef.current?.refetch(),
    }));

    const handleViewDetail = (wallet: Wallet) => {
      setSelectedWallet(wallet);
      setDetailModalVisible(true);
    };

    const handleCloseDetail = () => {
      setDetailModalVisible(false);
      setSelectedWallet(null);
    };

    const columns = [
      {
        title: tCommon("fields.id"),
        dataIndex: "id",
        key: "id",
        width: 80,
      },
      {
        title: tCommon("fields.user"),
        key: "user",
        render: (_: unknown, record: Wallet) => (
          <Space>
            <Avatar
              src={record.user.profilePicture || "/user/default-avatar.svg"}
              alt={record.user.name}
              size="large"
            >
              {record.user.name ? record.user.name[0].toUpperCase() : "U"}
            </Avatar>
            <div>
              <Text strong>{record.user.name}</Text>
              <div>
                <Text type="secondary">{record.user.email}</Text>
              </div>
              <div>
                <Text type="secondary">{record.user.phoneNumber}</Text>
              </div>
            </div>
          </Space>
        ),
      },

      {
        title: tWallet("points"),
        dataIndex: "point",
        key: "point",
        width: 120,
        render: (point: number) => (
          <Tag color={point > 500 ? "green" : point > 100 ? "orange" : "red"}>
            {point.toLocaleString()} {tWallet("points_unit")}
          </Tag>
        ),
        sorter: (a: Wallet, b: Wallet) => a.point - b.point,
      },
      {
        title: tCommon("actions.actions"),
        key: "actions",
        width: 120,
        render: (_: unknown, record: Wallet) => (
          <Space>
            {access?.[PermissionEnum.WALLET_READ] && (
              <Button
                type="primary"
                size="small"
                icon={<EyeOutlined />}
                onClick={() => handleViewDetail(record)}
              >
                {tCommon("actions.view")}
              </Button>
            )}
          </Space>
        ),
      },
    ];

    return (
      <>
        <BaseTable<Wallet>
          ref={tableRef}
          api={api}
          columns={columns}
          rowKey="id"
          title={tTitle}
          showSearch={true}
          searchPlaceholder={tWallet("search_placeholder")}
          showActions={false}
        />

        {/* Wallet Detail Modal */}
        <Modal
          title={`Wallet Details - ${selectedWallet?.user.name}`}
          open={detailModalVisible}
          onCancel={handleCloseDetail}
          footer={null}
          width={1200}
          style={{ top: 20 }}
        >
          {selectedWallet && (
            <WalletDetail
              userId={selectedWallet.user.id}
              onClose={handleCloseDetail}
            />
          )}
        </Modal>
      </>
    );
  }
);

WalletTable.displayName = "WalletTable";

export default WalletTable;
