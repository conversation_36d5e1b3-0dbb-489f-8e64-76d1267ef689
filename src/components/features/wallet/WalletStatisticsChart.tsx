"use client";

import React from "react";
import {
  Card,
  Row,
  Col,
  Progress,
  Statistic,
  Typography,
  Space,
  Tag,
} from "antd";
import {
  PieChartOutlined,
  BarChartOutlined,
  TrophyOutlined,
  DollarOutlined,
  TransactionOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import { AdminWalletResponse } from "@/types/wallet";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";

dayjs.extend(relativeTime);

const { Title, Text } = Typography;

interface WalletStatisticsChartProps {
  wallet: AdminWalletResponse;
}

const WalletStatisticsChart: React.FC<WalletStatisticsChartProps> = ({
  wallet,
}) => {
  const stats = wallet.statistics;

  if (!stats) {
    return (
      <Card>
        <div style={{ textAlign: "center", padding: "50px" }}>
          <Text type="secondary">No statistics available</Text>
        </div>
      </Card>
    );
  }

  // Calculate percentages and ratios
  const totalPoints =
    (stats.totalPointsEarned || 0) + (stats.totalPointsSpent || 0);
  const earnedPercentage =
    totalPoints > 0 ? ((stats.totalPointsEarned || 0) / totalPoints) * 100 : 0;
  const spentPercentage =
    totalPoints > 0 ? ((stats.totalPointsSpent || 0) / totalPoints) * 100 : 0;

  const totalPayments = stats.totalPaymentRequests || 0;
  const successRate =
    totalPayments > 0
      ? ((stats.successfulPaymentRequests || 0) / totalPayments) * 100
      : 0;
  const pendingRate =
    totalPayments > 0
      ? ((stats.pendingPaymentRequests || 0) / totalPayments) * 100
      : 0;
  const failedRate =
    totalPayments > 0
      ? ((stats.failedPaymentRequests || 0) / totalPayments) * 100
      : 0;

  const getTransactionTypeDisplay = (type: string) => {
    const typeMap: Record<
      string,
      { label: string; color: string; icon: React.ReactNode }
    > = {
      ADD_MONEY_TO_WALLET: {
        label: "Add Money",
        color: "green",
        icon: <DollarOutlined />,
      },
      REDEEM_POINTS: {
        label: "Redeem Points",
        color: "red",
        icon: <TrophyOutlined />,
      },
      ADMIN_ADJUSTMENT_ADD: {
        label: "Admin Add",
        color: "blue",
        icon: <CheckCircleOutlined />,
      },
      ADMIN_ADJUSTMENT_SUBTRACT: {
        label: "Admin Subtract",
        color: "orange",
        icon: <ExclamationCircleOutlined />,
      },
      REWARD: { label: "Reward", color: "gold", icon: <TrophyOutlined /> },
      PURCHASE: {
        label: "Purchase",
        color: "purple",
        icon: <TransactionOutlined />,
      },
    };

    const config = typeMap[type] || {
      label: type,
      color: "default",
      icon: <TransactionOutlined />,
    };
    return (
      <Space>
        {config.icon}
        <Tag color={config.color}>{config.label}</Tag>
      </Space>
    );
  };

  return (
    <div>
      {/* Points Overview */}
      <Row gutter={[16, 16]} className="mb-4">
        <Col span={12}>
          <Card
            title={
              <Space>
                <PieChartOutlined />
                Points Distribution
              </Space>
            }
          >
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="Points Earned"
                  value={stats.totalPointsEarned || 0}
                  valueStyle={{ color: "#52c41a" }}
                />
                <Progress
                  percent={Math.round(earnedPercentage)}
                  strokeColor="#52c41a"
                  showInfo={false}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="Points Spent"
                  value={stats.totalPointsSpent || 0}
                  valueStyle={{ color: "#ff4d4f" }}
                />
                <Progress
                  percent={Math.round(spentPercentage)}
                  strokeColor="#ff4d4f"
                  showInfo={false}
                />
              </Col>
            </Row>
            <div className="mt-4">
              <Text strong>Net Points: </Text>
              <Text
                style={{
                  color:
                    (stats.totalPointsEarned || 0) -
                      (stats.totalPointsSpent || 0) >=
                    0
                      ? "#52c41a"
                      : "#ff4d4f",
                }}
              >
                {(
                  (stats.totalPointsEarned || 0) - (stats.totalPointsSpent || 0)
                ).toLocaleString()}
              </Text>
            </div>
          </Card>
        </Col>

        <Col span={12}>
          <Card
            title={
              <Space>
                <BarChartOutlined />
                Payment Success Rate
              </Space>
            }
          >
            <div className="mb-3">
              <div className="flex justify-between items-center mb-1">
                <Text>Successful</Text>
                <Text strong style={{ color: "#52c41a" }}>
                  {stats.successfulPaymentRequests || 0}
                </Text>
              </div>
              <Progress
                percent={Math.round(successRate)}
                strokeColor="#52c41a"
                showInfo={true}
              />
            </div>

            <div className="mb-3">
              <div className="flex justify-between items-center mb-1">
                <Text>Pending</Text>
                <Text strong style={{ color: "#faad14" }}>
                  {stats.pendingPaymentRequests || 0}
                </Text>
              </div>
              <Progress
                percent={Math.round(pendingRate)}
                strokeColor="#faad14"
                showInfo={true}
              />
            </div>

            <div>
              <div className="flex justify-between items-center mb-1">
                <Text>Failed</Text>
                <Text strong style={{ color: "#ff4d4f" }}>
                  {stats.failedPaymentRequests || 0}
                </Text>
              </div>
              <Progress
                percent={Math.round(failedRate)}
                strokeColor="#ff4d4f"
                showInfo={true}
              />
            </div>
          </Card>
        </Col>
      </Row>

      {/* Detailed Statistics */}
      <Row gutter={[16, 16]} className="mb-4">
        <Col span={8}>
          <Card>
            <Statistic
              title="Total Transactions"
              value={stats.totalTransactions || 0}
              prefix={<TransactionOutlined />}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="Total Payment Requests"
              value={stats.totalPaymentRequests || 0}
              prefix={<DollarOutlined />}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="Total Amount Paid"
              value={stats.totalAmountPaid || 0}
              prefix="$"
              precision={2}
              valueStyle={{ color: "#1890ff" }}
            />
          </Card>
        </Col>
      </Row>

      {/* Activity Timeline */}
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card title="Recent Activity">
            <Space direction="vertical" style={{ width: "100%" }}>
              {stats.lastTransactionDate && (
                <div>
                  <Text strong>Last Transaction:</Text>
                  <br />
                  <Space>
                    <ClockCircleOutlined />
                    <Text>
                      {dayjs(stats.lastTransactionDate).format(
                        "YYYY-MM-DD HH:mm:ss"
                      )}
                    </Text>
                  </Space>
                  <br />
                  <Text type="secondary">
                    {dayjs(stats.lastTransactionDate).fromNow()}
                  </Text>
                </div>
              )}

              {stats.lastPaymentDate && (
                <div>
                  <Text strong>Last Payment:</Text>
                  <br />
                  <Space>
                    <DollarOutlined />
                    <Text>
                      {dayjs(stats.lastPaymentDate).format(
                        "YYYY-MM-DD HH:mm:ss"
                      )}
                    </Text>
                  </Space>
                  <br />
                  <Text type="secondary">
                    {dayjs(stats.lastPaymentDate).fromNow()}
                  </Text>
                </div>
              )}

              {!stats.lastTransactionDate && !stats.lastPaymentDate && (
                <Text type="secondary">No recent activity</Text>
              )}
            </Space>
          </Card>
        </Col>

        <Col span={12}>
          <Card title="Transaction Preferences">
            {stats.mostUsedTransactionType ? (
              <div>
                <Text strong>Most Used Transaction Type:</Text>
                <br />
                <div className="mt-2">
                  {getTransactionTypeDisplay(stats.mostUsedTransactionType)}
                </div>
                <div className="mt-3">
                  <Text type="secondary">
                    This user frequently uses this type of transaction
                  </Text>
                </div>
              </div>
            ) : (
              <Text type="secondary">
                No transaction preference data available
              </Text>
            )}
          </Card>
        </Col>
      </Row>

      {/* Wallet Health Score */}
      <Card className="mt-4" title="Wallet Health Score">
        <Row gutter={16}>
          <Col span={8}>
            <div className="text-center">
              <Title level={4}>Activity Score</Title>
              <Progress
                type="circle"
                percent={Math.min(
                  100,
                  Math.round(((stats.totalTransactions || 0) / 50) * 100)
                )}
                strokeColor={{
                  "0%": "#108ee9",
                  "100%": "#87d068",
                }}
              />
              <div className="mt-2">
                <Text type="secondary">Based on transaction volume</Text>
              </div>
            </div>
          </Col>

          <Col span={8}>
            <div className="text-center">
              <Title level={4}>Payment Reliability</Title>
              <Progress
                type="circle"
                percent={Math.round(successRate)}
                strokeColor={{
                  "0%": "#ff4d4f",
                  "50%": "#faad14",
                  "100%": "#52c41a",
                }}
              />
              <div className="mt-2">
                <Text type="secondary">Payment success rate</Text>
              </div>
            </div>
          </Col>

          <Col span={8}>
            <div className="text-center">
              <Title level={4}>Balance Health</Title>
              <Progress
                type="circle"
                percent={Math.min(100, Math.round((wallet.point / 1000) * 100))}
                strokeColor={{
                  "0%": "#ff4d4f",
                  "30%": "#faad14",
                  "70%": "#52c41a",
                }}
              />
              <div className="mt-2">
                <Text type="secondary">Current balance status</Text>
              </div>
            </div>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default WalletStatisticsChart;
