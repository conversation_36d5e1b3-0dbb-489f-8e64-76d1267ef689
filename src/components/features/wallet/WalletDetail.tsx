"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  Row,
  Col,
  Statistic,
  Avatar,
  Typography,
  Tag,
  Button,
  Modal,
  Form,
  InputNumber,
  Input,
  message,
  Spin,
  Alert,
  Space,
  Tabs,
} from "antd";
import {
  WalletOutlined,
  UserOutlined,
  TransactionOutlined,
  PlusOutlined,
  MinusOutlined,
  ReloadOutlined,
  CalendarOutlined,
  DollarOutlined,
  TrophyOutlined,
  ShoppingCartOutlined,
} from "@ant-design/icons";
import { AdminWalletResponse } from "@/types/wallet";
import {
  getWalletDetail,
  getWalletByUserId,
  adjustWalletPoints,
} from "@/services/walletService";
// import WalletTransactionHistory from "./WalletTransactionHistory";
// import WalletStatisticsChart from "./WalletStatisticsChart";
import dayjs from "dayjs";
import { useTranslations } from "next-intl";

const { Title, Text } = Typography;
const { TabPane } = Tabs;

interface WalletDetailProps {
  walletId?: number;
  userId?: number;
  onClose?: () => void;
}

const WalletDetail: React.FC<WalletDetailProps> = ({
  walletId,
  userId,
  onClose,
}) => {
  const [wallet, setWallet] = useState<AdminWalletResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [adjustModalVisible, setAdjustModalVisible] = useState(false);
  const [adjusting, setAdjusting] = useState(false);
  const [form] = Form.useForm();
  const t = useTranslations("wallet");

  const fetchWalletDetail = async () => {
    try {
      setLoading(true);
      let response: AdminWalletResponse;

      if (walletId) {
        response = await getWalletDetail(walletId);
      } else if (userId) {
        response = await getWalletByUserId(userId);
      } else {
        throw new Error("Either walletId or userId must be provided");
      }

      setWallet(response);
    } catch (error) {
      console.error("Failed to fetch wallet detail:", error);
      message.error("Failed to load wallet details");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (walletId || userId) {
      fetchWalletDetail();
    }
  }, [walletId, userId]);

  const handleAdjustPoints = async (values: {
    points: number;
    reason: string;
  }) => {
    if (!wallet) return;

    try {
      setAdjusting(true);
      await adjustWalletPoints(wallet.id, values.points, values.reason);
      message.success(t("wallet_points_adjusted_successfully"));
      setAdjustModalVisible(false);
      form.resetFields();
      await fetchWalletDetail(); // Refresh data
    } catch (error) {
      console.error("Failed to adjust wallet points:", error);
      message.error(t("failed_to_adjust_wallet_points"));
    } finally {
      setAdjusting(false);
    }
  };

  const getPointsColor = (points: number) => {
    if (points >= 1000) return "green";
    if (points >= 500) return "orange";
    return "red";
  };

  const getTransactionTypeIcon = (type: string) => {
    switch (type) {
      case "ADD_MONEY_TO_WALLET":
        return <PlusOutlined style={{ color: "#52c41a" }} />;
      case "REDEEM_POINTS":
        return <MinusOutlined style={{ color: "#ff4d4f" }} />;
      case "REWARD":
        return <TrophyOutlined style={{ color: "#faad14" }} />;
      case "PURCHASE":
        return <ShoppingCartOutlined style={{ color: "#1890ff" }} />;
      default:
        return <TransactionOutlined />;
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: "center", padding: "50px" }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!wallet) {
    return (
      <Alert
        message="Wallet Not Found"
        description="The requested wallet could not be found."
        type="error"
        showIcon
      />
    );
  }

  return (
    <div>
      {/* Header */}
      <Card className="mb-4">
        <Row align="middle" justify="space-between">
          <Col>
            <Space size="large">
              <Avatar
                size={64}
                src={wallet.user.photoUrl}
                icon={<UserOutlined />}
              >
                {wallet.user.name?.[0]?.toUpperCase()}
              </Avatar>
              <div>
                <Title level={3} style={{ margin: 0 }}>
                  {wallet.user.name}
                </Title>
                <Text type="secondary">{wallet.user.email}</Text>
                <br />
                <Text type="secondary">{wallet.user.phone}</Text>
              </div>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchWalletDetail}
                loading={loading}
              >
                {t("refresh")}
              </Button>
              <Button
                type="primary"
                icon={<WalletOutlined />}
                onClick={() => setAdjustModalVisible(true)}
              >
                {t("adjust_points")}
              </Button>
              {onClose && <Button onClick={onClose}>{t("close")}</Button>}
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Wallet Overview */}
      <Row gutter={[16, 16]} className="mb-4">
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title={t("current_balance")}
              value={wallet.point}
              prefix={<WalletOutlined />}
              valueStyle={{ color: getPointsColor(wallet.point) }}
              suffix={
                <Tag color={getPointsColor(wallet.point)}>{t("points")}</Tag>
              }
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title={t("transaction_history")}
              value={wallet.statistics?.totalTransactions || 0}
              prefix={<TransactionOutlined />}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title={t("total_earned")}
              value={wallet.statistics?.totalPointsEarned || 0}
              prefix={<PlusOutlined style={{ color: "#52c41a" }} />}
              valueStyle={{ color: "#52c41a" }}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title={t("total_spent")}
              value={wallet.statistics?.totalPointsSpent || 0}
              prefix={<MinusOutlined style={{ color: "#ff4d4f" }} />}
              valueStyle={{ color: "#ff4d4f" }}
            />
          </Card>
        </Col>
      </Row>

      {/* Payment Statistics */}
      {wallet.statistics && (
        <Row gutter={[16, 16]} className="mb-4">
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="Total Payments"
                value={wallet.statistics.totalPaymentRequests}
                prefix={<DollarOutlined />}
              />
            </Card>
          </Col>

          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="Amount Paid"
                value={wallet.statistics.totalAmountPaid}
                prefix="$"
                precision={2}
                valueStyle={{ color: "#1890ff" }}
              />
            </Card>
          </Col>

          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="Successful Payments"
                value={wallet.statistics.successfulPaymentRequests}
                prefix={<TrophyOutlined style={{ color: "#52c41a" }} />}
                valueStyle={{ color: "#52c41a" }}
              />
            </Card>
          </Col>

          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title={t("pending_payments")}
                value={wallet.statistics.pendingPaymentRequests}
                prefix={<CalendarOutlined style={{ color: "#faad14" }} />}
                valueStyle={{ color: "#faad14" }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* Detailed Information */}
      <Card>
        <Tabs defaultActiveKey="transactions">
          <TabPane tab="Transaction History" key="transactions">
            <div>Transaction History - Coming Soon</div>
          </TabPane>

          <TabPane tab="Statistics" key="statistics">
            <div>Statistics - Coming Soon</div>
          </TabPane>

          <TabPane tab="Wallet Info" key="info">
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Card title="Wallet Information" size="small">
                  <p>
                    <strong>Wallet ID:</strong> {wallet.id}
                  </p>
                  <p>
                    <strong>Created:</strong>{" "}
                    {dayjs(wallet.createdAt).format("YYYY-MM-DD HH:mm:ss")}
                  </p>
                  {wallet.lastModifiedAt && (
                    <p>
                      <strong>Last Modified:</strong>{" "}
                      {dayjs(wallet.lastModifiedAt).format(
                        "YYYY-MM-DD HH:mm:ss"
                      )}
                    </p>
                  )}
                  {wallet.createdBy && (
                    <p>
                      <strong>Created By:</strong> {wallet.createdBy}
                    </p>
                  )}
                  {wallet.lastModifiedBy && (
                    <p>
                      <strong>Last Modified By:</strong> {wallet.lastModifiedBy}
                    </p>
                  )}
                </Card>
              </Col>

              <Col span={12}>
                <Card title="Activity Summary" size="small">
                  {wallet.statistics?.lastTransactionDate && (
                    <p>
                      <strong>Last Transaction:</strong>{" "}
                      {dayjs(wallet.statistics.lastTransactionDate).format(
                        "YYYY-MM-DD HH:mm:ss"
                      )}
                    </p>
                  )}
                  {wallet.statistics?.lastPaymentDate && (
                    <p>
                      <strong>Last Payment:</strong>{" "}
                      {dayjs(wallet.statistics.lastPaymentDate).format(
                        "YYYY-MM-DD HH:mm:ss"
                      )}
                    </p>
                  )}
                  {wallet.statistics?.mostUsedTransactionType && (
                    <p>
                      <strong>Most Used Transaction:</strong>{" "}
                      <Space>
                        {getTransactionTypeIcon(
                          wallet.statistics.mostUsedTransactionType
                        )}
                        {wallet.statistics.mostUsedTransactionType}
                      </Space>
                    </p>
                  )}
                </Card>
              </Col>
            </Row>
          </TabPane>
        </Tabs>
      </Card>

      {/* Adjust Points Modal */}
      <Modal
        title={t("adjust_points")}
        open={adjustModalVisible}
        onCancel={() => {
          setAdjustModalVisible(false);
          form.resetFields();
        }}
        footer={null}
      >
        <Form form={form} layout="vertical" onFinish={handleAdjustPoints}>
          <Form.Item
            name="points"
            label="Points to Add/Subtract"
            rules={[
              { required: true, message: "Please enter points amount" },
              { type: "number", message: "Please enter a valid number" },
            ]}
            help="Use positive numbers to add points, negative numbers to subtract points"
          >
            <InputNumber
              style={{ width: "100%" }}
              placeholder="e.g., 100 or -50"
              min={-wallet.point}
            />
          </Form.Item>

          <Form.Item
            name="reason"
            label="Reason for Adjustment"
            rules={[
              { required: true, message: "Please provide a reason" },
              { min: 5, message: "Reason must be at least 5 characters" },
            ]}
          >
            <Input.TextArea
              rows={3}
              placeholder="Explain why you are adjusting the points..."
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={adjusting}>
                Adjust Points
              </Button>
              <Button
                onClick={() => {
                  setAdjustModalVisible(false);
                  form.resetFields();
                }}
              >
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default WalletDetail;
