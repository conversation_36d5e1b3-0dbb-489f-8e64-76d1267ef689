"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  Card,
  Tag,
  Space,
  Button,
  DatePicker,
  Select,
  Row,
  Col,
  Statistic,
  Typography,
  message,
  Empty,
} from "antd";
import {
  TransactionOutlined,
  PlusOutlined,
  MinusOutlined,
  TrophyOutlined,
  ShoppingCartOutlined,
  ReloadOutlined,
  FilterOutlined,
} from "@ant-design/icons";
import {
  PersonalTransactionResponse,
  PersonalTransactionFilter,
} from "@/types/wallet";
import { getWalletTransactions } from "@/services/walletService";
import dayjs from "dayjs";

const { RangePicker } = DatePicker;
const { Option } = Select;
const { Text } = Typography;

interface WalletTransactionHistoryProps {
  walletId: number;
}

const WalletTransactionHistory: React.FC<WalletTransactionHistoryProps> = ({
  walletId,
}) => {
  const [transactions, setTransactions] = useState<
    PersonalTransactionResponse[]
  >([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [filters, setFilters] = useState<PersonalTransactionFilter>({});

  const transactionTypes = [
    { value: "ADD_MONEY_TO_WALLET", label: "Add Money", color: "green" },
    { value: "REDEEM_POINTS", label: "Redeem Points", color: "red" },
    { value: "ADMIN_ADJUSTMENT_ADD", label: "Admin Add", color: "blue" },
    {
      value: "ADMIN_ADJUSTMENT_SUBTRACT",
      label: "Admin Subtract",
      color: "orange",
    },
    { value: "REWARD", label: "Reward", color: "gold" },
    { value: "PURCHASE", label: "Purchase", color: "purple" },
  ];

  const fetchTransactions = async (
    page = 1,
    pageSize = 10,
    currentFilters = filters
  ) => {
    try {
      setLoading(true);
      const filter: PersonalTransactionFilter = {
        ...currentFilters,
        page: page - 1,
        limit: pageSize,
        sortBy: "createdAt",
        sortOrder: "desc",
      };

      const response = await getWalletTransactions(walletId, filter);
      setTransactions(response.data);
      setPagination({
        current: page,
        pageSize,
        total: response.pagination.totalElements,
      });
    } catch (error) {
      console.error("Failed to fetch transactions:", error);
      message.error("Failed to load transaction history");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTransactions();
  }, [walletId]);

  const handleTableChange = (paginationInfo: any) => {
    fetchTransactions(paginationInfo.current, paginationInfo.pageSize);
  };

  const handleFilterChange = (key: string, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
  };

  const applyFilters = () => {
    fetchTransactions(1, pagination.pageSize, filters);
  };

  const clearFilters = () => {
    setFilters({});
    fetchTransactions(1, pagination.pageSize, {});
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case "ADD_MONEY_TO_WALLET":
      case "ADMIN_ADJUSTMENT_ADD":
      case "REWARD":
        return <PlusOutlined style={{ color: "#52c41a" }} />;
      case "REDEEM_POINTS":
      case "ADMIN_ADJUSTMENT_SUBTRACT":
      case "PURCHASE":
        return <MinusOutlined style={{ color: "#ff4d4f" }} />;
      default:
        return <TransactionOutlined />;
    }
  };

  const getTransactionColor = (type: string) => {
    const typeConfig = transactionTypes.find((t) => t.value === type);
    return typeConfig?.color || "default";
  };

  const getPointsDisplay = (transaction: PersonalTransactionResponse) => {
    const isPositive = [
      "ADD_MONEY_TO_WALLET",
      "ADMIN_ADJUSTMENT_ADD",
      "REWARD",
    ].includes(transaction.type);
    return (
      <Text style={{ color: isPositive ? "#52c41a" : "#ff4d4f" }}>
        {isPositive ? "+" : "-"}
        {Math.abs(transaction.point).toLocaleString()}
      </Text>
    );
  };

  const columns = [
    {
      title: "Date",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 150,
      render: (date: string) => dayjs(date).format("YYYY-MM-DD HH:mm"),
      sorter: true,
    },
    {
      title: "Type",
      dataIndex: "type",
      key: "type",
      width: 180,
      render: (type: string) => (
        <Space>
          {getTransactionIcon(type)}
          <Tag color={getTransactionColor(type)}>
            {transactionTypes.find((t) => t.value === type)?.label || type}
          </Tag>
        </Space>
      ),
    },
    {
      title: "Points",
      dataIndex: "point",
      key: "point",
      width: 120,
      render: (_: number, record: PersonalTransactionResponse) =>
        getPointsDisplay(record),
      align: "right" as const,
    },
    {
      title: "Description",
      dataIndex: "description",
      key: "description",
      ellipsis: true,
      render: (description: string) => description || "-",
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status: string) => (
        <Tag
          color={
            status === "SUCCESS"
              ? "green"
              : status === "PENDING"
              ? "orange"
              : "red"
          }
        >
          {status || "SUCCESS"}
        </Tag>
      ),
    },
  ];

  // Calculate summary statistics
  const totalEarned = transactions
    .filter((t) =>
      ["ADD_MONEY_TO_WALLET", "ADMIN_ADJUSTMENT_ADD", "REWARD"].includes(t.type)
    )
    .reduce((sum, t) => sum + t.point, 0);

  const totalSpent = transactions
    .filter((t) =>
      ["REDEEM_POINTS", "ADMIN_ADJUSTMENT_SUBTRACT", "PURCHASE"].includes(
        t.type
      )
    )
    .reduce((sum, t) => sum + Math.abs(t.point), 0);

  return (
    <div>
      {/* Summary Statistics */}
      <Row gutter={16} className="mb-4">
        <Col span={8}>
          <Card size="small">
            <Statistic
              title="Total Transactions"
              value={pagination.total}
              prefix={<TransactionOutlined />}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card size="small">
            <Statistic
              title="Points Earned"
              value={totalEarned}
              prefix={<PlusOutlined />}
              valueStyle={{ color: "#52c41a" }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card size="small">
            <Statistic
              title="Points Spent"
              value={totalSpent}
              prefix={<MinusOutlined />}
              valueStyle={{ color: "#ff4d4f" }}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card size="small" className="mb-4">
        <Row gutter={16} align="middle">
          <Col span={6}>
            <Select
              placeholder="Transaction Type"
              allowClear
              style={{ width: "100%" }}
              value={filters.type}
              onChange={(value) => handleFilterChange("type", value)}
            >
              {transactionTypes.map((type) => (
                <Option key={type.value} value={type.value}>
                  <Space>
                    {getTransactionIcon(type.value)}
                    {type.label}
                  </Space>
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={8}>
            <RangePicker
              style={{ width: "100%" }}
              value={
                filters.fromDate && filters.toDate
                  ? [dayjs(filters.fromDate), dayjs(filters.toDate)]
                  : null
              }
              onChange={(dates) => {
                if (dates) {
                  handleFilterChange("fromDate", dates[0]?.toISOString());
                  handleFilterChange("toDate", dates[1]?.toISOString());
                } else {
                  handleFilterChange("fromDate", undefined);
                  handleFilterChange("toDate", undefined);
                }
              }}
            />
          </Col>
          <Col span={6}>
            <Space>
              <Button
                type="primary"
                icon={<FilterOutlined />}
                onClick={applyFilters}
              >
                Apply Filters
              </Button>
              <Button onClick={clearFilters}>Clear</Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => fetchTransactions()}
              >
                Refresh
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Transaction Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={transactions}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} transactions`,
          }}
          onChange={handleTableChange}
          locale={{
            emptyText: (
              <Empty
                description="No transactions found"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            ),
          }}
        />
      </Card>
    </div>
  );
};

export default WalletTransactionHistory;
