"use client";
import BaseCheckbox from "@/components/ui/checkboxes/BaseCheckbox";
import BaseInput from "@/components/ui/inputs/BaseInput";
import BaseTextArea from "@/components/ui/inputs/BaseTextArea";
import BaseModal from "@/components/ui/modals/BaseModal";
import { useNotification } from "@/contexts/NotiContext";
import PermissionService from "@/services/permissionService";
import RoleService from "@/services/roleService";
import { Permission } from "@/types/permission";
import { Role, RoleFormData } from "@/types/role";
import {
  Checkbox,
  CheckboxChangeEvent,
  Col,
  Form,
  Row,
  Typography,
} from "antd";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

const { Text } = Typography;

interface RoleFormModalProps {
  isVisible: boolean;
  onClose: () => void;
  selectedRole: Role | null;
  onSuccess: () => void;
}

const RoleFormModal: React.FC<RoleFormModalProps> = ({
  isVisible,
  onClose,
  selectedRole,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const notification = useNotification();
  const t = useTranslations("roles");
  const tForm = useTranslations("form");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);

  useEffect(() => {
    if (isVisible) {
      // Reset form and fetch permissions when modal becomes visible
      form.resetFields();
      fetchAllPermissions();

      // Set initial values if editing existing role
      if (selectedRole) {
        form.setFieldsValue({
          name: selectedRole.name,
          description: selectedRole.description,
          isDefault: selectedRole.isDefault,
        });

        setSelectedPermissions(
          selectedRole.permissions.map((permission) => permission.id)
        );
      } else {
        setSelectedPermissions([]);
      }
    }
  }, [isVisible, selectedRole, form]);

  useEffect(() => {}, [selectedPermissions]);

  const fetchAllPermissions = async () => {
    try {
      const permissions = await PermissionService.getList({
        page: 0,
        limit: 1000,
      });

      setPermissions(permissions.data);
    } catch (error) {
      console.log("🚀 ~ fetchAllPermissions ~ error:", error);
    }
  };

  const handleFormSubmit = async (values: RoleFormData) => {
    try {
      setIsSubmitting(true);

      await RoleService.update(selectedRole?.id as string, {
        ...values,
        permissionIds: selectedPermissions,
      });

      notification.notifySuccess(t("role_updated_successfully"));

      onSuccess();
      onClose();
    } catch (error: any) {
      notification.notifyError(
        error?.response?.data?.message || tForm("default_error")
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleModulePermissionChange = (e: CheckboxChangeEvent) => {
    const { checked, value } = e.target;
    if (checked) {
      setSelectedPermissions((prev) => [...prev, value]);
    } else {
      setSelectedPermissions((prev) => prev.filter((id) => id !== value));
    }
  };

  const checkCheckedValue = (permission: Permission) => {
    return selectedPermissions?.includes(permission.id);
  };

  const handleSelectAll = (e: CheckboxChangeEvent) => {
    const { checked } = e.target;
    if (checked) {
      setSelectedPermissions(permissions.map((permission) => permission.id));
    } else {
      setSelectedPermissions([]);
    }
  };

  return (
    <BaseModal
      isVisible={isVisible}
      title="Update Role"
      onClose={() => {
        onClose();
        form.resetFields();
      }}
      submitText="Update"
      onSubmit={form.submit}
      confirmLoading={isSubmitting}
      width={1300}
    >
      <Form
        form={form}
        onFinish={handleFormSubmit}
        layout="vertical"
        initialValues={{ name: selectedRole?.name || "" }}
      >
        <BaseInput
          label="Role name"
          name="name"
          required
          placeholder="Enter role name"
          helpText="Enter a unique name for this role"
        />

        <BaseTextArea
          name="description"
          label="Description"
          placeholder="Enter role description"
          rows={4}
        />

        <BaseCheckbox
          name="isDefault"
          checkboxLabel="Is default"
          helpText="Default role can not be deleted"
        />

        <div className="pt-8 font-bold">Role Permissions</div>

        <div className="my-4">
          <div className="flex items-center mb-2">
            <Text strong>Administrator Access</Text>
          </div>

          <Checkbox
            checked={selectedPermissions.length === permissions.length}
            value="all"
            onChange={handleSelectAll}
          >
            Select All
          </Checkbox>
        </div>

        <Row gutter={[0, 16]}>
          {permissions.map((permission) => (
            <Col key={permission.id} span={6}>
              <Checkbox
                className="text-nowrap"
                checked={checkCheckedValue(permission)}
                value={permission.id}
                onChange={handleModulePermissionChange}
              >
                {permission?.name?.toUpperCase()}
              </Checkbox>
            </Col>
          ))}
        </Row>
      </Form>
    </BaseModal>
  );
};

export default RoleFormModal;
