"use client";
import BaseButton from "@/components/ui/buttons/BaseButton";
import { Role } from "@/types/role";
import { DeleteOutlined } from "@ant-design/icons";
import { Card, Popconfirm, Space, Typography } from "antd";
import { useRouter } from "next/navigation";

const { Text } = Typography;

interface RoleCardProps {
  role: Role;
  onEdit?: (role: Role) => void;
  onDelete?: (role: Role) => void;
}

const RoleCard: React.FC<RoleCardProps> = ({ role, onEdit, onDelete }) => {
  const router = useRouter();

  const handleView = () => {
    router.push(`/account-management/roles/${role.id}`);
  };

  // Display only top 5 permissions
  const displayPermissions = role.permissions.slice(0, 5).map((p) => p.name);

  return (
    <Card
      className="h-full [&_.ant-card-body]:h-[calc(100%-58px)] [&_.ant-card-body]:p-4 [&_.ant-card-body]:flex [&_.ant-card-body]:flex-col"
      title={
        <div className="flex justify-between items-center">
          <Text strong className="text-lg">
            {role.name}
          </Text>
          {onDelete && (
            <Popconfirm
              title="Delete this role?"
              description="This action cannot be undone."
              onConfirm={() => onDelete(role)}
              okText="Delete"
              cancelText="Cancel"
              placement="left"
            >
              <BaseButton
                type="text"
                danger
                icon={<DeleteOutlined />}
                size="small"
                onClick={(e) => e.stopPropagation()}
              />
            </Popconfirm>
          )}
        </div>
      }
    >
      <div className="flex-1">
        {/* Permissions list */}
        <Space direction="vertical" size="small" className="w-full">
          {displayPermissions.map((permission, index) => (
            <div key={index}>
              <Text>• {permission}</Text>
            </div>
          ))}
          {role.permissions.length > 5 && (
            <Text type="secondary">
              +{role.permissions.length - 5} more permissions
            </Text>
          )}
        </Space>
      </div>

      <div className="flex justify-end gap-2 mt-auto pt-4">
        <BaseButton
          onClick={(e) => {
            e.stopPropagation();
            handleView();
          }}
        >
          View Role
        </BaseButton>
        {onEdit && (
          <BaseButton
            type="primary"
            onClick={(e) => {
              e.stopPropagation();
              onEdit(role);
            }}
          >
            Edit Role
          </BaseButton>
        )}
      </div>
    </Card>
  );
};

export default RoleCard;
