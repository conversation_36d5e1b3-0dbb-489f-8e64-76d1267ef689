"use client";
import BaseCheckbox from "@/components/ui/checkboxes/BaseCheckbox";
import BaseInput from "@/components/ui/inputs/BaseInput";
import BaseTextArea from "@/components/ui/inputs/BaseTextArea";
import BaseModal from "@/components/ui/modals/BaseModal";
import { useNotification } from "@/contexts/NotiContext";
import RoleService from "@/services/roleService";
import PermissionService from "@/services/permissionService";
import { RoleFormData } from "@/types/role";
import { Permission } from "@/types/permission";
import { PlusOutlined } from "@ant-design/icons";
import { Card, Form, Checkbox, Row, Col, Typography, Spin } from "antd";
import { CheckboxChangeEvent } from "antd/es/checkbox";
import { useTranslations } from "next-intl";
import { useState, useEffect } from "react";

const { Text } = Typography;

interface IAddRoleCard {
  onSuccess?: () => void;
}

const AddRoleCard: React.FC<IAddRoleCard> = ({ onSuccess }) => {
  const [form] = Form.useForm();
  const t = useTranslations("roles");
  const notification = useNotification();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [loadingPermissions, setLoadingPermissions] = useState(false);

  useEffect(() => {
    if (isModalOpen) {
      fetchAllPermissions();
      // Reset form and permissions when modal opens
      form.resetFields();
      setSelectedPermissions([]);
    }
  }, [isModalOpen, form]);

  const fetchAllPermissions = async () => {
    try {
      setLoadingPermissions(true);
      const permissions = await PermissionService.getList({
        page: 0,
        limit: 1000,
      });
      setPermissions(permissions.data);
    } catch (error) {
      console.log("🚀 ~ fetchAllPermissions ~ error:", error);
    } finally {
      setLoadingPermissions(false);
    }
  };

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleCancel = () => {
    form.resetFields();
    setSelectedPermissions([]);
    setIsModalOpen(false);
  };

  const handleModulePermissionChange = (e: CheckboxChangeEvent) => {
    const { checked, value } = e.target;
    if (checked) {
      setSelectedPermissions((prev) => [...prev, value]);
    } else {
      setSelectedPermissions((prev) => prev.filter((id) => id !== value));
    }
  };

  const checkCheckedValue = (permission: Permission) => {
    return selectedPermissions?.includes(permission.id);
  };

  const handleSelectAll = (e: CheckboxChangeEvent) => {
    const { checked } = e.target;
    if (checked) {
      setSelectedPermissions(permissions.map((permission) => permission.id));
    } else {
      setSelectedPermissions([]);
    }
  };

  const handleSubmit = async (values: RoleFormData) => {
    // Validate that at least one permission is selected
    if (selectedPermissions.length === 0) {
      notification.notifyError(
        "Please select at least one permission for this role"
      );
      return;
    }

    setIsSubmitting(true);
    try {
      await RoleService.create({
        ...values,
        permissionIds: selectedPermissions,
      });

      notification.notifySuccess(t("role_created_successfully"));
      setIsModalOpen(false);
      form.resetFields();
      setSelectedPermissions([]);
      onSuccess?.();
    } catch (error) {
      console.error("Failed to add role:", error);
      notification.notifyError(t("role_creation_failed"));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Card
        className="h-full cursor-pointer flex items-center justify-center [&_.ant-card-body]:w-full [&_.ant-card-body]:h-full [&_.ant-card-body]:flex [&_.ant-card-body]:flex-col [&_.ant-card-body]:items-center [&_.ant-card-body]:justify-center"
        onClick={showModal}
        hoverable
      >
        <div className="text-center py-8">
          <PlusOutlined style={{ fontSize: 28 }} />
          <p className="text-lg text-gray-600 font-medium">Add New Role</p>
        </div>
      </Card>

      <BaseModal
        isVisible={isModalOpen}
        title="Add Role"
        onClose={handleCancel}
        onSubmit={form.submit}
        submitText="Submit"
        confirmLoading={isSubmitting}
        width={1300}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <BaseInput
            name="name"
            label="Role name"
            required
            placeholder="Enter Role name"
          />

          <BaseTextArea
            name="description"
            label="Description"
            placeholder="Enter role description"
            rows={4}
          />

          <BaseCheckbox
            name="isDefault"
            checkboxLabel="Is default"
            helpText="Default role can not be deleted"
          />

          <div className="pt-8 font-bold">Role Permissions</div>

          <div className="my-4">
            <div className="flex items-center justify-between mb-2">
              <Text strong>Administrator Access</Text>
              <Text type="secondary">
                {selectedPermissions.length} of {permissions.length} permissions
                selected
              </Text>
            </div>

            <Checkbox
              checked={selectedPermissions.length === permissions.length}
              indeterminate={
                selectedPermissions.length > 0 &&
                selectedPermissions.length < permissions.length
              }
              value="all"
              onChange={handleSelectAll}
            >
              Select All
            </Checkbox>
          </div>

          {loadingPermissions ? (
            <div className="flex justify-center py-4">
              <Spin size="large" />
            </div>
          ) : (
            <Row gutter={[0, 16]}>
              {permissions.map((permission) => (
                <Col key={permission.id} span={6}>
                  <Checkbox
                    className="text-nowrap"
                    checked={checkCheckedValue(permission)}
                    value={permission.id}
                    onChange={handleModulePermissionChange}
                  >
                    {permission?.name?.toUpperCase()}
                  </Checkbox>
                </Col>
              ))}
            </Row>
          )}
        </Form>
      </BaseModal>
    </>
  );
};

export default AddRoleCard;
