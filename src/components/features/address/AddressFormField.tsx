// components/features/address/AddressFormField.tsx
"use client";
import React from "react";
import { Form } from "antd";
import { Rule } from "antd/lib/form";
import AddressSelector from "./AddressSelector";
import { AddressFormData } from "@/types/address";

interface AddressFormFieldProps {
  name?: string | string[];
  required?: boolean;
  disabled?: boolean;
  rules?: Rule[];
  onChange?: (value: AddressFormData & { detailAddress: string }) => void;
  className?: string;
  dependencies?: string[];
  shouldUpdate?: boolean | ((prevValues: any, curValues: any) => boolean);
  preserve?: boolean;
}

const AddressFormField: React.FC<AddressFormFieldProps> = ({
  name = "address",
  required = false,
  disabled = false,
  onChange,
  className = "",
  dependencies,
  shouldUpdate,
  preserve = true,
}) => {
  return (
    <Form.Item
      name={name}
      label={null}
      required={required}
      className={className}
      dependencies={dependencies}
      shouldUpdate={shouldUpdate}
      preserve={preserve}
      noStyle
    >
      <AddressSelector
        required={required}
        disabled={disabled}
        onChange={onChange}
      />
    </Form.Item>
  );
};

export default AddressFormField;
