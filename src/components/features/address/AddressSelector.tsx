/* eslint-disable react-hooks/exhaustive-deps */
// components/features/address/AddressSelector.tsx
"use client";
import BaseInput from "@/components/ui/inputs/BaseInput";
import BaseSelect from "@/components/ui/selects/BaseSelect";
import { AddressFormData, Province, District, Ward } from "@/types/address";
import AddressService from "@/services/addressService";
import { useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";

interface AddressSelectorProps {
  value?: AddressFormData;
  onChange?: (value: AddressFormData) => void;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  showLabels?: boolean;
  displayOnOneColumn?: boolean;
}

const AddressSelector: React.FC<AddressSelectorProps> = ({
  value,
  onChange,
  required = false,
  disabled = false,
  className = "",
  showLabels = true,
  displayOnOneColumn = false,
}) => {
  const t = useTranslations("address");

  // Data states - only for loading options
  const [provinces, setProvinces] = useState<Province[]>([]);
  const [districts, setDistricts] = useState<District[]>([]);
  const [wards, setWards] = useState<Ward[]>([]);

  // Loading states
  const [loading, setLoading] = useState({
    provinces: false,
    districts: false,
    wards: false,
  });

  // Load provinces
  const loadProvinces = async () => {
    setLoading((prev) => ({ ...prev, provinces: true }));
    try {
      const data = await AddressService.getProvinces();
      setProvinces(data);

      // Auto-load districts if we have initial province value
      if (value?.provinceCode) {
        await loadDistricts(value.provinceCode);
      }
    } catch (error) {
      console.error("Failed to load provinces:", error);
    } finally {
      setLoading((prev) => ({ ...prev, provinces: false }));
    }
  };

  // Load districts
  const loadDistricts = async (provinceCode: number) => {
    setLoading((prev) => ({ ...prev, districts: true }));
    try {
      const data = await AddressService.getDistricts(provinceCode);
      setDistricts(data);

      // Auto-load wards if we have initial district value
      if (value?.districtCode) {
        await loadWards(value.districtCode);
      }
    } catch (error) {
      console.error("Failed to load districts:", error);
    } finally {
      setLoading((prev) => ({ ...prev, districts: false }));
    }
  };

  // Load wards
  const loadWards = async (districtCode: number) => {
    setLoading((prev) => ({ ...prev, wards: true }));
    try {
      const data = await AddressService.getWards(districtCode);
      setWards(data);
    } catch (error) {
      console.error("Failed to load wards:", error);
    } finally {
      setLoading((prev) => ({ ...prev, wards: false }));
    }
  };

  // Load initial data
  useEffect(() => {
    loadProvinces();
  }, []);

  // Load cascading data when value changes
  useEffect(() => {
    if (value?.provinceCode && provinces.length > 0) {
      loadDistricts(value.provinceCode);
    } else {
      setDistricts([]);
      setWards([]);
    }
  }, [value?.provinceCode, provinces.length]);

  useEffect(() => {
    if (value?.districtCode && districts.length > 0) {
      loadWards(value.districtCode);
    } else {
      setWards([]);
    }
  }, [value?.districtCode, districts.length]);

  // Handle changes - all controlled by Form.Item through value/onChange
  const handleProvinceChange = (provinceCode: number | null) => {
    const newValue: AddressFormData = {
      provinceCode,
      districtCode: null, // Reset district when province changes
      wardCode: null, // Reset ward when province changes
      detailAddress: value?.detailAddress || "",
    };
    onChange?.(newValue);
  };

  const handleDistrictChange = (districtCode: number | null) => {
    const newValue: AddressFormData = {
      provinceCode: value?.provinceCode || null,
      districtCode,
      wardCode: null, // Reset ward when district changes
      detailAddress: value?.detailAddress || "",
    };
    onChange?.(newValue);
  };

  const handleWardChange = (wardCode: number | null) => {
    const newValue: AddressFormData = {
      provinceCode: value?.provinceCode || null,
      districtCode: value?.districtCode || null,
      wardCode,
      detailAddress: value?.detailAddress || "",
    };
    onChange?.(newValue);
  };

  const handleDetailAddressChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const newValue: AddressFormData = {
      provinceCode: value?.provinceCode || null,
      districtCode: value?.districtCode || null,
      wardCode: value?.wardCode || null,
      detailAddress: e.target.value,
    };
    onChange?.(newValue);
  };

  // Transform data for BaseSelect options
  const provinceOptions = provinces?.map((province) => ({
    value: province.code,
    label: province.name,
  }));

  const districtOptions = districts?.map((district) => ({
    value: district.code,
    label: district.name,
  }));

  const wardOptions = wards?.map((ward) => ({
    value: ward.code,
    label: ward.name,
  }));

  return (
    <div className={className}>
      <div
        className={`grid gap-4 ${
          displayOnOneColumn ? "grid-cols-1" : "grid-cols-2"
        }`}
      >
        <BaseSelect
          label={showLabels ? t("province") : undefined}
          placeholder={t("select_province")}
          options={provinceOptions}
          loading={loading.provinces}
          disabled={disabled}
          value={value?.provinceCode}
          onChange={handleProvinceChange}
          required={required}
        />

        <BaseSelect
          label={showLabels ? t("district") : undefined}
          placeholder={t("select_district")}
          options={districtOptions}
          loading={loading.districts}
          disabled={disabled}
          value={value?.districtCode}
          onChange={handleDistrictChange}
          required={required}
        />

        <BaseSelect
          label={showLabels ? t("ward") : undefined}
          placeholder={t("select_ward")}
          options={wardOptions}
          loading={loading.wards}
          disabled={disabled}
          value={value?.wardCode}
          onChange={handleWardChange}
          required={required}
        />

        <BaseInput
          label={showLabels ? t("detailed_address") : undefined}
          placeholder={t("enter_detailed_address")}
          disabled={disabled}
          maxLength={200}
          showCount
          value={value?.detailAddress || ""}
          onChange={handleDetailAddressChange}
        />
      </div>
    </div>
  );
};

export default AddressSelector;
