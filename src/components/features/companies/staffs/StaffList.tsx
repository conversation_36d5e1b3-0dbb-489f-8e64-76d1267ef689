// components/companies/TeamMemberList.tsx
"use client";

import TeamMemberForm from "@/components/features/companies/staffs/StaffForm";
import BaseModal from "@/components/ui/modals/BaseModal";
import BaseTable from "@/components/ui/tables/BaseTable";
import { FilterConfig } from "@/components/ui/tables/TableFilter";
import { PermissionEnum } from "@/constants/_permissionEnum";
import { AuthContext } from "@/contexts/AuthContext";
import StaffService from "@/services/staffService";
import { TeamMember } from "@/types/teamMember";
import {
  LockOutlined,
  MailOutlined,
  UnlockOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { Avatar, Button, message, Space, Tag } from "antd";
import dayjs from "dayjs";
import { useContext, useRef, useState } from "react";

interface TeamMemberListProps {
  companyId: string;
}

const TeamMemberList: React.FC<TeamMemberListProps> = ({ companyId }) => {
  const [editMember, setEditMember] = useState<TeamMember | null>(null);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const tableRef = useRef<{ refetch: () => void }>(null);

  const authContext = useContext(AuthContext);
  const access = authContext?.access;

  const filters: FilterConfig[] = [
    {
      key: "isActive",
      label: "Status",
      type: "select",
      options: [
        { value: "true", label: "Active" },
        { value: "false", label: "Inactive" },
      ],
    },
    {
      key: "isPending",
      label: "Invitation Status",
      type: "select",
      options: [
        { value: "true", label: "Pending" },
        { value: "false", label: "Accepted" },
      ],
    },
    {
      key: "isManager",
      label: "Role",
      type: "select",
      options: [
        { value: "true", label: "Manager" },
        { value: "false", label: "Member" },
      ],
    },
  ];

  const handleModalSuccess = () => {
    handleModalClose();
    if (tableRef.current) {
      tableRef.current.refetch();
    }
  };

  const handleInviteMember = () => {
    setEditMember(null);
    setEditModalVisible(true);
  };

  const handleEditMember = (member: TeamMember) => {
    setEditMember(member);
    setEditModalVisible(true);
  };

  const handleDeleteMember = async (member: TeamMember) => {
    try {
      await StaffService.delete(companyId, member.id);
      message.success("Team member removed successfully");
      return Promise.resolve();
    } catch (error) {
      console.error("Error removing team member:", error);
      message.error("Failed to remove team member");
      return Promise.reject(error);
    }
  };

  const handleResendInvitation = async (memberId: string) => {
    try {
      await StaffService.resendInvitation(companyId, memberId);
      message.success("Invitation resent successfully");
    } catch (error) {
      console.error("Error resending invitation:", error);
      message.error("Failed to resend invitation");
    }
  };

  const handleToggleManager = async (member: TeamMember) => {
    try {
      await StaffService.makeManager(companyId, member.id, !member.isManager);
      message.success(
        `User ${
          member.isManager ? "removed from" : "made"
        } manager successfully`
      );
    } catch (error) {
      console.error("Error updating manager status:", error);
      message.error("Failed to update manager status");
    }
  };

  const handleModalClose = () => {
    setEditMember(null);
    setEditModalVisible(false);
  };

  const columns = [
    {
      title: "Member",
      key: "member",
      render: (_: unknown, record: TeamMember) => (
        <Space>
          <Avatar src={record.photoUrl} icon={<UserOutlined />} size="large" />
          <div>
            <div>{record.name}</div>
            <div className="text-xs text-gray-500">
              {record.position || "No position"}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: "Branch",
      dataIndex: "branchName",
      key: "branchName",
      render: (text: string) => text || "Not assigned",
    },
    {
      title: "Status",
      key: "status",
      render: (_: unknown, record: TeamMember) => (
        <Space>
          {record.isPending ? (
            <Tag color="orange">Pending</Tag>
          ) : record.isActive ? (
            <Tag color="success">Active</Tag>
          ) : (
            <Tag color="error">Inactive</Tag>
          )}
          {record.isManager && <Tag color="blue">Manager</Tag>}
        </Space>
      ),
    },
    {
      title: "Join Date",
      dataIndex: "joinDate",
      key: "joinDate",
      render: (date: string) => dayjs(date).format("YYYY-MM-DD"),
    },
    {
      title: "Actions",
      key: "actions",
      render: (_: unknown, record: TeamMember) => (
        <Space size="small">
          <Button
            type="link"
            icon={record.isManager ? <LockOutlined /> : <UnlockOutlined />}
            onClick={() => handleToggleManager(record)}
          >
            {record.isManager ? "Remove Manager" : "Make Manager"}
          </Button>

          {record.isPending && (
            <Button
              type="link"
              icon={<MailOutlined />}
              onClick={() => handleResendInvitation(record.id)}
            >
              Resend Invite
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <>
      <BaseTable
        ref={tableRef}
        api={StaffService.getList}
        initialParams={{ companyId }}
        columns={columns}
        rowKey="id"
        title=""
        createBtnText="Invite Member"
        showSearch={true}
        searchPlaceholder="Search team members..."
        onCreate={
          access &&
          access[PermissionEnum.COMPANY_INVITE_STAFF] &&
          handleInviteMember
        }
        onEdit={
          access &&
          access[PermissionEnum.COMPANY_UPDATE_STAFF] &&
          handleEditMember
        }
        onDelete={
          access &&
          access[PermissionEnum.COMPANY_REMOVE_STAFF] &&
          handleDeleteMember
        }
        filters={filters}
        showActions={true}
      />

      {/* Edit/Invite Member Modal */}
      <BaseModal
        isVisible={editModalVisible}
        title={editMember ? "Edit Team Member" : "Invite Team Member"}
        onClose={() => setEditModalVisible(false)}
        width={600}
        footer={null}
      >
        <TeamMemberForm
          companyId={companyId}
          initialData={editMember}
          onSuccess={handleModalSuccess}
        />
      </BaseModal>
    </>
  );
};

export default TeamMemberList;
