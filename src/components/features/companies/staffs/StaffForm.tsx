"use client";

import BaseButton from "@/components/ui/buttons/BaseButton";
import BaseInput from "@/components/ui/inputs/BaseInput";
import BaseSelect from "@/components/ui/selects/BaseSelect";
import DebounceSelect from "@/components/ui/selects/DebounceSelect";
import BaseSwitch from "@/components/ui/switches/BaseSwitch";
import BranchService from "@/services/branchService";
import StaffService from "@/services/staffService";
import UserService from "@/services/userService";
import { Branch } from "@/types/branch";
import { TeamMember, TeamMemberInput } from "@/types/teamMember";
import { Flex, Form, message } from "antd";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

interface TeamMemberFormProps {
  companyId: string;
  initialData?: TeamMember | null;
  onSuccess: () => void;
}

const TeamMemberForm: React.FC<TeamMemberFormProps> = ({
  companyId,
  initialData,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [loadingBranches, setLoadingBranches] = useState(false);
  const isEditing = !!initialData;
  const tCommon = useTranslations("common");
  const tTeamMember = useTranslations("team_member");

  useEffect(() => {
    fetchBranches();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [companyId]);

  useEffect(() => {
    if (initialData) {
      console.log("🚀 ~ useEffect ~ initialData:", initialData);
      form.setFieldsValue({
        name: initialData.name,
        position: initialData.position,
        branchId: initialData.branch.id,
        isManager: initialData.isManager,
      });
    } else {
      form.resetFields();
    }
  }, [initialData, form]);

  const fetchBranches = async () => {
    setLoadingBranches(true);
    try {
      const response = await BranchService.getList({ companyId });
      setBranches(response.data);
    } catch (error) {
      console.error("Error fetching branches:", error);
      message.error(tTeamMember("failed_to_load_branches"));
    } finally {
      setLoadingBranches(false);
    }
  };

  const handleSubmit = async (values: TeamMemberInput) => {
    setLoading(true);
    try {
      if (isEditing && initialData) {
        await StaffService.update({
          ...values,
          id: initialData.id,
          companyId: parseInt(companyId),
        });
        message.success(tTeamMember("team_member_updated_successfully"));
      } else {
        await StaffService.inviteTeamMember({
          ...values,
          companyId: parseInt(companyId),
        });
        message.success(tTeamMember("team_member_invited_successfully"));
      }
      onSuccess();
    } catch (error) {
      console.error("Failed to save team member:", error);
      message.error(tTeamMember("failed_to_save_team_member"));
    } finally {
      setLoading(false);
    }
  };

  const branchOptions = branches.map((branch) => ({
    value: branch.id,
    label: `${branch.name} ${
      branch.isDefault ? `(${tTeamMember("default")})` : ""
    }`,
  }));

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      initialValues={{
        isManager: false,
      }}
    >
      {!isEditing && (
        <DebounceSelect
          label={tCommon("fields.user")}
          name="userId"
          fetchOptions={(search) =>
            UserService.getList({
              keyword: search,
              page: 0,
              limit: 20000,
            })
          }
          placeholder={tTeamMember("search_and_select_users_placeholder")}
          required
        />
      )}

      {isEditing && (
        <BaseInput
          name="name"
          label={tTeamMember("team_member_name")}
          disabled
        />
      )}

      <BaseInput
        name="position"
        label={tTeamMember("position")}
        placeholder={tTeamMember("enter_position_placeholder")}
      />

      <BaseSelect
        name="branchId"
        label={tTeamMember("branch")}
        placeholder={tTeamMember("select_branch_placeholder")}
        loading={loadingBranches}
        allowClear
        options={branchOptions}
      />

      <BaseSwitch name="isManager" label={tTeamMember("manager_role")} />

      <Flex justify="flex-end" gap="small" className="mt-6">
        <BaseButton label={tCommon("actions.cancel")} onClick={onSuccess} />
        <BaseButton
          label={
            isEditing
              ? tTeamMember("update_member")
              : tTeamMember("invite_member")
          }
          type="primary"
          htmlType="submit"
          loading={loading}
        />
      </Flex>
    </Form>
  );
};

export default TeamMemberForm;
