// components/companies/BranchList.tsx
"use client";

import BranchForm from "@/components/features/companies/branches/BranchForm";
import BaseModal from "@/components/ui/modals/BaseModal";
import BaseTable from "@/components/ui/tables/BaseTable";
import { FilterConfig } from "@/components/ui/tables/TableFilter";
import { PermissionEnum } from "@/constants/_permissionEnum";
import { AuthContext } from "@/contexts/AuthContext";
import BranchService from "@/services/branchService";
import { Branch } from "@/types/branch";
import { CheckCircleOutlined } from "@ant-design/icons";
import { Button, Space, Tag, message } from "antd";
import { useContext, useRef, useState } from "react";

interface BranchListProps {
  companyId: string;
}

const BranchList: React.FC<BranchListProps> = ({ companyId }) => {
  const [editBranch, setEditBranch] = useState<Branch | null>(null);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const tableRef = useRef<{ refetch: () => void }>(null);

  const authContext = useContext(AuthContext);
  const access = authContext?.access;

  const filters: FilterConfig[] = [
    {
      key: "isDefault",
      label: "Branch Type",
      type: "select",
      options: [
        { value: "true", label: "Default Branch" },
        { value: "false", label: "Regular Branch" },
      ],
    },
    {
      key: "name",
      label: "Branch Name",
      type: "text",
    },
  ];

  const handleModalClose = () => {
    setEditBranch(null);
    setEditModalVisible(false);
  };

  const handleModalSuccess = () => {
    handleModalClose();
    if (tableRef.current) {
      tableRef.current.refetch();
    }
  };

  const handlerOnCreateBranch = () => {
    setEditBranch(null);
    setEditModalVisible(true);
  };

  const handleEditBranch = (branch: Branch) => {
    setEditBranch(branch);
    setEditModalVisible(true);
  };

  const handleDeleteBranch = async (branch: Branch) => {
    if (branch.isDefault) {
      message.error("Cannot delete the default branch");
      return Promise.reject("Cannot delete default branch");
    }

    try {
      await BranchService.delete(companyId, `${branch.id}`);
      message.success("Branch deleted successfully");
      return Promise.resolve();
    } catch (error) {
      console.error("Error deleting branch:", error);
      message.error("Failed to delete branch");
      return Promise.reject(error);
    }
  };

  const handleSetDefaultBranch = async (branch: Branch) => {
    if (branch.isDefault) {
      return;
    }

    try {
      await BranchService.update(companyId, branch.id, {
        ...branch,
        isDefault: true,
      });
      message.success("Default branch updated");
    } catch (error) {
      console.error("Error setting default branch:", error);
      message.error("Failed to update default branch");
    }
  };

  const columns = [
    {
      title: "Branch Name",
      dataIndex: "name",
      key: "name",
      render: (text: string, record: Branch) => (
        <span>
          {text}
          {record.isDefault && (
            <Tag color="success" className="!ml-2">
              Default
            </Tag>
          )}
        </span>
      ),
    },
    {
      title: "Address",
      dataIndex: "address",
      key: "address",
      ellipsis: true,
    },
    {
      title: "Manager",
      dataIndex: "managerName",
      key: "managerName",
      render: (text: string) => text || "Not assigned",
    },
    {
      title: "Phone",
      dataIndex: "phoneNumber",
      key: "phoneNumber",
      render: (text: string) => text || "N/A",
    },
    {
      title: "Staff",
      dataIndex: "staffCount",
      key: "staffCount",
    },
    {
      title: "Actions",
      key: "actions",
      render: (_: unknown, record: Branch) => (
        <Space size="small">
          {!record.isDefault && (
            <Button
              type="link"
              icon={<CheckCircleOutlined />}
              onClick={() => handleSetDefaultBranch(record)}
            >
              Set as Default
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <>
      <BaseTable
        ref={tableRef}
        api={BranchService.getList}
        columns={columns}
        rowKey="id"
        title=""
        createBtnText="Add Branch"
        showSearch={true}
        searchPlaceholder="Search branches..."
        onCreate={
          access &&
          access[PermissionEnum.COMPANY_CREATE_BRANCH] &&
          handlerOnCreateBranch
        }
        onEdit={
          access &&
          access[PermissionEnum.COMPANY_UPDATE_BRANCH] &&
          handleEditBranch
        }
        onDelete={
          access &&
          access[PermissionEnum.COMPANY_UPDATE_BRANCH] &&
          handleDeleteBranch
        }
        onRowClick={handleEditBranch}
        filters={filters}
        initialParams={{ companyId }}
        showActions={true}
      />

      {/* Edit Branch Modal */}
      <BaseModal
        isVisible={editModalVisible}
        title={editBranch ? "Edit Branch" : "Add Branch"}
        onClose={() => setEditModalVisible(false)}
        width={600}
        footer={null}
      >
        <BranchForm
          companyId={companyId}
          initialData={editBranch}
          onSuccess={handleModalSuccess}
        />
      </BaseModal>
    </>
  );
};

export default BranchList;
