"use client";

import BaseInput from "@/components/ui/inputs/BaseInput";
import BaseTextArea from "@/components/ui/inputs/BaseTextArea";
import BaseSwitch from "@/components/ui/switches/BaseSwitch";
import BaseButton from "@/components/ui/buttons/BaseButton";
import BranchService from "@/services/branchService";
import { Form, message, Flex } from "antd";
import { useEffect, useState } from "react";
import { Branch } from "@/types/branch";
import AddressFormField from "../../address/AddressFormField";

interface BranchFormProps {
  companyId: string;
  initialData?: Branch | null;
  onSuccess: () => void;
}

const BranchForm: React.FC<BranchFormProps> = ({
  companyId,
  initialData,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const isEditing = !!initialData;

  useEffect(() => {
    if (initialData) {
      // Format operating hours if they exist
      const operatingHoursValue = initialData.operatingHours;

      form.setFieldsValue({
        name: initialData.name,
        address: initialData.address,
        managerName: initialData.managerName,
        phoneNumber: initialData.phoneNumber,
        operatingHours: operatingHoursValue,
        isDefault: initialData.isDefault,
      });
    } else {
      form.resetFields();
    }
  }, [initialData, form]);

  const handleSubmit = async (values: Branch) => {
    setLoading(true);
    try {
      if (isEditing) {
        const updateData: Branch = {
          ...values,
          id: initialData.id,
        };
        await BranchService.update(companyId, updateData.id, updateData);
        message.success("Branch updated successfully");
      } else {
        await BranchService.create(companyId, values);
        message.success("Branch created successfully");
      }
      onSuccess();
    } catch (error) {
      console.error("Failed to save branch:", error);
      message.error("Failed to save branch");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      initialValues={{
        isDefault: false,
      }}
    >
      <BaseInput
        name="name"
        label="Branch Name"
        placeholder="Enter branch name"
        required
        rules={[{ required: true, message: "Please enter branch name" }]}
      />

      <AddressFormField name="address" required />

      <BaseInput
        name="managerName"
        label="Branch Manager"
        placeholder="Name of branch manager (optional)"
      />

      <BaseInput
        name="phoneNumber"
        label="Contact Phone"
        required
        placeholder="Branch contact phone (optional)"
      />

      <BaseInput
        name="operatingHours"
        label="Operating Hours"
        placeholder="e.g. Mon-Fri: 9:00 AM - 5:00 PM"
      />

      <BaseSwitch
        name="isDefault"
        label="Default Branch"
        helpText="The default branch is the main branch of the company"
        disabled={initialData?.isDefault}
      />

      <Flex justify="flex-end" gap="small" className="mt-6">
        <BaseButton label="Cancel" onClick={onSuccess} />
        <BaseButton
          label={isEditing ? "Update Branch" : "Create Branch"}
          type="primary"
          htmlType="submit"
          loading={loading}
        />
      </Flex>
    </Form>
  );
};

export default BranchForm;
