"use client";

import React from "react";
import BaseModal from "@/components/ui/modals/BaseModal";
import CompanyForm from "@/components/features/companies/CompanyForm";
import { Company } from "@/types/company";
import { useTranslations } from "next-intl";

interface CompanyModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  company?: Company | null;
}

const CompanyModal: React.FC<CompanyModalProps> = ({
  isVisible,
  onClose,
  onSuccess,
  company = null,
}) => {
  const tCompanies = useTranslations("companies");
  const isEditing = !!company;

  const handleSuccess = () => {
    onSuccess();
    onClose();
  };

  return (
    <BaseModal
      isVisible={isVisible}
      title={
        isEditing ? tCompanies("edit_company") : tCompanies("add_new_company")
      }
      onClose={onClose}
      width={1000}
      footer={null}
    >
      <CompanyForm initialData={company} onSuccess={handleSuccess} />
    </BaseModal>
  );
};

export default CompanyModal;
