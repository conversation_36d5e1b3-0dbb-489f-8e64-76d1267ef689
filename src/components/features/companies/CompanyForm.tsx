"use client";

import Address<PERSON><PERSON><PERSON>ield from "@/components/features/address/AddressFormField";
import BaseButton from "@/components/ui/buttons/BaseButton";
import BaseInput from "@/components/ui/inputs/BaseInput";
import BaseTextArea from "@/components/ui/inputs/BaseTextArea";
import BaseSelect from "@/components/ui/selects/BaseSelect";
import BaseSwitch from "@/components/ui/switches/BaseSwitch";
import BaseUpload from "@/components/ui/uploads/BaseUpload";
import { FILE_TYPES } from "@/constants/file";
import { useNotification } from "@/contexts/NotiContext";
import CompanyService from "@/services/companyService";
import {
  Company,
  CompanyCreateInput,
  CompanyUpdateInput,
} from "@/types/company";
import { UploadOutlined } from "@ant-design/icons";
import { Flex, Form } from "antd";
import { UploadChangeParam, UploadFile } from "antd/lib/upload";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

interface CompanyFormProps {
  initialData?: Company | null;
  onSuccess: () => void;
}

const CompanyForm: React.FC<CompanyFormProps> = ({
  initialData,
  onSuccess,
}) => {
  const notification = useNotification();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [logoUrl, setLogoUrl] = useState("");

  const tCompanies = useTranslations("companies");
  const tCommon = useTranslations("common");

  const isEditing = !!initialData;

  useEffect(() => {
    if (initialData) {
      form.setFieldsValue({
        ...initialData,
        address: {
          provinceCode: initialData.address?.province?.code || null,
          districtCode: initialData.address?.district?.code || null,
          wardCode: initialData.address?.ward?.code || null,
          detailAddress: initialData.address?.detailAddress || "",
        },
      });
    } else {
      form.resetFields();
    }
  }, [initialData, form]);

  const handleSubmit = async (
    values: CompanyCreateInput & CompanyUpdateInput
  ) => {
    setLoading(true);
    try {
      if (isEditing && initialData) {
        const updateData: CompanyUpdateInput = {
          ...values,
          id: initialData.id,
          logoUrl: logoUrl || initialData.logoUrl,
        };
        await CompanyService.update(initialData.id, updateData);
        notification.notifySuccess(tCompanies("company_updated_successfully"));
      } else {
        const createData: CompanyCreateInput = {
          ...values,
          logoUrl: logoUrl || "",
        };
        await CompanyService.create(createData);
        notification.notifySuccess(tCompanies("company_created_successfully"));
      }
      onSuccess();
    } catch (error) {
      console.error("Failed to save company:", error);
      notification.notifyError(tCompanies("failed_to_save_company"));
    } finally {
      setLoading(false);
    }
  };

  const handleLogoChange = (info: UploadChangeParam<UploadFile<unknown>>) => {
    if (info.file.status === "done") {
      // Check if response has the expected structure
      const filePath = (info.file.response as any)?.data?.filePath;
      setLogoUrl(filePath || "");
    }
  };

  const industryOptions = [
    { value: "technology", label: tCompanies("industries.technology") },
    { value: "healthcare", label: tCompanies("industries.healthcare") },
    { value: "education", label: tCompanies("industries.education") },
    { value: "finance", label: tCompanies("industries.finance") },
    { value: "retail", label: tCompanies("industries.retail") },
    { value: "manufacturing", label: tCompanies("industries.manufacturing") },
    { value: "hospitality", label: tCompanies("industries.hospitality") },
    { value: "other", label: tCompanies("industries.other") },
  ];

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      initialValues={{
        isIndividual: false,
      }}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="md:col-span-2">
          <BaseInput
            name="name"
            label={tCompanies("company_name")}
            placeholder={tCompanies("placeholders.company_name")}
            required
            rules={[
              {
                required: true,
                message: tCompanies("please_enter_company_name"),
              },
            ]}
          />
        </div>

        <BaseSelect
          name="industry"
          label={tCommon("fields.industry")}
          placeholder={tCompanies("select_industry")}
          options={industryOptions}
          required
          rules={[
            {
              required: true,
              message: tCompanies("please_select_industry"),
            },
          ]}
        />

        <BaseInput
          name="foundedYear"
          label={tCompanies("founded_year")}
          placeholder={tCompanies("placeholders.founded_year")}
        />

        <BaseInput
          name="email"
          label={tCompanies("contact_email")}
          placeholder={tCompanies("placeholders.contact_email")}
          rules={[
            {
              type: "email",
              message: tCompanies("please_enter_valid_email"),
            },
          ]}
        />

        <BaseInput
          name="phoneNumber"
          label={tCompanies("contact_phone")}
          placeholder={tCompanies("placeholders.contact_phone")}
        />

        <div className="md:col-span-2">
          <BaseInput
            name="websiteUrl"
            label={tCompanies("website_url")}
            placeholder={tCompanies("placeholders.website_url")}
            rules={[
              {
                type: "url",
                message: tCompanies("please_enter_valid_url"),
              },
            ]}
          />
        </div>

        <div className="md:col-span-2">
          <AddressFormField name="address" required />
        </div>

        <div className="md:col-span-2">
          <BaseTextArea
            name="description"
            label={tCompanies("company_description")}
            rows={4}
            placeholder={tCompanies("placeholders.company_description")}
            required
            rules={[
              {
                required: true,
                message: tCompanies("please_enter_company_description"),
              },
            ]}
          />
        </div>

        <BaseSwitch
          name="isIndividual"
          label={tCompanies("individual_company")}
          helpText={tCompanies("individual_company_help")}
        />

        <BaseUpload
          name="logoUrl"
          label={tCompanies("company_logo")}
          listType="picture"
          maxCount={1}
          accept=".jpg,.jpeg,.png"
          data={{
            type: FILE_TYPES.COMPANY_LOGO,
          }}
          onChange={handleLogoChange}
          buttonText={tCompanies("upload_logo")}
          buttonIcon={<UploadOutlined />}
        />
      </div>

      <Flex justify="flex-end" gap="small" className="mt-6">
        <BaseButton label={tCommon("actions.cancel")} onClick={onSuccess} />
        <BaseButton
          label={
            isEditing
              ? tCompanies("update_company")
              : tCompanies("create_company")
          }
          type="primary"
          htmlType="submit"
          loading={loading}
        />
      </Flex>
    </Form>
  );
};

export default CompanyForm;
