// components/features/notifications/NotificationListing.tsx
"use client";

import BaseTable, { BaseTableRef } from "@/components/ui/tables/BaseTable";
import { FilterConfig } from "@/components/ui/tables/TableFilter";
import {
  NOTIFICATION_CHANNELS,
  NOTIFICATION_STATUSES,
  NOTIFICATION_TYPES,
  getChannelIcon,
  getStatusColor,
} from "@/constants/notifications";
import { useNotification } from "@/contexts/NotiContext";
import NotificationService from "@/services/notificationService";
import { Notification } from "@/types/notifications";
import { EyeOutlined, RedoOutlined } from "@ant-design/icons";
import { Button, Flex, Space, Tag, Tooltip } from "antd";
import { ColumnsType } from "antd/es/table";
import React, { useRef } from "react";

interface NotificationListingProps {
  onCreateNew?: () => void;
  onViewDetails?: (notification: Notification) => void;
}

const NotificationListing: React.FC<NotificationListingProps> = ({
  // onCreateNew,
  onViewDetails,
}) => {
  const tableRef = useRef<BaseTableRef>(null);
  const notification = useNotification();

  // Table columns configuration
  const columns: ColumnsType<Notification> = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 100,
      render: (id: string) => (
        <code className="text-xs bg-gray-100 px-2 py-1 rounded-sm">
          {id.slice(-8)}
        </code>
      ),
    },
    {
      title: "User",
      key: "user",
      width: 200,
      render: (_, record) => (
        <div>
          <div className="font-medium">{record.user_name}</div>
          <div className="text-xs text-gray-500">{record.user_email}</div>
        </div>
      ),
    },
    {
      title: "Type",
      dataIndex: "type",
      key: "type",
      width: 150,
      render: (type: string) => {
        const typeConfig = NOTIFICATION_TYPES.find((t) => t.value === type);
        return (
          <Tag color="blue" className="text-xs">
            {typeConfig?.label || type}
          </Tag>
        );
      },
    },
    {
      title: "Channel",
      dataIndex: "channel",
      key: "channel",
      width: 120,
      render: (channel: string) => {
        const channelConfig = NOTIFICATION_CHANNELS.find(
          (c) => c.value === channel
        );
        return (
          <Flex align="center" gap="small">
            <span>{getChannelIcon(channel as any)}</span>
            <span className="text-xs">{channelConfig?.label || channel}</span>
          </Flex>
        );
      },
    },
    {
      title: "Title",
      dataIndex: "title",
      key: "title",
      width: 250,
      render: (title: string) => (
        <Tooltip title={title}>
          <div className="truncate max-w-48">{title}</div>
        </Tooltip>
      ),
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      width: 120,
      render: (status: string) => {
        const statusConfig = NOTIFICATION_STATUSES.find(
          (s) => s.value === status
        );
        return (
          <Tag color={getStatusColor(status as any)}>
            {statusConfig?.label || status}
          </Tag>
        );
      },
    },
    {
      title: "Sent At",
      dataIndex: "sent_at",
      key: "sent_at",
      width: 150,
      render: (date: string) => (
        <div className="text-xs">
          {date ? new Date(date).toLocaleString() : "-"}
        </div>
      ),
    },
    {
      title: "Read At",
      dataIndex: "read_at",
      key: "read_at",
      width: 150,
      render: (date: string) => (
        <div className="text-xs text-gray-500">
          {date ? new Date(date).toLocaleString() : "-"}
        </div>
      ),
    },
    {
      title: "Actions",
      key: "actions",
      width: 120,
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="View Details">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                onViewDetails?.(record);
              }}
            />
          </Tooltip>
          {record.status === "failed" && (
            <Tooltip title="Resend">
              <Button
                type="text"
                size="small"
                icon={<RedoOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleResend(record);
                }}
              />
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  // Filter configuration
  const filters: FilterConfig[] = [
    {
      key: "type",
      label: "Type",
      type: "select",
      options: NOTIFICATION_TYPES,
    },
    {
      key: "channel",
      label: "Channel",
      type: "select",
      options: NOTIFICATION_CHANNELS,
    },
    {
      key: "status",
      label: "Status",
      type: "select",
      options: NOTIFICATION_STATUSES,
    },
    {
      key: "user_id",
      label: "User ID",
      type: "text",
      placeholder: "Enter user ID",
    },
    {
      key: "date_range",
      label: "Date Range",
      type: "dateRange",
    },
  ];

  // Handle resend notification
  const handleResend = async (record: Notification) => {
    try {
      await NotificationService.resend(record.id);
      notification.notifySuccess("Notification resent successfully");
      tableRef.current?.refetch();
    } catch (error) {
      console.log("🚀 ~ handleResend ~ error:", error);
      notification.notifyError("Failed to resend notification");
    }
  };

  // Handle delete notification
  const handleDelete = async (record: Notification): Promise<void> => {
    try {
      await NotificationService.delete(record.id);
      notification.notifySuccess("Notification deleted successfully");
    } catch (error) {
      notification.notifyError("Failed to delete notification");
      throw error;
    }
  };

  const handleRowClick = (record: Notification) => {
    onViewDetails?.(record);
  };

  return (
    <BaseTable<Notification>
      ref={tableRef}
      api={NotificationService.getList}
      columns={columns}
      rowKey="id"
      showSearch={true}
      searchPlaceholder="Search by title, user name, or email..."
      onRowClick={handleRowClick}
      onDelete={handleDelete}
      filters={filters}
      showActions={false}
    />
  );
};

export default NotificationListing;
