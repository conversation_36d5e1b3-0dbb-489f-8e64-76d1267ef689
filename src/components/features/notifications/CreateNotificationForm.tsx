// components/features/notifications/CreateNotificationForm.tsx
"use client";

import BaseButton from "@/components/ui/buttons/BaseButton";
import BaseDatePicker from "@/components/ui/datepickers/BaseDatePicker";
import BaseInput from "@/components/ui/inputs/BaseInput";
import BaseTextArea from "@/components/ui/inputs/BaseTextArea";
import BaseSelect from "@/components/ui/selects/BaseSelect";
import DebounceSelect from "@/components/ui/selects/DebounceSelect";
import {
  NOTIFICATION_CHANNELS,
  NOTIFICATION_TYPES,
} from "@/constants/notifications";
import { UserRole } from "@/constants/userRole";
import { useNotification } from "@/contexts/NotiContext";
import NotificationService from "@/services/notificationService";
import UserService from "@/services/userService";
import {
  CreateNotificationRequest,
  NotificationType,
} from "@/types/notifications";
import { ClockCircleOutlined, SendOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Divider, Flex, Form, Typography } from "antd";
import dayjs from "dayjs";
import React, { useState } from "react";
import { useTranslations } from "next-intl";

const { Title, Text } = Typography;

interface CreateNotificationFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

const CreateNotificationForm: React.FC<CreateNotificationFormProps> = ({
  onSuccess,
  onCancel,
}) => {
  const tNotifications = useTranslations("notifications");
  const tCommon = useTranslations("common");
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [sendType, setSendType] = useState<"now" | "scheduled">("now");
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [targetAudience, setTargetAudience] = useState<
    "specific" | "all" | "role"
  >("specific");
  const notification = useNotification();

  // Fetch users for selection
  const fetchUsers = async (search: string) => {
    try {
      if (!search || search.length < 2) return [];

      const response = await UserService.getList({
        search,
        page: 1,
        pageSize: 20,
      });

      return response.data.map((user) => ({
        label: (
          <Flex justify="space-between" align="center">
            <span>{user.name}</span>
            <Text type="secondary" className="text-xs">
              {user.email}
            </Text>
          </Flex>
        ),
        value: user.id,
      }));
    } catch (error) {
      console.error("Failed to fetch users:", error);
      return [];
    }
  };
  // Handle form submission
  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);

      let userIds: string[] = [];

      // Determine user IDs based on target audience
      if (targetAudience === "specific") {
        userIds = selectedUsers;
      } else if (targetAudience === "all") {
        // Get all users - this might need pagination handling
        const allUsers = await UserService.getList({ pageSize: 10000 });
        userIds = allUsers.data.map((user) => user.id.toString());
      } else if (targetAudience === "role") {
        // Get users by role
        const roleUsers = await UserService.getList({
          role: values.target_role,
          pageSize: 10000,
        });
        userIds = roleUsers.data.map((user) => user.id.toString());
      }

      if (userIds.length === 0) {
        notification.notifyError("Please select at least one recipient");
        return;
      }

      const requestData: CreateNotificationRequest = {
        user_ids: userIds,
        type: values.type,
        channel: values.channel,
        title: values.title,
        content: values.content,
        scheduled_at:
          sendType === "scheduled"
            ? values.scheduled_at?.toISOString()
            : undefined,
        metadata: {
          target_audience: targetAudience,
          target_role: values.target_role,
          created_by: UserRole.ADMIN, // This should come from auth context
        },
      };

      if (userIds.length === 1) {
        await NotificationService.create(requestData);
        notification.notifySuccess("Notification created successfully");
      } else {
        const result = await NotificationService.sendBulk(requestData);
        notification.notifySuccess(
          `Bulk notification sent to ${result.data.count} users`
        );
      }

      form.resetFields();
      onSuccess?.();
    } catch (error) {
      notification.notifyError("Failed to send notification");
      console.error("Send notification error:", error);
    } finally {
      setLoading(false);
    }
  };

  // Template suggestions based on notification type
  const getTemplateSuggestion = (
    type: NotificationType
  ): { title: string; content: string } => {
    const templateKey = `templates.${type}`;

    try {
      return {
        title: tNotifications(`${templateKey}.title`),
        content: tNotifications(`${templateKey}.content`),
      };
    } catch (error) {
      return { title: "", content: "" };
    }
  };

  // Handle notification type change
  const handleTypeChange = (type: NotificationType) => {
    const template = getTemplateSuggestion(type);
    form.setFieldsValue({
      title: template.title,
      content: template.content,
    });
  };

  // Audience type options
  const audienceOptions = [
    { value: "specific", label: tNotifications("form.audience_options.specific") },
    { value: "all", label: tNotifications("form.audience_options.all") },
    { value: "role", label: tNotifications("form.audience_options.role") },
  ];

  // Role options
  const roleOptions = [
    { value: UserRole.EMPLOYER, label: tNotifications("form.role_options.employers") },
    { value: UserRole.JOB_SEEKER, label: tNotifications("form.role_options.job_seekers") },
  ];

  return (
    <>
      <Typography.Title level={4}>Notification Management</Typography.Title>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        className="mt-4"
      >
        {/* Notification Type and Channel */}
        <Flex gap="large" wrap>
          <div className="flex-1 min-w-64">
            <BaseSelect
              label={tNotifications("form.notification_type")}
              name="type"
              required
              options={NOTIFICATION_TYPES}
              placeholder={tNotifications("form.placeholders.select_notification_type")}
              onChange={handleTypeChange}
            />
          </div>
          <div className="flex-1 min-w-64">
            <BaseSelect
              label={tNotifications("form.channel")}
              name="channel"
              required
              options={NOTIFICATION_CHANNELS}
              placeholder={tNotifications("form.placeholders.select_delivery_channel")}
            />
          </div>
        </Flex>

        <Divider />

        {/* Target Audience */}
        <Title level={5}>{tNotifications("form.target_audience")}</Title>
        <BaseSelect
          label={tNotifications("form.audience_type")}
          name="audience_type"
          required
          options={audienceOptions}
          placeholder={tNotifications("form.placeholders.select_audience_type")}
          onChange={setTargetAudience}
          value={targetAudience}
        />

        {targetAudience === "specific" && (
          <DebounceSelect
            label={tNotifications("form.select_users")}
            name="users"
            fetchOptions={fetchUsers}
            placeholder={tNotifications("form.placeholders.search_and_select_users")}
            mode="multiple"
            required
            helpText={tNotifications("form.help_text.search_users")}
          />
        )}

        {targetAudience === "role" && (
          <BaseSelect
            label={tNotifications("form.target_role")}
            name="target_role"
            required
            options={roleOptions}
            placeholder={tNotifications("form.placeholders.select_user_role")}
          />
        )}

        {targetAudience === "all" && (
          <Alert
            message={tNotifications("form.send_to_all_users")}
            description={tNotifications("form.send_to_all_users_desc")}
            type="warning"
            showIcon
            className="mb-4"
          />
        )}

        <Divider />

        {/* Message Content */}
        <Title level={5}>{tNotifications("form.message_content")}</Title>
        <BaseInput
          label={tNotifications("form.title")}
          name="title"
          required
          placeholder={tNotifications("form.placeholders.enter_notification_title")}
          maxLength={100}
          showCount
        />

        <BaseTextArea
          label={tNotifications("form.content")}
          name="content"
          required
          placeholder={tNotifications("form.placeholders.enter_notification_content")}
          rows={6}
          maxLength={1000}
          showCount
          helpText={tNotifications("form.help_text.content_variables")}
        />

        <Divider />

        {/* Delivery Options */}
        <Title level={5}>{tNotifications("form.delivery_options")}</Title>
        <BaseSelect
          label={tNotifications("form.send_time")}
          name="send_time"
          required
          options={[
            { value: "now", label: tNotifications("form.send_options.now") },
            { value: "scheduled", label: tNotifications("form.send_options.scheduled") },
          ]}
          placeholder={tNotifications("form.placeholders.select_when_to_send")}
          onChange={setSendType}
          value={sendType}
        />

        {sendType === "scheduled" && (
          <BaseDatePicker
            label={tNotifications("form.scheduled_time")}
            name="scheduled_at"
            required
            placeholder={tNotifications("form.placeholders.select_date_and_time")}
            disabledDate={(current) => current && current < dayjs()}
          />
        )}

        {/* Actions */}
        <Flex gap="middle" justify="end" className="mt-6">
          <BaseButton label={tNotifications("form.cancel")} onClick={onCancel} />
          <BaseButton
            type="primary"
            htmlType="submit"
            loading={loading}
            icon={
              sendType === "now" ? <SendOutlined /> : <ClockCircleOutlined />
            }
            label={sendType === "now" ? tNotifications("form.send_now") : tNotifications("form.schedule_notification")}
          />
        </Flex>
      </Form>
    </>
  );
};

export default CreateNotificationForm;
