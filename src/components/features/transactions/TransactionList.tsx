"use client";
// app/admin/transactions/components/TransactionList.tsx
import BaseTable from "@/components/ui/tables/BaseTable";
import { FilterConfig } from "@/components/ui/tables/TableFilter";
import TransactionService from "@/services/transactionService";
import { Transaction } from "@/types/transaction";
import { Modal, Tag, Typography } from "antd";
import dayjs from "dayjs";
import { useState } from "react";
import { useTranslations } from "next-intl";

const { Text } = Typography;

const TransactionList = () => {
  const tTransactions = useTranslations("transactions");
  const [selectedTransaction, setSelectedTransaction] =
    useState<Transaction | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);

  const getStatusTag = (status: string) => {
    const statusMap: Record<string, { color: string; text: string }> = {
      completed: { color: "success", text: tTransactions("statuses.completed") },
      pending: { color: "processing", text: tTransactions("statuses.pending") },
      failed: { color: "error", text: tTransactions("statuses.failed") },
      refunded: { color: "warning", text: tTransactions("statuses.refunded") },
    };

    const { color, text } = statusMap[status] || {
      color: "default",
      text: status,
    };
    return <Tag color={color}>{text}</Tag>;
  };

  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 80,
    },
    {
      title: tTransactions("user"),
      dataIndex: "userName",
      key: "userName",
      render: (text: string, record: Transaction) => (
        <span>
          {text} <Text type="secondary">({record.userId})</Text>
        </span>
      ),
    },
    {
      title: tTransactions("date"),
      dataIndex: "timestamp",
      key: "timestamp",
      render: (text: string) => dayjs(text).format("MMM DD, YYYY HH:mm"),
      sorter: true,
    },
    {
      title: tTransactions("points"),
      dataIndex: "points",
      key: "points",
      render: (points: number) => <Text strong>{points.toLocaleString()}</Text>,
      sorter: true,
    },
    {
      title: tTransactions("amount"),
      dataIndex: "amount",
      key: "amount",
      render: (amount: number) => (
        <Text strong>${amount.toLocaleString()}</Text>
      ),
      sorter: true,
    },
    {
      title: tTransactions("payment_method"),
      dataIndex: "paymentMethod",
      key: "paymentMethod",
      render: (method: string) => {
        const methodMap: Record<string, string> = {
          credit_card: tTransactions("payment_methods.credit_card"),
          bank_transfer: tTransactions("payment_methods.bank_transfer"),
          e_wallet: tTransactions("payment_methods.e_wallet"),
          other: tTransactions("payment_methods.other"),
        };
        return methodMap[method] || method;
      },
    },
    {
      title: tTransactions("status"),
      dataIndex: "status",
      key: "status",
      render: (status: string) => getStatusTag(status),
    },
  ];

  const filters: FilterConfig[] = [
    {
      key: "status",
      label: "Status",
      type: "select",
      options: [
        { value: "completed", label: "Completed" },
        { value: "pending", label: "Pending" },
        { value: "failed", label: "Failed" },
        { value: "refunded", label: "Refunded" },
      ],
    },
    {
      key: "paymentMethod",
      label: "Payment Method",
      type: "select",
      options: [
        { value: "credit_card", label: "Credit Card" },
        { value: "bank_transfer", label: "Bank Transfer" },
        { value: "e_wallet", label: "E-Wallet" },
        { value: "other", label: "Other" },
      ],
    },
    {
      key: "dateRange",
      label: "Transaction Date",
      type: "dateRange",
    },
  ];

  const handleExportCsv = async () => {
    try {
      const blob = await TransactionService.exportTransactions();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `transactions-${new Date().toISOString().split("T")[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      a.remove();
    } catch (error) {
      console.error("Error exporting transactions:", error);
    }
  };

  const handleRowClick = (record: Transaction) => {
    setSelectedTransaction(record);
    setDetailModalVisible(true);
  };

  return (
    <>
      <BaseTable<Transaction>
        api={TransactionService.getTransactions}
        columns={columns}
        rowKey="id"
        title={tTransactions("transaction_history")}
        showSearch={true}
        searchPlaceholder={tTransactions("search_placeholder")}
        filters={filters}
        showActions={false}
        onRowClick={handleRowClick}
        initialParams={{ sort: "-timestamp" }}
        createBtnText={tTransactions("export_to_csv")}
        onCreate={handleExportCsv}
      />

      {/* Transaction Detail Modal */}
      <Modal
        title={tTransactions("transaction_details")}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedTransaction && (
          <div className="transaction-detail">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Text type="secondary">{tTransactions("transaction_id")}</Text>
                <div>
                  <Text copyable>{selectedTransaction.id}</Text>
                </div>
              </div>
              <div>
                <Text type="secondary">{tTransactions("status")}</Text>
                <div>{getStatusTag(selectedTransaction.status)}</div>
              </div>
              <div>
                <Text type="secondary">{tTransactions("user")}</Text>
                <div>{selectedTransaction.userName}</div>
              </div>
              <div>
                <Text type="secondary">{tTransactions("user_id")}</Text>
                <div>
                  <Text copyable>{selectedTransaction.userId}</Text>
                </div>
              </div>
              <div>
                <Text type="secondary">{tTransactions("date_time")}</Text>
                <div>
                  {dayjs(selectedTransaction.timestamp).format(
                    "MMM DD, YYYY HH:mm:ss"
                  )}
                </div>
              </div>
              <div>
                <Text type="secondary">{tTransactions("payment_method")}</Text>
                <div>
                  {selectedTransaction.paymentMethod === "credit_card" &&
                    tTransactions("payment_methods.credit_card")}
                  {selectedTransaction.paymentMethod === "bank_transfer" &&
                    tTransactions("payment_methods.bank_transfer")}
                  {selectedTransaction.paymentMethod === "e_wallet" &&
                    tTransactions("payment_methods.e_wallet")}
                  {selectedTransaction.paymentMethod === "other" &&
                    tTransactions("payment_methods.other")}
                </div>
              </div>
              <div>
                <Text type="secondary">{tTransactions("points")}</Text>
                <div>
                  <Text strong>
                    {selectedTransaction.points.toLocaleString()}
                  </Text>
                </div>
              </div>
              <div>
                <Text type="secondary">{tTransactions("amount")}</Text>
                <div>
                  <Text strong>
                    ${selectedTransaction.amount.toLocaleString()}
                  </Text>
                </div>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </>
  );
};

export default TransactionList;
