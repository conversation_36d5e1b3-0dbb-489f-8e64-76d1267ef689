"use client";
import BasicInfoTab from "@/components/features/posts/forms/BasicInfoTab";
import BenefitsTab from "@/components/features/posts/forms/BenifitsTab";
import DocumentsTab from "@/components/features/posts/forms/DocumentsTab";
import JobDetailsTab, {
  DaySchedule,
} from "@/components/features/posts/forms/JobDetailsTab";
import PostSettingsTab from "@/components/features/posts/forms/PostSettingsTab";
import SkillsTab from "@/components/features/posts/forms/SkillsTab";
import BaseButton from "@/components/ui/buttons/BaseButton";
import BranchService from "@/services/branchService";
import PostService from "@/services/postService";
import { Post, PostStatus } from "@/types/post";
import { CheckCircleOutlined, SaveOutlined } from "@ant-design/icons";
import {
  Alert,
  Card,
  Flex,
  Form,
  List,
  message,
  Modal,
  Spin,
  Tabs,
  Typography,
} from "antd";
import dayjs from "dayjs";
import { useTranslations } from "next-intl";
import { useParams, useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { AddressFormData } from "@/types/address";

const { Title } = Typography;

interface PostFormPageProps {
  isEdit?: boolean;
}

type PostFormSchemaType = Omit<Post, "salary"> & {
  salaryCurrency: string;
  salaryPeriod: string;
  salaryMin: number;
  salaryMax: number;
  workSchedule: DaySchedule[];
  address?: AddressFormData;
  companyId?: number;
  branchId?: number;
  workType?: string;
};

const PostFormPage: React.FC<PostFormPageProps> = ({ isEdit = false }) => {
  const router = useRouter();
  const params = useParams();
  const postId = isEdit ? (params.id as string) : undefined;

  const [form] = Form.useForm<PostFormSchemaType>();
  const tCommon = useTranslations("common");
  const tPost = useTranslations("posts");
  const tMessages = useTranslations("messages");

  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [initialValues, setInitialValues] = useState<Partial<Post> | null>(
    null
  );
  const [initialCompanyId, setInitialCompanyId] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("basic");
  const [validationModalOpen, setValidationModalOpen] = useState(false);
  const [missingFields, setMissingFields] = useState<
    Array<{ field: string; tab: string; label: string }>
  >([]);
  const [addressDisabled, setAddressDisabled] = useState(true);

  const branchId = Form.useWatch("branchId", form);
  const companyId = Form.useWatch("companyId", form);

  const items = [
    {
      key: "basic",
      label: tPost("tabs.basic_information"),
      children: <BasicInfoTab form={form} addressDisabled={addressDisabled} />,
    },
    {
      key: "jobDetails",
      label: tPost("tabs.job_details"),
      children: <JobDetailsTab form={form} />,
    },
    {
      key: "skills",
      label: tPost("tabs.skills"),
      children: <SkillsTab form={form} />,
    },
    {
      key: "benefits",
      label: tPost("tabs.benefits"),
      children: <BenefitsTab form={form} />,
    },
    {
      key: "documents",
      label: tPost("tabs.required_documents"),
      children: <DocumentsTab />,
    },
    {
      key: "settings",
      label: tPost("tabs.post_settings"),
      children: <PostSettingsTab />,
    },
  ];

  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  const handlePreviousTab = () => {
    const currentIndex = items.findIndex((item) => item.key === activeTab);
    if (currentIndex > 0) {
      setActiveTab(items[currentIndex - 1].key);
    }
  };

  const handleNextTab = () => {
    const currentIndex = items.findIndex((item) => item.key === activeTab);
    if (currentIndex < items.length - 1) {
      setActiveTab(items[currentIndex + 1].key);
    }
  };

  const isFirstTab = activeTab === items[0].key;
  const isLastTab = activeTab === items[items.length - 1].key;

  // Validate required fields function
  const validateRequiredFields = () => {
    const values = form.getFieldsValue();
    const missing: Array<{ field: string; tab: string; label: string }> = [];

    // Basic Info Tab
    if (!values.title?.trim()) {
      missing.push({ field: "title", tab: "basic", label: tPost("job_title") });
    }
    if (!values.companyId) {
      missing.push({
        field: "companyId",
        tab: "basic",
        label: tCommon("fields.company"),
      });
    }
    if (!values.branchId) {
      missing.push({
        field: "branchId",
        tab: "basic",
        label: tCommon("fields.branch"),
      });
    }
    if (!values.description?.trim()) {
      missing.push({
        field: "description",
        tab: "basic",
        label: tPost("job_description"),
      });
    }

    // Job Details Tab
    if (!values.jobType) {
      missing.push({
        field: "jobType",
        tab: "jobDetails",
        label: tPost("job_type"),
      });
    }
    if (!values.salaryMin) {
      missing.push({
        field: "salaryMin",
        tab: "jobDetails",
        label: tPost("salary_min"),
      });
    }
    if (!values.salaryMax) {
      missing.push({
        field: "salaryMax",
        tab: "jobDetails",
        label: tPost("salary_max"),
      });
    }
    if (!values.salaryCurrency) {
      missing.push({
        field: "salaryCurrency",
        tab: "jobDetails",
        label: "Salary Currency",
      });
    }
    if (!values.salaryPeriod) {
      missing.push({
        field: "salaryPeriod",
        tab: "jobDetails",
        label: "Salary Period",
      });
    }
    if (!values.experienceLevel) {
      missing.push({
        field: "experienceLevel",
        tab: "jobDetails",
        label: tPost("experience_level"),
      });
    }
    if (!values.positions) {
      missing.push({
        field: "positions",
        tab: "jobDetails",
        label: tPost("positions"),
      });
    }

    // Work Schedule validation
    if (
      !values.workSchedule ||
      !values.workSchedule.some((day: any) => day.isActive)
    ) {
      missing.push({
        field: "workSchedule",
        tab: "jobDetails",
        label: tPost("work_schedule.other_periods"),
      });
    }

    // Post Settings Tab
    if (!values.postDate) {
      missing.push({ field: "postDate", tab: "settings", label: "Post Date" });
    }
    if (!values.expireDate) {
      missing.push({
        field: "expireDate",
        tab: "settings",
        label: "Expiry Date",
      });
    }

    return missing;
  };

  // Fetch post data if in edit mode
  useEffect(() => {
    if (isEdit && postId) {
      const fetchPostData = async () => {
        try {
          setLoading(true);
          const postData = (await PostService.getDetail(postId)).data;

          // Format dates for form input
          const formattedPost = {
            ...postData,
            companyId: (postData as any).companyId,
            branchId: (postData as any).branchId,
            salaryCurrency: postData.salary?.currency || "VND",
            salaryPeriod: postData.salary?.period || "MONTHLY",
            salaryMin: postData.salary?.min || 0,
            salaryMax: postData.salary?.max || 0,
            jobType: postData.jobType || "",
            workType: postData.workType || "",
            experienceLevel: postData.experienceLevel || "",
            positions: postData.positions || "",
            workingInformation: postData.workingInformation || [],
            skills: postData.skills || [],
            benefits: postData.benefits || [],
            requiredDocuments: postData.requiredDocuments || [],
            postDate: postData.postDate
              ? dayjs(postData.postDate).toISOString()
              : "",
            expireDate: postData.expireDate
              ? dayjs(postData.expireDate).toISOString()
              : "",
          };
          console.log("formattedPost", formattedPost);
          setInitialValues(formattedPost);
          setInitialCompanyId((postData as any).companyId || null);
          form.setFieldsValue(formattedPost);
        } catch (err) {
          console.error("Error fetching post data:", err);
          setError(tMessages("errors.failed_to_load_post_data"));
        } finally {
          setLoading(false);
        }
      };

      fetchPostData();
    } else {
      // Set default values for new post
      const defaultValues = {
        status: tPost("status.draft") as PostStatus,
        isFeatured: false,
        featuredDuration: undefined,
        urgentHiring: false,
        positions: "1",
        skills: [],
        benefits: [],
        salaryCurrency: "VND",
        salaryPeriod: "MONTHLY",
        requiredDocuments: [
          {
            name: tPost("documents.resume_cv"),
            required: true,
          },
        ],
        postDate: "2022-12-12",
        expireDate: "2022-12-12",
      };

      setInitialValues(defaultValues);
      form.setFieldsValue(defaultValues);
    }
  }, [isEdit, postId, form, tMessages, tPost]);

  const handleSave = async (published: boolean) => {
    console.log("Saving post with published status:", form.getFieldsValue());

    // If publishing, check required fields first
    if (published) {
      const missing = validateRequiredFields();
      if (missing.length > 0) {
        setMissingFields(missing);
        setValidationModalOpen(true);
        return;
      }
    }

    try {
      await form.validateFields();
      const { salaryMin, salaryMax, salaryCurrency, salaryPeriod, ...values } =
        form.getFieldsValue();

      // Prepare data for submission
      const safeSkills = Array.isArray(values.skills) ? values.skills : [];
      const safeBenefits = Array.isArray(values.benefits)
        ? values.benefits
        : [];
      const postData: Post = {
        ...values,
        skills: safeSkills,
        benefits: safeBenefits,
        salary: {
          min: salaryMin || 0,
          max: salaryMax || 0,
          currency: salaryCurrency || "VND",
          period: salaryPeriod || "MONTHLY",
        },
        status: published ? tPost("status.published") : tPost("status.draft"),
        postDate: dayjs(values.postDate).toISOString(),
        expireDate: dayjs(values.expireDate).toISOString(),
        workingInformation: values.workSchedule
          ? values.workSchedule
              .filter((schedule: any) => schedule.isActive)
              .map((schedule: any) => ({
                workDays: schedule.day,
                workHours: schedule.timeRanges
                  .map((range: any) => {
                    if (range.start && range.end) {
                      return `${range.start.format(
                        "HH:mm"
                      )} - ${range.end.format("HH:mm")}`;
                    }
                    return "";
                  })
                  .filter((timeStr: string) => timeStr)
                  .join(", "),
              }))
          : [],
      };

      setSaveLoading(true);

      if (isEdit && postId) {
        await PostService.update(postId, postData);
        message.success(tMessages("success.post_updated"));
      } else {
        await PostService.create(postData);
        message.success(tMessages("success.post_created"));
      }

      // Redirect back to posts list based on current context
      const currentPath = window.location.pathname;
      if (currentPath.includes("job-seekers-management")) {
        router.push("/job-seekers-management/posts");
      } else if (currentPath.includes("employers-management")) {
        router.push("/employers-management/posts");
      } else {
        router.push("/features/posts"); // fallback
      }
    } catch (err) {
      console.error("Error saving post:", err);
      message.error(tMessages("errors.failed_to_save_post"));
    } finally {
      setSaveLoading(false);
    }
  };

  useEffect(() => {
    const companyIdValue =
      typeof companyId === "object" ? (companyId as any)?.value : companyId;
    const branchIdValue =
      typeof branchId === "object" ? (branchId as any)?.value : branchId;
    if (branchIdValue && companyIdValue) {
      BranchService.getDetail(
        String(companyIdValue),
        String(branchIdValue)
      ).then((res) => {
        const addr = res.data.address;
        if (
          addr &&
          typeof addr === "object" &&
          (addr as any).province &&
          (addr as any).district &&
          (addr as any).ward
        ) {
          form.setFieldValue("address", {
            provinceCode: (addr as any).province.code,
            districtCode: (addr as any).district.code,
            wardCode: (addr as any).ward.code,
            detailAddress: (addr as any).detailAddress || "",
          });
        }
      });
    }
  }, [branchId, companyId]);

  useEffect(() => {
    // Only reset branchId when user changes company after initial load
    // Don't reset during initial data loading in edit mode
    if (companyId && initialCompanyId !== null && !loading) {
      const currentCompanyId =
        typeof companyId === "object" ? (companyId as any)?.value : companyId;

      // Only reset if company actually changed from initial value
      if (currentCompanyId !== initialCompanyId) {
        form.setFieldValue("branchId", "");
      }
    }
  }, [companyId, initialCompanyId, loading]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message={tCommon("status.error")}
        description={error}
        type="error"
      />
    );
  }

  return (
    <Card className="space-y-6">
      <Title level={3}>
        {isEdit ? tPost("form.edit_post") : tPost("form.create_new_post")}
      </Title>

      <Form<PostFormSchemaType>
        form={form}
        layout="vertical"
        initialValues={initialValues || {}}
        autoComplete="off"
      >
        <Tabs activeKey={activeTab} onChange={handleTabChange} items={items} />

        <Flex className="!mt-4" justify="space-between">
          <Flex gap="middle">
            <BaseButton
              label={tCommon("actions.cancel")}
              onClick={() => {
                const currentPath = window.location.pathname;
                if (currentPath.includes("job-seekers-management")) {
                  router.push("/job-seekers-management/posts");
                } else if (currentPath.includes("employers-management")) {
                  router.push("/employers-management/posts");
                } else {
                  router.push("/features/posts"); // fallback
                }
              }}
            />
            <BaseButton
              icon={<SaveOutlined />}
              type="primary"
              label={tPost("actions.save_as_draft")}
              loading={saveLoading}
              onClick={() => handleSave(false)}
            />
          </Flex>

          <Flex gap="middle">
            {!isFirstTab && (
              <BaseButton
                label={tCommon("actions.previous")}
                onClick={handlePreviousTab}
              />
            )}

            {!isLastTab ? (
              <BaseButton
                type="primary"
                label={tCommon("actions.next")}
                onClick={handleNextTab}
              />
            ) : (
              <BaseButton
                type="primary"
                label={
                  isEdit
                    ? tCommon("actions.update")
                    : tCommon("actions.publish")
                }
                icon={<CheckCircleOutlined />}
                onClick={() => handleSave(true)}
                loading={saveLoading}
              />
            )}
          </Flex>
        </Flex>
      </Form>

      {/* Validation Error Modal */}
      <Modal
        title="Missing Required Fields"
        open={validationModalOpen}
        onOk={() => setValidationModalOpen(false)}
        onCancel={() => setValidationModalOpen(false)}
        footer={[
          <BaseButton
            key="close"
            type="primary"
            onClick={() => setValidationModalOpen(false)}
            label="OK"
          />,
        ]}
      >
        <div className="mb-4">
          <Typography.Text>
            Please fill in the following required fields before publishing:
          </Typography.Text>
          <br />
          <Typography.Text type="secondary" className="text-sm">
            Click on any field to navigate to its tab.
          </Typography.Text>
        </div>
        <List
          size="small"
          dataSource={missingFields}
          renderItem={(item) => (
            <List.Item
              className="cursor-pointer hover:bg-gray-50 rounded px-2"
              onClick={() => {
                setActiveTab(item.tab);
                setValidationModalOpen(false);
              }}
            >
              <Typography.Text>
                <strong>{item.label}</strong> -{" "}
                {tPost(
                  `tabs.${
                    item.tab === "basic"
                      ? "basic_information"
                      : item.tab === "jobDetails"
                      ? "job_details"
                      : item.tab === "settings"
                      ? "post_settings"
                      : item.tab
                  }`
                )}
              </Typography.Text>
            </List.Item>
          )}
        />
      </Modal>
    </Card>
  );
};

export default PostFormPage;
