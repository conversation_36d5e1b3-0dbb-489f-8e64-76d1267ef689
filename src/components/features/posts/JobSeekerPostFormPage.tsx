import React, { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import {
  Form,
  Input,
  Button,
  Select,
  Switch,
  message,
  Spin,
  Space,
  Card,
  Typography,
  InputNumber,
  DatePicker,
  Row,
  Col,
} from "antd";
import DebounceSelect from "@/components/ui/selects/DebounceSelect";
import AddressFormField from "@/components/features/address/AddressFormField";
import UserService from "@/services/userService";
import { UserRole } from "@/constants/userRole";
import JobSeekerPostService from "@/services/jobSeekerPostService";
import type { JobSeekerPost } from "@/types/post";
import { useTranslations } from "next-intl";
import { getFeatureDurationSelectOptions } from "@/enums/featureDuration";
import { getContractTypeSelectOptions } from "@/enums/contractType";
import { getSalaryPeriodSelectOptions } from "@/enums/salaryPeriod";
import styles from "./JobSeekerPostFormPage.module.css";
import {
  SaveOutlined,
  UserOutlined,
  BookOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  EnvironmentOutlined,
  PlusOutlined,
  DeleteOutlined,
  GlobalOutlined,
  BankOutlined,
  SettingOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";

const { TextArea } = Input;
const { Title } = Typography;

interface Props {
  isEdit?: boolean;
}

const statusOptions = [
  { value: "ACTIVE", label: "Active" },
  { value: "DRAFT", label: "Draft" },
  { value: "PENDING", label: "Pending" },
  { value: "REJECTED", label: "Rejected" },
  { value: "EXPIRED", label: "Expired" },
  { value: "PAUSED", label: "Paused" },
  { value: "CLOSED", label: "Closed" },
  { value: "reopen", label: "Reopen" },
];
const workingDayOptions = [
  { value: "MONDAY", label: "Monday" },
  { value: "TUESDAY", label: "Tuesday" },
  { value: "WEDNESDAY", label: "Wednesday" },
  { value: "THURSDAY", label: "Thursday" },
  { value: "FRIDAY", label: "Friday" },
  { value: "SATURDAY", label: "Saturday" },
  { value: "SUNDAY", label: "Sunday" },
];
const workingShiftOptions = [
  { value: "MORNING", label: "Morning" },
  { value: "AFTERNOON", label: "Afternoon" },
  { value: "EVENING", label: "Evening" },
];
const featureDurationOptions = getFeatureDurationSelectOptions();

const jobTypeOptions = [
  { value: "FULL_TIME", label: "Full Time" },
  { value: "PART_TIME", label: "Part Time" },
  { value: "CONTRACT", label: "Contract" },
  { value: "INTERNSHIP", label: "Internship" },
];

const contractTypeOptions = getContractTypeSelectOptions();

const workTypeOptions = [
  { value: "ONSITE", label: "On-site" },
  { value: "REMOTE", label: "Remote" },
  { value: "HYBRID", label: "Hybrid" },
];

const educationLevelOptions = [
  { value: "HIGH_SCHOOL", label: "High School" },
  { value: "ASSOCIATE", label: "Associate Degree" },
  { value: "BACHELOR", label: "Bachelor's Degree" },
  { value: "MASTER", label: "Master's Degree" },
  { value: "DOCTORATE", label: "Doctorate" },
  { value: "OTHER", label: "Other" },
];

// Fetch job seeker users for DebounceSelect
const fetchJobSeekerOptions = async (search: string) => {
  const res = await UserService.getList({
    search,
    role: UserRole.JOB_SEEKER,
    page: 0,
    limit: 50,
  });
  return { data: res.data };
};

const JobSeekerPostFormPage: React.FC<Props> = ({ isEdit }) => {
  const [form] = Form.useForm();
  const params = useParams();
  const router = useRouter();
  const t = useTranslations("job_seeker_post");
  const tCommon = useTranslations("common");
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [initialValues, setInitialValues] = useState<Partial<JobSeekerPost>>(
    {}
  );

  const postId = params.id as string;

  // Watch isFeatureJob to enable/disable featureDuration
  const isFeatureJob = Form.useWatch("isFeatureJob", form);

  // moved currencyOptions, periodOptions here to use t
  const currencyOptions = [
    { value: "VND", label: "VND" },
    { value: "USD", label: "USD" },
    { value: "EUR", label: "EUR" },
    { value: "GBP", label: "GBP" },
    { value: "CNY", label: "CNY" },
  ];
  const periodOptions = getSalaryPeriodSelectOptions();

  useEffect(() => {
    if (isEdit && postId) {
      setLoading(true);
      JobSeekerPostService.getDetail(postId)
        .then((res) => {
          // convert date string to dayjs for DatePicker
          const data = {
            ...res.data,
            startTime: res.data.startTime
              ? dayjs(res.data.startTime)
              : undefined,
            endTime: res.data.endTime ? dayjs(res.data.endTime) : undefined,
          };
          setInitialValues(data);
          form.setFieldsValue(data);
        })
        .catch(() => message.error("Failed to load post data"))
        .finally(() => setLoading(false));
    }
  }, [isEdit, postId, form]);

  // Clear featureDuration when isFeatureJob is disabled
  useEffect(() => {
    if (!isFeatureJob) {
      form.setFieldValue("featureDuration", undefined);
    }
  }, [isFeatureJob, form]);

  const onFinish = async (values: any) => {
    setSubmitting(true);
    try {
      // convert dayjs to ISO string format and prepare location data
      const submitData = {
        ...values,
        startTime: values.startTime ? values.startTime.toISOString() : null,
        endTime: values.endTime ? values.endTime.toISOString() : null,
        // Transform location data to match API structure
        location: values.location
          ? {
              provinceCode: values.location.provinceCode,
              districtCode: values.location.districtCode,
              wardCode: values.location.wardCode,
              detailAddress: values.location.detailAddress,
              // Note: provinceName, districtName, wardName will be populated by backend
            }
          : null,
      };
      if (isEdit && postId) {
        await JobSeekerPostService.update(postId, submitData);
        message.success(tCommon("messages.success.updated_successfully"));
        router.push(`/job-seekers-management/posts/${postId}`);
      } else {
        await JobSeekerPostService.create(submitData);
        message.success(tCommon("messages.success.created_successfully"));
        router.push(`/job-seekers-management/posts`);
      }
    } catch (err) {
      message.error(tCommon("messages.error.failed_to_save_post"));
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <div className="flex justify-center items-center min-h-[300px]">
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Title level={3} style={{ margin: 0 }}>
                {isEdit
                  ? t("edit_job_seeker_post") || "Edit Job Seeker Post"
                  : t("create_new_post")}
              </Title>
            </Space>
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={() => form.submit()}
              size="large"
              loading={submitting}
            >
              {isEdit ? tCommon("actions.update") : tCommon("actions.create")}
            </Button>
          </Col>
        </Row>
      </Card>

      <Form
        form={form}
        layout="vertical"
        initialValues={initialValues}
        onFinish={onFinish}
        autoComplete="off"
        requiredMark={false}
      >
        {/* Basic Info */}
        <Card
          title={
            <Title level={4} style={{ margin: 0 }}>
              <UserOutlined /> {t("basic_information")}
            </Title>
          }
          style={{ marginBottom: 24 }}
        >
          <Form.Item
            name="title"
            label={t("title")}
            rules={[
              {
                required: true,
                message: t("validation.please_enter_job_title"),
              },
            ]}
          >
            <Input size="large" />
          </Form.Item>
          <Form.Item name="industry" label={t("industry")}>
            <Input size="large" />
          </Form.Item>
          <DebounceSelect
            name="jobSeekerId"
            label="Job Seeker"
            fetchOptions={fetchJobSeekerOptions}
            placeholder="Search and select a job seeker..."
            required
          />
          <Form.Item name="status" label={t("status")}>
            <Select options={statusOptions} size="large" />
          </Form.Item>
          <Form.Item name="description" label={tCommon("fields.description")}>
            <TextArea rows={4} showCount maxLength={500} />
          </Form.Item>
        </Card>

        {/* Skills */}
        <Card
          title={
            <Title level={4} style={{ margin: 0 }}>
              <TrophyOutlined /> {t("skills")}
            </Title>
          }
          style={{ marginBottom: 24 }}
        >
          <Form.List name="skills">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Card
                    key={key}
                    type="inner"
                    className={styles.innerCard}
                  >
                    <Row gutter={16}>
                      <Col span={20}>
                        <Form.Item
                          {...restField}
                          name={name}
                          label="Skill"
                          rules={[
                            {
                              required: true,
                              message: "Please enter skill",
                            },
                          ]}
                        >
                          <Input
                            placeholder={
                              t("skills_placeholder") ||
                              "e.g., JavaScript, React, Node.js"
                            }
                            size="large"
                          />
                        </Form.Item>
                      </Col>
                      <Col span={4}>
                        <Form.Item label=" ">
                          <Button
                            type="text"
                            danger
                            icon={<DeleteOutlined />}
                            onClick={() => remove(name)}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Card>
                ))}
                <Button
                  type="dashed"
                  onClick={() => add()}
                  icon={<PlusOutlined />}
                  block
                >
                  {t("add_skill") || "Add Skill"}
                </Button>
              </>
            )}
          </Form.List>
        </Card>
        {/* Languages */}
        <Card
          title={
            <Title level={4} style={{ margin: 0 }}>
              <GlobalOutlined /> {t("languages")}
            </Title>
          }
          style={{ marginBottom: 24 }}
        >
          <Form.List name="languages">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Card
                    key={key}
                    type="inner"
                    className={styles.innerCard}
                  >
                    <Row gutter={16}>
                      <Col span={22}>
                        <Form.Item
                          {...restField}
                          name={name}
                          label={t("languages")}
                          rules={[
                            {
                              required: true,
                              message: `${tCommon("placeholders.please_input")} ${t("languages").toLowerCase()}`,
                            },
                          ]}
                        >
                          <Input
                            placeholder={t("languages_placeholder")}
                            size="large"
                          />
                        </Form.Item>
                      </Col>
                      <Col span={2}>
                        <Form.Item label=" ">
                          <Button
                            type="text"
                            danger
                            icon={<DeleteOutlined />}
                            onClick={() => remove(name)}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Card>
                ))}
                <Button
                  type="dashed"
                  onClick={() => add()}
                  icon={<PlusOutlined />}
                  block
                >
                  {t("add_language")}
                </Button>
              </>
            )}
          </Form.List>
        </Card>

        {/* Education */}
        <Card
          title={
            <Title level={4} style={{ margin: 0 }}>
              <BookOutlined /> {t("education") || "Education"}
            </Title>
          }
          style={{ marginBottom: 24 }}
        >
          <Form.Item name="educationLevel" label={t("education_level")}>
            <Select options={educationLevelOptions} size="large" />
          </Form.Item>
          <Form.Item name="educationDetail" label={t("education_detail")}>
            <Input size="large" />
          </Form.Item>
        </Card>

        {/* Experiences */}
        <Card
          title={
            <Title level={4} style={{ margin: 0 }}>
              <BankOutlined /> {t("experiences") || "Experiences"}
            </Title>
          }
          style={{ marginBottom: 24 }}
        >
          <Form.List name="experiences">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Card
                    key={key}
                    type="inner"
                    className={styles.innerCard}
                  >
                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          {...restField}
                          name={[name, "industry"]}
                          label="Industry"
                          rules={[
                            {
                              required: true,
                              message: "Please enter industry",
                            },
                          ]}
                        >
                          <Input
                            placeholder="e.g., Software Development"
                            size="large"
                          />
                        </Form.Item>
                      </Col>
                      <Col span={10}>
                        <Form.Item
                          {...restField}
                          name={[name, "yearOfExperience"]}
                          label="Years of Experience"
                          rules={[
                            {
                              required: true,
                              message: "Please enter years of experience",
                            },
                          ]}
                        >
                          <InputNumber
                            min={0}
                            max={50}
                            placeholder="Years"
                            style={{ width: "100%" }}
                            size="large"
                          />
                        </Form.Item>
                      </Col>
                      <Col span={2}>
                        <Form.Item label=" ">
                          <Button
                            type="text"
                            danger
                            icon={<DeleteOutlined />}
                            onClick={() => remove(name)}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Card>
                ))}
                <Button
                  type="dashed"
                  onClick={() => add()}
                  icon={<PlusOutlined />}
                  block
                >
                  {t("add_experience") || "Add Experience"}
                </Button>
              </>
            )}
          </Form.List>
        </Card>

        {/* Work Schedule */}
        <Card
          title={
            <Title level={4} style={{ margin: 0 }}>
              <ClockCircleOutlined /> {t("work_schedule") || "Work Schedule"}
            </Title>
          }
          style={{ marginBottom: 24 }}
        >
          <Form.Item name="workingDays" label={t("working_days")}>
            <Select mode="multiple" options={workingDayOptions} size="large" />
          </Form.Item>
          <Form.Item name="workingShifts" label={t("working_shifts")}>
            <Select
              mode="multiple"
              options={workingShiftOptions}
              size="large"
            />
          </Form.Item>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="workingHourPerDay"
                label={t("working_hour_per_day")}
              >
                <InputNumber
                  min={1}
                  max={24}
                  style={{ width: "100%" }}
                  size="large"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="jobType" label={t("jobType")}>
                <Select options={jobTypeOptions} size="large" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="startTime" label={t("start_time")}>
                <DatePicker showTime style={{ width: "100%" }} size="large" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="endTime" label={t("end_time")}>
                <DatePicker showTime style={{ width: "100%" }} size="large" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="contractType" label={t("contractType")}>
                <Select options={contractTypeOptions} size="large" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="workType" label={t("workType")}>
                <Select options={workTypeOptions} size="large" />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Salary */}
        <Card
          title={
            <Title level={4} style={{ margin: 0 }}>
              <DollarOutlined /> {t("salary") || "Salary"}
            </Title>
          }
          style={{ marginBottom: 24 }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name={["salary", "min"]} label={t("salary_min")}>
                <InputNumber
                  min={0}
                  style={{ width: "100%" }}
                  size="large"
                  placeholder="Enter minimum salary"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name={["salary", "max"]} label={t("salary_max")}>
                <InputNumber
                  min={0}
                  style={{ width: "100%" }}
                  size="large"
                  placeholder="Enter maximum salary"
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={["salary", "currency"]}
                label={t("salary_currency")}
              >
                <Select options={currencyOptions} size="large" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name={["salary", "period"]} label={t("salary_period")}>
                <Select options={periodOptions} size="large" />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Location */}
        <Card
          title={
            <Title level={4} style={{ margin: 0 }}>
              <EnvironmentOutlined /> {t("location")}
            </Title>
          }
          style={{ marginBottom: 24 }}
        >
          <AddressFormField name="location" required className="mt-4" />
        </Card>

        {/* Feature & Status */}
        <Card
          title={
            <Title level={4} style={{ margin: 0 }}>
              <SettingOutlined />{" "}
              {t("other_information") || "Other Information"}
            </Title>
          }
          style={{ marginBottom: 24 }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="isFeatureJob" label={t("featureJob")}>
                <Switch
                  checkedChildren={tCommon("responses.yes")}
                  unCheckedChildren={tCommon("responses.no")}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="featureDuration"
                label={t("feature_duration")}
                rules={[
                  {
                    required: isFeatureJob,
                    message:
                      "Please select feature duration when feature job is enabled",
                  },
                ]}
              >
                <Select
                  options={featureDurationOptions}
                  size="large"
                  disabled={!isFeatureJob}
                  placeholder={
                    isFeatureJob
                      ? "Select feature duration"
                      : "Enable feature job first"
                  }
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="isEnableEmailNotification"
                label={t("isEnableEmailNotification")}
              >
                <Switch
                  checkedChildren={tCommon("responses.yes")}
                  unCheckedChildren={tCommon("responses.no")}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="isEnableInformation"
                label={t("isEnableInformation")}
              >
                <Switch
                  checkedChildren={tCommon("responses.yes")}
                  unCheckedChildren={tCommon("responses.no")}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="isAutoAcceptInterviewInvitation"
                label={t("isAutoAcceptInterviewInvitation")}
              >
                <Switch
                  checkedChildren={tCommon("responses.yes")}
                  unCheckedChildren={tCommon("responses.no")}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="isReady" label={t("isReady")}>
                <Switch
                  checkedChildren={tCommon("responses.yes")}
                  unCheckedChildren={tCommon("responses.no")}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Save Button */}
        <Card>
          <Row justify="end">
            <Space size="large">
              <Button size="large" onClick={() => router.back()}>
                Cancel
              </Button>
              <Button
                type="primary"
                size="large"
                icon={<SaveOutlined />}
                onClick={() => form.submit()}
                loading={submitting}
              >
                {isEdit ? tCommon("actions.update") : tCommon("actions.create")}
              </Button>
            </Space>
          </Row>
        </Card>
      </Form>
    </div>
  );
};

export default JobSeekerPostFormPage;
