/* JobSeekerPostFormPage Styles */

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.formCard {
  margin-bottom: 24px;
}

.cardTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
}

/* Inner cards for form lists */
.innerCard {
  margin-bottom: 16px;
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
}

.innerCard:hover {
  background-color: #f5f5f5;
  transition: background-color 0.3s ease;
}

/* Form sections */
.formSection {
  margin-bottom: 24px;
}

.formRow {
  margin-bottom: 16px;
}

/* Action buttons */
.actionButtons {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 24px;
}

.addButton {
  width: 100%;
  margin-top: 16px;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .formCard {
    margin-bottom: 16px;
  }
  
  .innerCard {
    margin-bottom: 12px;
  }
  
  .actionButtons {
    flex-direction: column;
    gap: 12px;
  }
}

@media (max-width: 576px) {
  .container {
    padding: 12px;
  }
  
  .cardTitle {
    font-size: 16px;
  }
}

/* Skills, Languages, Experiences specific styles */
.skillItem,
.languageItem,
.experienceItem {
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.skillItem:hover,
.languageItem:hover,
.experienceItem:hover {
  background-color: #f5f5f5;
  border-color: #40a9ff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Delete button styling */
.deleteButton {
  color: #ff4d4f;
  border: none;
  background: transparent;
}

.deleteButton:hover {
  color: #ff7875;
  background-color: rgba(255, 77, 79, 0.1);
}

/* Form validation styles */
.errorText {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}

.requiredField::before {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

/* Loading states */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* Success/Error messages */
.successMessage {
  color: #52c41a;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.errorMessage {
  color: #ff4d4f;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
}

/* Work schedule specific styles */
.workScheduleGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.workScheduleItem {
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
}

/* Salary section */
.salarySection {
  background-color: #f9f9f9;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 24px;
}

.salaryRow {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.salaryField {
  flex: 1;
}

@media (max-width: 768px) {
  .salaryRow {
    flex-direction: column;
    gap: 12px;
  }
}

/* Location section */
.locationSection {
  background-color: #f0f9ff;
  border: 1px solid #bae7ff;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 24px;
}

/* Other information section */
.otherInfoSection {
  background-color: #f6f6f6;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 24px;
}

.switchRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 0;
  border-bottom: 1px solid #e8e8e8;
}

.switchRow:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

/* Print styles */
@media print {
  .container {
    padding: 0;
    max-width: none;
  }
  
  .actionButtons {
    display: none;
  }
  
  .deleteButton {
    display: none;
  }
  
  .addButton {
    display: none;
  }
  
  .formCard {
    break-inside: avoid;
    margin-bottom: 20px;
  }
}
