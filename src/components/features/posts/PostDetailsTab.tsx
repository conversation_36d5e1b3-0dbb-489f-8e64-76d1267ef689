"use client";
import type { Post } from "@/types/post";
import { Card, Descriptions, Space, Tag, Typography } from "antd";
import React from "react";
import dayjs from "dayjs";
import { useTranslations } from "next-intl";
import { getFeatureDurationLabel } from "@/enums/featureDuration";
import { getSalaryPeriodShortLabel } from "@/enums/salaryPeriod";

const { Text, Paragraph } = Typography;

interface PostDetailsTabProps {
  post: Post;
}

const PostDetailsTab: React.FC<PostDetailsTabProps> = ({ post }) => {
  const t = useTranslations("posts");
  const tCommon = useTranslations("common");

  return (
    <Card>
      <Descriptions layout="vertical" bordered>
        <Descriptions.Item label={t("company")} span={2}>
          {post.companyName || ""}
        </Descriptions.Item>
        <Descriptions.Item label={t("location")}>
          {post.location}
        </Descriptions.Item>
        <Descriptions.Item label={t("post_date")}>
          {post.postDate ? dayjs(post.postDate).format("DD/MM/YYYY") : ""}
        </Descriptions.Item>
        <Descriptions.Item label={t("expire_date")}>
          {post.expireDate ? dayjs(post.expireDate).format("DD/MM/YYYY") : ""}
        </Descriptions.Item>
        <Descriptions.Item label={t("positions")}>
          {post.positions}
        </Descriptions.Item>
        <Descriptions.Item label={t("job_type")}>
          {post.jobType}
        </Descriptions.Item>
        <Descriptions.Item label={t("salary")}>
          {post.salary && typeof post.salary === "object"
            ? `${post.salary.min} - ${post.salary.max} ${
                post.salary.currency
              } ${getSalaryPeriodShortLabel(post.salary.period)}`
            : post.salary}
        </Descriptions.Item>
        {post?.workingInformation &&
          Array.isArray(post?.workingInformation) &&
          post?.workingInformation?.length > 0 && (
            <Descriptions.Item
              label={t("working_information") || "Working Information"}
              span={3}
            >
              <Space direction="vertical">
                {post?.workingInformation?.map((info, idx) => (
                  <Text key={idx}>
                    {info.workDays}:{" "}
                    {typeof info.workHours === "string"
                      ? info.workHours
                      : JSON.stringify(info.workHours)}
                  </Text>
                ))}
              </Space>
            </Descriptions.Item>
          )}
        <Descriptions.Item label={t("experience_level")}>
          {post.experienceLevel}
        </Descriptions.Item>
        <Descriptions.Item label={t("features") || "Features"} span={3}>
          <Space wrap>
            {post.isFeatureJob && (
              <Tag color="gold">
                {t("featured")}{" "}
                {post.featuredDuration &&
                  `(${getFeatureDurationLabel(post.featuredDuration)})`}
              </Tag>
            )}
            {post.urgentHiring && <Tag color="volcano">{t("urgent")}</Tag>}
          </Space>
        </Descriptions.Item>
        <Descriptions.Item label={t("skills.title") || "Skills"} span={3}>
          <Space wrap>
            {post?.skills?.map((skill, index) => (
              <Tag key={index} color="blue">
                {skill}
              </Tag>
            ))}
          </Space>
        </Descriptions.Item>
        <Descriptions.Item label={t("benefits.title") || "Benefits"} span={3}>
          <Space direction="vertical">
            {post?.benefits?.map((benefit, index) => (
              <Text key={index}>• {benefit}</Text>
            ))}
          </Space>
        </Descriptions.Item>
        <Descriptions.Item
          label={t("required_documents") || "Required Documents"}
          span={3}
        >
          <Space direction="vertical">
            {post?.requiredDocuments?.map((doc, index) => (
              <Text key={index}>
                • {doc.name}{" "}
                {doc.required ? (
                  <Tag color="red">
                    {tCommon("requirements.required") || "Required"}
                  </Tag>
                ) : (
                  <Tag color="blue">
                    {tCommon("requirements.optional") || "Optional"}
                  </Tag>
                )}
              </Text>
            ))}
          </Space>
        </Descriptions.Item>
        <Descriptions.Item label={tCommon("fields.description")} span={3}>
          <Paragraph ellipsis={{ rows: 4, expandable: true, symbol: "more" }}>
            {post.description}
          </Paragraph>
        </Descriptions.Item>
      </Descriptions>
    </Card>
  );
};

export default PostDetailsTab;
