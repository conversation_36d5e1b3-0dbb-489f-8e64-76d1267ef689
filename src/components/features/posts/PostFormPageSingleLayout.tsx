"use client";
import React, { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import {
  Form,
  Input,
  Button,
  Select,
  Switch,
  message,
  Spin,
  Space,
  Card,
  Typography,
  DatePicker,
  Row,
  Col,
  Flex,
  TimePicker,
} from "antd";
import {
  SaveOutlined,
  UserOutlined,
  RiseOutlined,
  TrophyOutlined,
  GiftOutlined,
  FileTextOutlined,
  SettingOutlined,
  MinusCircleOutlined,
  PlusOutlined,
  CheckCircleOutlined,
  MinusOutlined,
} from "@ant-design/icons";
import { useTranslations } from "next-intl";
import dayjs from "dayjs";

// Import existing components and services
import BaseInput from "@/components/ui/inputs/BaseInput";
import BaseTextArea from "@/components/ui/inputs/BaseTextArea";
import BaseSelect from "@/components/ui/selects/BaseSelect";
import BaseInputNumber from "@/components/ui/inputs/BaseInputNumber";

import DebounceSelect from "@/components/ui/selects/DebounceSelect";
import Address<PERSON>orm<PERSON>ield from "@/components/features/address/AddressFormField";
import CompanyService from "@/services/companyService";
import BranchService from "@/services/branchService";
import PostService from "@/services/postService";
import { Post, PostStatus } from "@/types/post";
import { AddressFormData } from "@/types/address";
import { DaySchedule } from "@/components/features/posts/forms/JobDetailsTab";
import {
  JOB_TYPE_KEYS,
  WORK_TYPE_KEYS,
  EXPERIENCE_LEVEL_KEYS,
} from "@/constants/post";
import { useOptions } from "@/hooks/useTranslateOptions";
import {
  ENUM_POST_SALARY_CURRENCY,
  ENUM_WORK_DAY_VALUE,
} from "@/constants/enum";
import { getFeatureDurationSelectOptions } from "@/enums/featureDuration";
import { getSalaryPeriodSelectOptions } from "@/enums/salaryPeriod";
import styles from "./PostFormPageSingleLayout.module.css";

const { Title, Text } = Typography;

interface PostFormPageSingleLayoutProps {
  isEdit?: boolean;
}

type PostFormSchemaType = Omit<Post, "salary"> & {
  salaryCurrency: string;
  salaryPeriod: string;
  salaryMin: number;
  salaryMax: number;
  workSchedule: DaySchedule[];
  address?: AddressFormData;
  companyId?: number;
  branchId?: number;
  workType?: string;
};

const PostFormPageSingleLayout: React.FC<PostFormPageSingleLayoutProps> = ({
  isEdit = false,
}) => {
  const router = useRouter();
  const params = useParams();
  const postId = isEdit ? (params.id as string) : undefined;

  const [form] = Form.useForm<PostFormSchemaType>();
  const tCommon = useTranslations("common");
  const tPost = useTranslations("posts");
  const tMessages = useTranslations("messages");

  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [initialValues, setInitialValues] = useState<Partial<Post> | null>(null);
  const [initialCompanyId, setInitialCompanyId] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [addressDisabled, setAddressDisabled] = useState(true);

  // Form watchers
  const branchId = Form.useWatch("branchId", form);
  const companyId = Form.useWatch("companyId", form);
  const isFeatured = Form.useWatch("isFeatured", form);

  // Options
  const jobTypeOptions = useOptions("job.types", Object.values(JOB_TYPE_KEYS));
  const workTypeOptions = useOptions("job.work_types", Object.values(WORK_TYPE_KEYS));
  const experienceLevelOptions = useOptions(
    "job.experience_levels",
    Object.values(EXPERIENCE_LEVEL_KEYS)
  );
  const salaryPeriodOptions = getSalaryPeriodSelectOptions();
  const salaryCurrencyOptions = useOptions(
    "job.salary_currencies",
    Object.values(ENUM_POST_SALARY_CURRENCY)
  );
  const featuredDurationOptions = getFeatureDurationSelectOptions();

  // Company and branch options
  const [companyOptions, setCompanyOptions] = useState<any[]>([]);
  const [branchOptions, setBranchOptions] = useState<any[]>([]);

  // Skills and benefits state
  const [skillInput, setSkillInput] = useState("");
  const [benefitInput, setBenefitInput] = useState("");

  // Fetch company options
  const fetchCompanyOptions = async (search: string) => {
    try {
      const response = await CompanyService.getList({
        keyword: search,
        page: 0,
        limit: 20000,
      });
      const options = response.data.map((company: any) => ({
        label: company.name,
        value: company.id,
      }));
      setCompanyOptions(options);
      return options;
    } catch (error) {
      console.error("Error fetching companies:", error);
      return [];
    }
  };

  // Fetch branch options
  const fetchBranchOptions = async (search: string) => {
    try {
      const currentCompanyId = form.getFieldValue("companyId")?.value || form.getFieldValue("companyId");
      if (!currentCompanyId) return [];

      const response = await BranchService.getList({
        keyword: search,
        page: 0,
        limit: 20000,
        companyId: currentCompanyId,
      });
      const options = response.data.map((branch: any) => ({
        label: branch.name,
        value: branch.id,
      }));
      setBranchOptions(options);
      return options;
    } catch (error) {
      console.error("Error fetching branches:", error);
      return [];
    }
  };

  // Clear featuredDuration when isFeatured is disabled
  useEffect(() => {
    if (!isFeatured) {
      form.setFieldValue("featuredDuration", undefined);
    }
  }, [isFeatured, form]);

  // Auto-fill address when branch is selected
  useEffect(() => {
    const companyIdValue = typeof companyId === "object" ? (companyId as any)?.value : companyId;
    const branchIdValue = typeof branchId === "object" ? (branchId as any)?.value : branchId;

    if (branchIdValue && companyIdValue) {
      setAddressDisabled(false);
      BranchService.getDetail(String(companyIdValue), String(branchIdValue)).then((res) => {
        const addr = res.data.address;
        if (addr && typeof addr === "object" && (addr as any).province && (addr as any).district && (addr as any).ward) {
          form.setFieldValue("address", {
            provinceCode: (addr as any).province.code,
            districtCode: (addr as any).district.code,
            wardCode: (addr as any).ward.code,
            detailAddress: (addr as any).detailAddress || "",
          });
        }
      });
    } else {
      setAddressDisabled(true);
    }
  }, [branchId, companyId, form]);

  // Reset branchId when company changes
  useEffect(() => {
    if (companyId && initialCompanyId !== null && !loading) {
      const currentCompanyId = typeof companyId === "object" ? (companyId as any)?.value : companyId;
      if (currentCompanyId !== initialCompanyId) {
        form.setFieldValue("branchId", "");
      }
    }
  }, [companyId, initialCompanyId, loading, form]);

  // Skills handlers
  const handleAddSkill = () => {
    if (!skillInput.trim()) return;
    const skills = form.getFieldValue("skills") || [];
    const newSkills = [...skills, skillInput.trim()];
    form.setFieldValue("skills", newSkills);
    setSkillInput("");
  };

  const handleRemoveSkill = (indexToRemove: number) => {
    const skills = form.getFieldValue("skills") || [];
    const newSkills = skills.filter((_: string, index: number) => index !== indexToRemove);
    form.setFieldValue("skills", newSkills);
  };

  // Benefits handlers
  const handleAddBenefit = () => {
    if (!benefitInput.trim()) return;
    const benefits = form.getFieldValue("benefits") || [];
    const newBenefits = [...benefits, benefitInput.trim()];
    form.setFieldValue("benefits", newBenefits);
    setBenefitInput("");
  };

  const handleRemoveBenefit = (indexToRemove: number) => {
    const benefits = form.getFieldValue("benefits") || [];
    const newBenefits = benefits.filter((_: string, index: number) => index !== indexToRemove);
    form.setFieldValue("benefits", newBenefits);
  };

  // Work Schedule Component
  const WorkScheduleComponent = () => {
    const [schedule, setSchedule] = useState<any[]>(() => {
      const initialSchedule = form.getFieldValue("workSchedule") || [];
      if (initialSchedule.length > 0) {
        return initialSchedule;
      }
      return Object.keys(ENUM_WORK_DAY_VALUE).map((dayKey) => ({
        day: dayKey,
        isActive: false,
        periods: [{ startTime: null, endTime: null }],
      }));
    });

    const toggleDayActive = (dayIndex: number) => {
      const newSchedule = [...schedule];
      newSchedule[dayIndex].isActive = !newSchedule[dayIndex].isActive;
      setSchedule(newSchedule);
      form.setFieldValue("workSchedule", newSchedule);
    };

    const updatePeriod = (dayIndex: number, periodIndex: number, field: 'startTime' | 'endTime', value: any) => {
      const newSchedule = [...schedule];
      newSchedule[dayIndex].periods[periodIndex][field] = value;
      setSchedule(newSchedule);
      form.setFieldValue("workSchedule", newSchedule);
    };

    const addPeriod = (dayIndex: number) => {
      const newSchedule = [...schedule];
      newSchedule[dayIndex].periods.push({ startTime: null, endTime: null });
      setSchedule(newSchedule);
      form.setFieldValue("workSchedule", newSchedule);
    };

    const removePeriod = (dayIndex: number, periodIndex: number) => {
      const newSchedule = [...schedule];
      if (newSchedule[dayIndex].periods.length > 1) {
        newSchedule[dayIndex].periods.splice(periodIndex, 1);
        setSchedule(newSchedule);
        form.setFieldValue("workSchedule", newSchedule);
      }
    };

    return (
      <Card className="mb-6">
        <div className="mb-4">
          <Space size={8} className="mb-4">
            {Object.entries(ENUM_WORK_DAY_VALUE).map(([dayKey], index) => (
              <Button
                key={dayKey}
                type={schedule[index].isActive ? "primary" : "default"}
                shape="circle"
                onClick={() => toggleDayActive(index)}
                className={`flex items-center justify-center ${
                  schedule[index].isActive ? "bg-blue-500" : "bg-gray-100"
                }`}
              >
                {tPost(`work_schedule.day_abbr.${dayKey.toLowerCase()}`)}
              </Button>
            ))}
          </Space>
        </div>

        {schedule.map((day, dayIndex) => {
          if (!day.isActive) return null;
          const dayKey = Object.keys(ENUM_WORK_DAY_VALUE)[dayIndex];

          return (
            <div key={dayIndex} className="mb-4 p-4 border rounded">
              <Text strong className="block mb-2">
                {tPost(`work_schedule.days.${dayKey.toLowerCase()}`)}
              </Text>

              {day.periods.map((period: any, periodIndex: number) => (
                <div key={periodIndex} className="flex items-center gap-2 mb-2">
                  <TimePicker
                    value={period.startTime}
                    onChange={(time) => updatePeriod(dayIndex, periodIndex, 'startTime', time)}
                    format="HH:mm"
                    placeholder="Start time"
                  />
                  <span>-</span>
                  <TimePicker
                    value={period.endTime}
                    onChange={(time) => updatePeriod(dayIndex, periodIndex, 'endTime', time)}
                    format="HH:mm"
                    placeholder="End time"
                  />

                  <Button
                    type="text"
                    icon={<PlusOutlined />}
                    onClick={() => addPeriod(dayIndex)}
                    size="small"
                  />

                  {day.periods.length > 1 && (
                    <Button
                      type="text"
                      danger
                      icon={<MinusOutlined />}
                      onClick={() => removePeriod(dayIndex, periodIndex)}
                      size="small"
                    />
                  )}
                </div>
              ))}
            </div>
          );
        })}
      </Card>
    );
  };

  // Fetch post data if in edit mode
  useEffect(() => {
    if (isEdit && postId) {
      const fetchPostData = async () => {
        try {
          setLoading(true);
          const postData = (await PostService.getDetail(postId)).data;

          // Format dates for form input
          const formattedPost = {
            ...postData,
            companyId: (postData as any).companyId,
            branchId: (postData as any).branchId,
            salaryCurrency: postData.salary?.currency || "VND",
            salaryPeriod: postData.salary?.period || "MONTHLY",
            salaryMin: postData.salary?.min || 0,
            salaryMax: postData.salary?.max || 0,
            jobType: postData.jobType || "",
            workType: postData.workType || "",
            experienceLevel: postData.experienceLevel || "",
            positions: postData.positions || "",
            workingInformation: postData.workingInformation || [],
            skills: postData.skills || [],
            benefits: postData.benefits || [],
            requiredDocuments: postData.requiredDocuments || [],
            postDate: postData.postDate ? dayjs(postData.postDate).toISOString() : "",
            expireDate: postData.expireDate ? dayjs(postData.expireDate).toISOString() : "",
          };

          setInitialValues(formattedPost);
          setInitialCompanyId((postData as any).companyId || null);
          form.setFieldsValue(formattedPost);
        } catch (err) {
          console.error("Error fetching post data:", err);
          setError(tMessages("errors.failed_to_load_post_data"));
        } finally {
          setLoading(false);
        }
      };

      fetchPostData();
    } else {
      // Set default values for new post
      const defaultValues = {
        status: tPost("status.draft") as PostStatus,
        isFeatured: false,
        featuredDuration: undefined,
        urgentHiring: false,
        positions: "1",
        skills: [],
        benefits: [],
        salaryCurrency: "VND",
        salaryPeriod: "MONTHLY",
        requiredDocuments: [
          {
            name: tPost("documents.resume_cv"),
            required: true,
          },
        ],
        postDate: dayjs().toISOString(),
        expireDate: dayjs().add(30, 'days').toISOString(),
      };

      setInitialValues(defaultValues);
      form.setFieldsValue(defaultValues);
    }
  }, [isEdit, postId, form, tMessages, tPost]);

  // Validate required fields for publishing
  const validateRequiredFields = () => {
    const values = form.getFieldsValue();
    const missing: string[] = [];

    // Basic Info validation
    if (!values.title?.trim()) missing.push("Job Title");
    if (!values.companyId) missing.push("Company");
    if (!values.branchId) missing.push("Branch");
    if (!values.description?.trim()) missing.push("Job Description");

    // Job Details validation
    if (!values.jobType) missing.push("Job Type");
    if (!values.workType) missing.push("Work Type");
    if (!values.experienceLevel) missing.push("Experience Level");
    if (!values.positions) missing.push("Positions");
    if (!values.salaryMin) missing.push("Minimum Salary");
    if (!values.salaryMax) missing.push("Maximum Salary");

    // Work Schedule validation
    if (!values.workSchedule || !values.workSchedule.some((day: any) => day.isActive)) {
      missing.push("Work Schedule");
    }

    // Post Settings validation
    if (!values.postDate) missing.push("Post Date");
    if (!values.expireDate) missing.push("Expiry Date");

    return missing;
  };

  // Save handler
  const handleSave = async (published: boolean) => {
    try {
      // If publishing, check required fields first
      if (published) {
        const missing = validateRequiredFields();
        if (missing.length > 0) {
          message.error(`Please fill in the following required fields: ${missing.join(", ")}`);
          return;
        }
      }

      await form.validateFields();
      const { salaryMin, salaryMax, salaryCurrency, salaryPeriod, ...values } = form.getFieldsValue();

      // Prepare data for submission
      const safeSkills = Array.isArray(values.skills) ? values.skills : [];
      const safeBenefits = Array.isArray(values.benefits) ? values.benefits : [];

      const postData: Post = {
        ...values,
        skills: safeSkills,
        benefits: safeBenefits,
        salary: {
          min: salaryMin || 0,
          max: salaryMax || 0,
          currency: salaryCurrency || "VND",
          period: salaryPeriod || "MONTHLY",
        } as any,
        status: published ? tPost("status.published") : tPost("status.draft"),
        postDate: dayjs(values.postDate).toISOString(),
        expireDate: dayjs(values.expireDate).toISOString(),
        workingInformation: values.workSchedule
          ? values.workSchedule
              .filter((day: any) => day.isActive)
              .map((day: any) => ({
                workDays: day.day,
                workHours: day.periods
                  .filter((period: any) => period.startTime && period.endTime)
                  .map((period: any) => `${period.startTime?.format('HH:mm')}-${period.endTime?.format('HH:mm')}`)
                  .join(', '),
              }))
          : [],
      };

      setSaveLoading(true);

      if (isEdit && postId) {
        await PostService.update(postId, postData);
        message.success(tMessages("success.post_updated"));
      } else {
        await PostService.create(postData);
        message.success(tMessages("success.post_created"));
      }

      // Redirect back to posts list
      const currentPath = window.location.pathname;
      if (currentPath.includes("job-seekers-management")) {
        router.push("/job-seekers-management/posts");
      } else if (currentPath.includes("employers-management")) {
        router.push("/employers-management/posts");
      } else {
        router.push("/features/posts");
      }
    } catch (err) {
      console.error("Error saving post:", err);
      message.error(tMessages("errors.failed_to_save_post"));
    } finally {
      setSaveLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.wrapper}>
        <Title level={3} className="mb-6 text-center">
          {isEdit ? tPost("form.edit_post") : tPost("form.create_new_post")}
        </Title>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Spin size="large" />
        </div>
      ) : error ? (
        <Card>
          <Text type="danger">{error}</Text>
        </Card>
      ) : (
        <Form<PostFormSchemaType>
          form={form}
          layout="vertical"
          initialValues={initialValues || {}}
          autoComplete="off"
          onFinish={() => handleSave(true)}
        >
          <Row gutter={[24, 24]} className={styles.formRow}>
            {/* Left Column */}
            <Col xs={24} lg={24} xl={12} xxl={12} className={styles.column}>
              {/* Basic Information Section */}
              <Card
                title={
                  <Title level={4} style={{ margin: 0 }}>
                    <UserOutlined /> {tPost("basic_information")}
                  </Title>
                }
                className={styles.card}
              >
            <BaseInput
              name="title"
              label={tPost("job_title")}
              required
              placeholder={tPost("placeholders.job_title")}
              rules={[
                {
                  required: true,
                  message: tPost("validation.please_enter_job_title"),
                },
              ]}
            />

            <Flex gap="middle">
              <DebounceSelect
                label={tCommon("fields.company")}
                name="companyId"
                fetchOptions={fetchCompanyOptions}
                required
                options={companyOptions}
                className="w-1/2"
              />

              <DebounceSelect
                label={tCommon("fields.branch")}
                name="branchId"
                fetchOptions={fetchBranchOptions}
                required
                options={branchOptions}
                className="w-1/2"
              />
            </Flex>

            <AddressFormField
              name="address"
              required
              className="mt-4"
              disabled={addressDisabled}
            />

            <BaseTextArea
              name="description"
              label={tPost("job_description")}
              required
              placeholder={tPost("placeholders.job_description")}
              rows={4}
              rules={[
                {
                  required: true,
                  message: tPost("validation.please_enter_job_description"),
                },
              ]}
            />
              </Card>

              {/* Job Details Section */}
              <Card
                title={
                  <Title level={4} style={{ margin: 0 }}>
                    <RiseOutlined /> {tPost("job_details")}
                  </Title>
                }
                className={styles.card}
              >
            <Flex gap="middle">
              <BaseSelect
                name="jobType"
                label={tPost("job_type")}
                required
                options={jobTypeOptions}
                placeholder={tPost("placeholders.select_job_type")}
                className="w-1/2"
                rules={[{ required: true, message: "Please select job type" }]}
              />

              <BaseSelect
                name="workType"
                label={tPost("work_type")}
                required
                options={workTypeOptions}
                placeholder={tPost("placeholders.select_work_type")}
                className="w-1/2"
                rules={[{ required: true, message: "Please select work type" }]}
              />
            </Flex>

            <Flex gap="middle">
              <BaseSelect
                name="experienceLevel"
                label={tPost("experience_level")}
                required
                options={experienceLevelOptions}
                placeholder={tPost("placeholders.select_experience_level")}
                className="w-1/2"
                rules={[{ required: true, message: "Please select experience level" }]}
              />

              <BaseInputNumber
                name="positions"
                label={tPost("positions")}
                required
                min={1}
                placeholder="1"
                className="w-1/2"
                rules={[{ required: true, message: "Please enter number of positions" }]}
              />
            </Flex>

            {/* Work Schedule */}
            <div className="mb-4">
              <Text strong>{tPost("work_schedule.other_periods")}</Text>
              <WorkScheduleComponent />
            </div>

            {/* Salary Section */}
            <div className="mb-4">
              <Text strong className="block mb-2">{tPost("salary_information")}</Text>
              <Row gutter={16}>
                <Col span={12}>
                  <BaseInputNumber
                    name="salaryMin"
                    label={tPost("salary_min")}
                    required
                    min={0}
                    placeholder="Enter minimum salary"
                    rules={[{ required: true, message: "Please enter minimum salary" }]}
                  />
                </Col>
                <Col span={12}>
                  <BaseInputNumber
                    name="salaryMax"
                    label={tPost("salary_max")}
                    required
                    min={0}
                    placeholder="Enter maximum salary"
                    rules={[{ required: true, message: "Please enter maximum salary" }]}
                  />
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <BaseSelect
                    name="salaryCurrency"
                    label={tPost("salary_currency")}
                    required
                    options={salaryCurrencyOptions}
                    placeholder="Select currency"
                    rules={[{ required: true, message: "Please select currency" }]}
                  />
                </Col>
                <Col span={12}>
                  <BaseSelect
                    name="salaryPeriod"
                    label={tPost("salary_period")}
                    required
                    options={salaryPeriodOptions}
                    placeholder="Select period"
                    rules={[{ required: true, message: "Please select salary period" }]}
                  />
                </Col>
              </Row>
            </div>
              </Card>

              {/* Skills Section */}
              <Card
                title={
                  <Title level={4} style={{ margin: 0 }}>
                    <TrophyOutlined /> {tPost("skills.title")}
                  </Title>
                }
                className={styles.card}
              >
            <div className="space-y-4">
              <BaseInput
                label={tPost("skills.label")}
                placeholder={tPost("skills.placeholder")}
                value={skillInput}
                onChange={(e) => setSkillInput(e.target.value)}
                onPressEnter={(e) => {
                  e.preventDefault();
                  handleAddSkill();
                }}
                suffix={
                  <Button
                    type="text"
                    size="small"
                    onClick={handleAddSkill}
                    disabled={!skillInput.trim()}
                    title={tPost("skills.add_skill")}
                  >
                    <PlusOutlined />
                  </Button>
                }
              />

              <Form.Item
                shouldUpdate={(prevValues, currentValues) =>
                  prevValues.skills !== currentValues.skills
                }
              >
                {({ getFieldValue }) => {
                  const skills = getFieldValue("skills") || [];

                  return (
                    <div className="space-y-2">
                      {skills.map((skill: string, index: number) => (
                        <Flex
                          key={`${skill}-${index}`}
                          justify="space-between"
                          align="center"
                        >
                          <Text>• {skill}</Text>
                          <Button
                            type="text"
                            danger
                            size="small"
                            icon={<MinusCircleOutlined />}
                            onClick={() => handleRemoveSkill(index)}
                            title={tPost("skills.remove_skill")}
                          />
                        </Flex>
                      ))}

                      {skills.length === 0 && (
                        <Text type="secondary" className="text-sm">
                          {tPost("skills.no_skills_added")}
                        </Text>
                      )}
                    </div>
                  );
                }}
              </Form.Item>

              <Form.Item name="skills" hidden>
                <input type="hidden" />
              </Form.Item>
            </div>
              </Card>
            </Col>

            {/* Right Column */}
            <Col xs={24} lg={24} xl={12} xxl={12} className={styles.column}>
              {/* Benefits Section */}
              <Card
                title={
                  <Title level={4} style={{ margin: 0 }}>
                    <GiftOutlined /> {tPost("benefits.title")}
                  </Title>
                }
                className={styles.card}
              >
            <div className="space-y-4">
              <BaseInput
                label={tPost("benefits.label")}
                placeholder={tPost("benefits.placeholder")}
                value={benefitInput}
                onChange={(e) => setBenefitInput(e.target.value)}
                onPressEnter={(e) => {
                  e.preventDefault();
                  handleAddBenefit();
                }}
                suffix={
                  <Button
                    type="text"
                    size="small"
                    onClick={handleAddBenefit}
                    disabled={!benefitInput.trim()}
                    title={tPost("benefits.add_benefit")}
                  >
                    <PlusOutlined />
                  </Button>
                }
              />

              <Form.Item
                shouldUpdate={(prevValues, currentValues) =>
                  prevValues.benefits !== currentValues.benefits
                }
              >
                {({ getFieldValue }) => {
                  const benefits = getFieldValue("benefits") || [];

                  return (
                    <div className="space-y-2">
                      {benefits.map((benefit: string, index: number) => (
                        <Flex
                          key={`${benefit}-${index}`}
                          justify="space-between"
                          align="center"
                        >
                          <Text>• {benefit}</Text>
                          <Button
                            type="text"
                            danger
                            size="small"
                            icon={<MinusCircleOutlined />}
                            onClick={() => handleRemoveBenefit(index)}
                            title={tPost("benefits.remove_benefit")}
                          />
                        </Flex>
                      ))}

                      {benefits.length === 0 && (
                        <Text type="secondary" className="text-sm">
                          {tPost("benefits.no_benefits_added")}
                        </Text>
                      )}
                    </div>
                  );
                }}
              </Form.Item>

              <Form.Item name="benefits" hidden>
                <input type="hidden" />
              </Form.Item>
            </div>
              </Card>

              {/* Documents Section */}
              <Card
                title={
                  <Title level={4} style={{ margin: 0 }}>
                    <FileTextOutlined /> {tPost("tabs.required_documents")}
                  </Title>
                }
                className={styles.card}
              >
            <Form.List name="requiredDocuments">
              {(fields, { add, remove }) => (
                <>
                  {fields.map(({ key, name, ...restField }) => (
                    <Flex
                      key={key}
                      align="center"
                      gap="middle"
                      style={{ marginBottom: 8 }}
                    >
                      <Form.Item
                        {...restField}
                        name={[name, "name"]}
                        rules={[{ required: true, message: "Missing document name" }]}
                        style={{ margin: 0, flex: 1 }}
                      >
                        <Input placeholder="Document name" />
                      </Form.Item>

                      <Form.Item
                        {...restField}
                        name={[name, "required"]}
                        valuePropName="checked"
                        style={{ margin: 0, width: 120 }}
                      >
                        <Switch
                          checkedChildren="Required"
                          unCheckedChildren="Optional"
                        />
                      </Form.Item>

                      <Button
                        type="text"
                        danger
                        icon={<MinusCircleOutlined />}
                        onClick={() => remove(name)}
                      />
                    </Flex>
                  ))}

                  <Form.Item>
                    <Button
                      type="dashed"
                      onClick={() => add({ name: "", required: false })}
                      block
                      icon={<PlusOutlined />}
                    >
                      Add Document
                    </Button>
                  </Form.Item>
                </>
              )}
            </Form.List>
              </Card>

              {/* Post Settings Section */}
              <Card
                title={
                  <Title level={4} style={{ margin: 0 }}>
                    <SettingOutlined /> {tPost("tabs.post_settings")}
                  </Title>
                }
                className={styles.card}
              >
            <Flex gap="middle">
              <Form.Item
                name="postDate"
                label="Post Date"
                rules={[{ required: true, message: "Please select post date" }]}
                className="w-1/2"
                getValueProps={(value) => ({
                  value: value ? dayjs(value) : undefined,
                })}
              >
                <DatePicker style={{ width: "100%" }} />
              </Form.Item>

              <Form.Item
                name="expireDate"
                label="Expiry Date"
                rules={[{ required: true, message: "Please select expiry date" }]}
                className="w-1/2"
                getValueProps={(value) => ({
                  value: value ? dayjs(value) : undefined,
                })}
              >
                <DatePicker style={{ width: "100%" }} />
              </Form.Item>
            </Flex>

            <Flex gap="middle">
              <Form.Item
                name="isFeatured"
                label="Featured Post"
                valuePropName="checked"
                className="w-1/2"
              >
                <Switch checkedChildren="Yes" unCheckedChildren="No" />
              </Form.Item>

              <Form.Item
                name="urgentHiring"
                label="Urgent Hiring"
                valuePropName="checked"
                className="w-1/2"
              >
                <Switch checkedChildren="Yes" unCheckedChildren="No" />
              </Form.Item>
            </Flex>

            {isFeatured && (
              <Flex gap="middle">
                <Form.Item
                  name="featuredDuration"
                  label={tPost("featured_duration")}
                  rules={[
                    {
                      required: isFeatured,
                      message: "Please select featured duration when featured post is enabled",
                    },
                  ]}
                  className="w-1/2"
                >
                  <Select
                    placeholder={
                      isFeatured
                        ? tPost("featured_duration_placeholder")
                        : "Enable featured post first"
                    }
                    options={featuredDurationOptions}
                    style={{ width: "100%" }}
                    disabled={!isFeatured}
                  />
                </Form.Item>

                <div className="w-1/2" />
              </Flex>
            )}
              </Card>
            </Col>
          </Row>

          {/* Action Buttons - Full Width */}
          <Card style={{ marginTop: 24 }} className={styles.card}>
            <Row justify="end">
              <Space size="large" className={styles.actionButtons}>
                <Button
                  size="large"
                  onClick={() => {
                    const currentPath = window.location.pathname;
                    if (currentPath.includes("job-seekers-management")) {
                      router.push("/job-seekers-management/posts");
                    } else if (currentPath.includes("employers-management")) {
                      router.push("/employers-management/posts");
                    } else {
                      router.push("/features/posts");
                    }
                  }}
                >
                  {tCommon("actions.cancel")}
                </Button>

                <Button
                  type="default"
                  size="large"
                  icon={<SaveOutlined />}
                  onClick={() => handleSave(false)}
                  loading={saveLoading}
                >
                  {tPost("actions.save_as_draft")}
                </Button>

                <Button
                  type="primary"
                  size="large"
                  icon={<CheckCircleOutlined />}
                  onClick={() => handleSave(true)}
                  loading={saveLoading}
                >
                  {isEdit ? tCommon("actions.update") : tCommon("actions.publish")}
                </Button>
              </Space>
            </Row>
          </Card>
        </Form>
      )}
      </div>
    </div>
  );
};

export default PostFormPageSingleLayout;
