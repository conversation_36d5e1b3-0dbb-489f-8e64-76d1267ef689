/* PostFormPageSingleLayout Responsive Styles */

.container {
  width: 100%;
  padding: 24px;
}

.wrapper {
  max-width: 1600px;
  margin: 0 auto;
  width: 100%;
}

.formRow {
  width: 100%;
}

.column {
  display: flex;
  flex-direction: column;
}

.card {
  margin-bottom: 24px;
  height: fit-content;
}

/* Responsive breakpoints */
@media (max-width: 1199px) {
  .wrapper {
    max-width: 100%;
  }
}

@media (max-width: 991px) {
  .container {
    padding: 16px;
  }
  
  .card {
    margin-bottom: 16px;
  }
}

@media (max-width: 767px) {
  .container {
    padding: 12px;
  }
  
  .card {
    margin-bottom: 12px;
  }
}

/* Work schedule responsive */
.workScheduleButtons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

@media (max-width: 575px) {
  .workScheduleButtons {
    justify-content: center;
  }
}

/* Action buttons responsive */
.actionButtons {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  flex-wrap: wrap;
}

@media (max-width: 575px) {
  .actionButtons {
    justify-content: center;
    gap: 12px;
  }
  
  .actionButtons button {
    min-width: 120px;
  }
}

/* Form fields responsive */
.formField {
  margin-bottom: 16px;
}

.formRow .ant-col {
  margin-bottom: 16px;
}

@media (max-width: 767px) {
  .formField {
    margin-bottom: 12px;
  }
  
  .formRow .ant-col {
    margin-bottom: 12px;
  }
}

/* Card title responsive */
.cardTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
}

@media (max-width: 575px) {
  .cardTitle {
    font-size: 16px;
  }
}

/* Skills and benefits list responsive */
.skillsList,
.benefitsList {
  max-height: 300px;
  overflow-y: auto;
}

.skillItem,
.benefitItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.skillItem:last-child,
.benefitItem:last-child {
  border-bottom: none;
}

/* Documents section responsive */
.documentItem {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

@media (max-width: 575px) {
  .documentItem {
    gap: 8px;
  }
}

/* Work schedule periods responsive */
.workPeriod {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

@media (max-width: 575px) {
  .workPeriod {
    justify-content: center;
  }
}

/* Salary fields responsive */
.salaryRow {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.salaryField {
  flex: 1;
  min-width: 200px;
}

@media (max-width: 575px) {
  .salaryField {
    min-width: 100%;
  }
}

/* Loading and error states */
.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.errorContainer {
  padding: 24px;
  text-align: center;
}

/* Smooth transitions */
.card,
.formField,
.skillItem,
.benefitItem {
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Print styles */
@media print {
  .container {
    padding: 0;
  }
  
  .actionButtons {
    display: none;
  }
  
  .card {
    break-inside: avoid;
    margin-bottom: 16px;
  }
}
