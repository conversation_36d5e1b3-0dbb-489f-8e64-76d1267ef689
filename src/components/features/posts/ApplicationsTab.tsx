"use client";
import type { JobApplication } from "@/types/application";
import { EyeOutlined } from "@ant-design/icons";
import type { TableColumnsType } from "antd";
import { <PERSON>ton, Card, Flex, Image, Space, Table, Tag } from "antd";
import { useRouter } from "next/navigation";
import React, { useRef, useState } from "react";
import CreateApplicationModal, {
  ApplicationModalMode,
} from "./CreateApplicationModal";
import BaseTable, { BaseTableRef } from "@/components/ui/tables/BaseTable";
import PostService from "@/services/postService";
interface ApplicationsTabProps {
  postId: number;
}

const ApplicationsTab: React.FC<ApplicationsTabProps> = ({ postId }) => {
  const router = useRouter();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedApplication, setSelectedApplication] =
    useState<JobApplication | null>(null);
  const [applicationModalMode, setApplicationModalMode] =
    useState<ApplicationModalMode>(ApplicationModalMode.CREATE);

  const handleCreateSuccess = () => {
    setShowCreateModal(false);
    tableRef.current?.refetch();
  };

  const tableRef = useRef<BaseTableRef>(null);
  const fetchApplications = async () => {
    const res = await PostService.getApplications(postId.toString());
    return { data: (res as any).data };
  };

  const applicationColumns: TableColumnsType<JobApplication> = [
    {
      title: "Candidate",
      dataIndex: "candidateName",
      key: "candidateName",
      render: (_, record) => (
        <Flex align="center" gap="small">
          <Image
            src={record.userInfo.profilePicture || ""}
            alt={record.userInfo.name}
            width={32}
            height={32}
            style={{ borderRadius: "50%" }}
            preview={false}
          />
          {record.userInfo.name}
        </Flex>
      ),
    },
    {
      title: "Match",
      dataIndex: "matchScore",
      key: "matchScore",
      render: (score) => (
        <Tag color={score > 80 ? "green" : score > 60 ? "orange" : "red"}>
          {score}%
        </Tag>
      ),
    },
    {
      title: "Application Date",
      dataIndex: "createdAt",
      key: "applicationDate",
      render: (value) =>
        value ? new Date(value).toLocaleDateString("vi-VN") : "",
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      render: (status) => {
        let color = "blue";
        if (status === "Đã xem") color = "cyan";
        if (status === "Lịch phỏng vấn") color = "orange";
        if (status === "Đã phỏng vấn") color = "purple";
        if (status === "Đã mời làm việc") color = "green";
        if (status === "Đã từ chối") color = "red";

        return <Tag color={color}>{status}</Tag>;
      },
    },
    // {
    //   title: "Actions",
    //   key: "actions",
    //   render: (_, record) => (
    //     <Space>
    //       <Button
    //         type="text"
    //         icon={<EyeOutlined />}
    //         onClick={() => router.push(`/admin/applications/${record.id}`)}
    //       />
    //     </Space>
    //   ),
    // },
  ];

  const handleCreateApplication = () => {
    setApplicationModalMode(ApplicationModalMode.CREATE);
    setShowCreateModal(true);
  };

  const handleViewApplication = (record: JobApplication) => {
    setApplicationModalMode(ApplicationModalMode.VIEW);
    setShowCreateModal(true);
    setSelectedApplication(record);
  };

  const handleEditApplication = (record: JobApplication) => {
    setApplicationModalMode(ApplicationModalMode.EDIT);
    setShowCreateModal(true);
    setSelectedApplication(record);
  };

  const handleDeleteApplication = async (record: JobApplication) => {
    await PostService.deleteApplication(record.id);
  };

  return (
    <Card>
      <CreateApplicationModal
        open={showCreateModal}
        onCancel={() => setShowCreateModal(false)}
        onSuccess={handleCreateSuccess}
        postId={postId}
        mode={applicationModalMode}
        userId={selectedApplication?.userInfo.id}
        resumeId={selectedApplication?.resumeId}
      />
      <BaseTable
        ref={tableRef}
        api={fetchApplications}
        columns={applicationColumns}
        rowKey="id"
        title=""
        createBtnText={""}
        showSearch={true}
        searchPlaceholder={""}
        onCreate={handleCreateApplication}
        onEdit={handleEditApplication}
        onDelete={handleDeleteApplication}
        onRowClick={handleViewApplication}
        filters={[]}
        showActions={true}
      />
    </Card>
  );
};

export default ApplicationsTab;
