"use client";
import BaseInput from "@/components/ui/inputs/BaseInput";
import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import { Button, Card, Flex, Form, FormInstance, Typography } from "antd";
import { useTranslations } from "next-intl";
import React, { useState } from "react";

const { Text } = Typography;

interface BenefitsTabProps {
  form: FormInstance;
}

const BenefitsTab: React.FC<BenefitsTabProps> = ({ form }) => {
  const tPost = useTranslations("posts");
  const [benefitInput, setBenefitInput] = useState("");

  const handleAddBenefit = () => {
    if (!benefitInput.trim()) return;

    const benefits = form.getFieldValue("benefits") || [];
    const newBenefits = [...benefits, benefitInput.trim()];

    // Use setFieldValue instead of setFieldsValue for single field
    form.setFieldValue("benefits", newBenefits);
    setBenefitInput("");
  };

  const handleRemoveBenefit = (indexToRemove: number) => {
    const benefits = form.getFieldValue("benefits") || [];
    const newBenefits = benefits.filter(
      (_: string, index: number) => index !== indexToRemove
    );

    // Use setFieldValue to trigger re-render
    form.setFieldValue("benefits", newBenefits);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddBenefit();
    }
  };

  return (
    <Card title={tPost("benefits.title")} className="mb-6">
      <div className="space-y-4">
        {/* Input for adding new benefits */}
        <BaseInput
          label={tPost("benefits.label")}
          placeholder={tPost("benefits.placeholder")}
          value={benefitInput}
          onChange={(e) => setBenefitInput(e.target.value)}
          onPressEnter={handleKeyPress}
          suffix={
            <Button
              type="text"
              size="small"
              onClick={handleAddBenefit}
              disabled={!benefitInput.trim()}
              title={tPost("benefits.add_benefit")}
            >
              <PlusOutlined />
            </Button>
          }
        />

        {/* Watch the benefits field to trigger re-renders */}
        <Form.Item
          shouldUpdate={(prevValues, currentValues) =>
            prevValues.benefits !== currentValues.benefits
          }
        >
          {({ getFieldValue }) => {
            const benefits = getFieldValue("benefits") || [];

            return (
              <div className="space-y-2">
                {benefits.map((benefit: string, index: number) => (
                  <Flex
                    key={`${benefit}-${index}`}
                    justify="space-between"
                    align="center"
                  >
                    <Text>• {benefit}</Text>
                    <Button
                      type="text"
                      danger
                      size="small"
                      icon={<MinusCircleOutlined />}
                      onClick={() => handleRemoveBenefit(index)}
                      title={tPost("benefits.remove_benefit")}
                    />
                  </Flex>
                ))}

                {benefits.length === 0 && (
                  <Text type="secondary" className="text-sm">
                    {tPost("benefits.no_benefits_added")}
                  </Text>
                )}
              </div>
            );
          }}
        </Form.Item>

        {/* Hidden Form.Item to store the actual benefits data */}
        <Form.Item name="benefits" hidden>
          <input type="hidden" />
        </Form.Item>
      </div>
    </Card>
  );
};

export default BenefitsTab;
