"use client";
import { Button, Card, Flex, Form, Input, Switch } from "antd";
import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import React from "react";

const DocumentsTab: React.FC = () => {
  return (
    <Card title="Required Documents" className="mb-6">
      <Form.List name="requiredDocuments">
        {(fields, { add, remove }) => (
          <>
            {fields.map(({ key, name, ...restField }) => (
              <Flex
                key={key}
                align="center"
                gap="middle"
                style={{ marginBottom: 8 }}
              >
                <Form.Item
                  {...restField}
                  name={[name, "name"]}
                  rules={[{ required: true, message: "Missing document name" }]}
                  style={{ margin: 0, flex: 1 }}
                >
                  <Input placeholder="Document name" />
                </Form.Item>

                <Form.Item
                  {...restField}
                  name={[name, "required"]}
                  valuePropName="checked"
                  style={{ margin: 0, width: 120 }}
                >
                  <Switch
                    checkedChildren="Required"
                    unCheckedChildren="Optional"
                  />
                </Form.Item>

                <Button
                  type="text"
                  danger
                  icon={<MinusCircleOutlined />}
                  onClick={() => remove(name)}
                />
              </Flex>
            ))}

            <Form.Item>
              <Button
                type="dashed"
                onClick={() => add({ name: "", required: false })}
                block
                icon={<PlusOutlined />}
              >
                Add Document
              </Button>
            </Form.Item>
          </>
        )}
      </Form.List>
    </Card>
  );
};

export default DocumentsTab;
