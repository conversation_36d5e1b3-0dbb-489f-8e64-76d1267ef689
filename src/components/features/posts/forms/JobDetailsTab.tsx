"use client";
import {
  <PERSON><PERSON>,
  Card,
  Checkbox,
  Flex,
  Form,
  Row,
  Select,
  Space,
  TimePicker,
  Typography,
} from "antd";
import { MinusOutlined, PlusOutlined } from "@ant-design/icons";
import { Dayjs } from "dayjs";
import { useCallback, useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import BaseSelect from "@/components/ui/selects/BaseSelect";
import BaseInput from "@/components/ui/inputs/BaseInput";
import BaseInputNumber from "@/components/ui/inputs/BaseInputNumber";
import {
  JOB_TYPE_KEYS,
  WORK_TYPE_KEYS,
  EXPERIENCE_LEVEL_KEYS,
} from "@/constants/post";
import { useOptions } from "@/hooks/useTranslateOptions";
import {
  ENUM_POST_SALARY_CURRENCY,
  ENUM_POST_SALARY_PERIOD,
  ENUM_WORK_DAY_VALUE,
} from "@/constants/enum";
import { getSalaryPeriodSelectOptions } from "@/enums/salaryPeriod";

export interface TimeRange {
  start: Dayjs | null;
  end: Dayjs | null;
}

export interface DaySchedule {
  day: string;
  timeRanges: TimeRange[];
  isActive: boolean;
}

interface WorkScheduleProps {
  name?: string;
  label?: string;
  value?: DaySchedule[];
  onChange?: (value: DaySchedule[]) => void;
  form?: any;
}

const WorkSchedule: React.FC<WorkScheduleProps> = ({
  name = "workSchedule",
  label = "Work Schedule",
  value: propValue,
  onChange: propOnChange,
  form: formProp,
}) => {
  const form = formProp || Form.useFormInstance();
  const t = useTranslations("posts");
  const [error, setError] = useState<string | null>(null);

  // Validate work schedule
  const validateWorkSchedule = useCallback(
    (value: DaySchedule[]) => {
      const hasActiveDay = value.some((day) => day.isActive);
      if (!hasActiveDay) {
        const errorMsg = t("validation.at_least_one_day");
        setError(errorMsg);
        return Promise.reject(new Error(errorMsg));
      }

      const hasValidTimeRanges = value.every(
        (day) =>
          !day.isActive ||
          day.timeRanges.some((range) => range.start && range.end)
      );

      if (!hasValidTimeRanges) {
        const errorMsg = t("validation.set_time_ranges");
        setError(errorMsg);
        return Promise.reject(new Error(errorMsg));
      }

      setError(null);
      return Promise.resolve();
    },
    [t]
  );

  // // Register validation
  // useEffect(() => {
  //   form.validateFields([name]).catch(() => {});
  // }, [form, name]);

  // Initialize schedule with empty time ranges for each day
  const getInitialSchedule = useCallback(
    (): DaySchedule[] => [
      {
        day: "sunday",
        timeRanges: [{ start: null, end: null }],
        isActive: false,
      },
      {
        day: "monday",
        timeRanges: [{ start: null, end: null }],
        isActive: false,
      },
      {
        day: "tuesday",
        timeRanges: [{ start: null, end: null }],
        isActive: false,
      },
      {
        day: "wednesday",
        timeRanges: [{ start: null, end: null }],
        isActive: false,
      },
      {
        day: "thursday",
        timeRanges: [{ start: null, end: null }],
        isActive: false,
      },
      {
        day: "friday",
        timeRanges: [{ start: null, end: null }],
        isActive: false,
      },
      {
        day: "saturday",
        timeRanges: [{ start: null, end: null }],
        isActive: false,
      },
    ],
    []
  );

  // Initialize with propValue or empty schedule
  const [schedule, setSchedule] = useState<DaySchedule[]>(
    propValue || getInitialSchedule()
  );

  // Update internal state when prop value changes
  useEffect(() => {
    if (propValue && propValue.length > 0) {
      setSchedule(propValue);
    } else if (schedule.length === 0) {
      setSchedule(getInitialSchedule());
    }
  }, [propValue, schedule.length, getInitialSchedule]);

  // Update internal state when prop value changes
  useEffect(() => {
    if (propValue) {
      setSchedule(propValue);
    }
  }, [propValue]);

  // Notify parent component of changes
  const handleScheduleChange = useCallback(
    (newSchedule: DaySchedule[]) => {
      setSchedule(newSchedule);
      if (propOnChange) {
        propOnChange(newSchedule);
      }
      if (form && name) {
        form.setFieldsValue({ [name]: newSchedule });
      }
    },
    [form, name, propOnChange]
  );

  // Update form when schedule changes
  useEffect(() => {
    handleScheduleChange(schedule);
  }, [schedule, handleScheduleChange]);

  const handleTimeChange = (
    dayIndex: number,
    rangeIndex: number,
    field: "start" | "end",
    time: Dayjs | null
  ) => {
    const newSchedule = [...schedule];
    newSchedule[dayIndex].timeRanges[rangeIndex][field] = time;

    setSchedule(newSchedule);
  };

  const addTimeRange = (dayIndex: number) => {
    const newSchedule = [...schedule];
    newSchedule[dayIndex].timeRanges.push({ start: null, end: null });
    setSchedule(newSchedule);
  };

  const removeTimeRange = (dayIndex: number, rangeIndex: number) => {
    const newSchedule = [...schedule];
    newSchedule[dayIndex].timeRanges.splice(rangeIndex, 1);
    // Ensure there's always at least one time range
    if (newSchedule[dayIndex].timeRanges.length === 0) {
      newSchedule[dayIndex].timeRanges.push({ start: null, end: null });
    }
    setSchedule(newSchedule);
  };

  const toggleDayActive = (dayIndex: number) => {
    const newSchedule = [...schedule];
    newSchedule[dayIndex].isActive = !newSchedule[dayIndex].isActive;
    setSchedule(newSchedule);
  };

  return (
    <Form.Item
      name={name}
      label={label}
      required
      className="w-full"
      rules={[
        {
          validator: (_, value) => validateWorkSchedule(value || []),
        },
      ]}
      help={error}
      validateStatus={error ? "error" : ""}
    >
      <Card className="mb-6">
        <div className="mb-4">
          <Space size={8} className="mb-4">
            {Object.entries(ENUM_WORK_DAY_VALUE).map(
              ([dayKey, dayValue], index) => (
                <Button
                  key={dayKey}
                  type={schedule[index].isActive ? "primary" : "default"}
                  shape="circle"
                  onClick={() => toggleDayActive(index)}
                  className={`flex items-center justify-center ${
                    schedule[index].isActive ? "bg-blue-500" : "bg-gray-100"
                  }`}
                >
                  {t(`work_schedule.day_abbr.${dayKey.toLowerCase()}`)}
                </Button>
              )
            )}
          </Space>

          <div className="space-y-3">
            {schedule
              .filter((day) => day.isActive)
              .map((day) => {
                const originalIndex = schedule.findIndex(
                  (d) => d.day === day.day
                );
                return (
                  <div key={day.day} className="flex items-start gap-4">
                    <div className="w-24">
                      <Typography.Text>
                        {t(`work_schedule.days.${day.day}`)}
                      </Typography.Text>
                    </div>
                    <div className="flex-1 space-y-2">
                      {day.timeRanges.map((range, rangeIndex) => (
                        <Space
                          key={rangeIndex}
                          className="w-full"
                          align="center"
                        >
                          <TimePicker
                            value={range.start}
                            onChange={(time) =>
                              handleTimeChange(
                                originalIndex,
                                rangeIndex,
                                "start",
                                time
                              )
                            }
                            onSelect={(time) =>
                              handleTimeChange(
                                originalIndex,
                                rangeIndex,
                                "start",
                                time
                              )
                            }
                            format="h:mm A"
                            className="w-32"
                            placeholder={t("work_schedule.start")}
                            minuteStep={15}
                            showNow={false}
                            inputReadOnly
                            allowClear={false}
                          />
                          <span>{t("work_schedule.to")}</span>
                          <TimePicker
                            value={range.end}
                            onChange={(time) =>
                              handleTimeChange(
                                originalIndex,
                                rangeIndex,
                                "end",
                                time
                              )
                            }
                            onSelect={(time) =>
                              handleTimeChange(
                                originalIndex,
                                rangeIndex,
                                "end",
                                time
                              )
                            }
                            format="h:mm A"
                            className="w-32"
                            placeholder={t("work_schedule.end")}
                            minuteStep={15}
                            showNow={false}
                            inputReadOnly
                            allowClear={false}
                          />
                          <Button
                            type="text"
                            icon={<PlusOutlined />}
                            onClick={() => addTimeRange(originalIndex)}
                            className="text-blue-500"
                          />
                          {day.timeRanges.length > 1 && (
                            <Button
                              type="text"
                              icon={<MinusOutlined />}
                              onClick={() =>
                                removeTimeRange(originalIndex, rangeIndex)
                              }
                              className="text-red-500"
                              danger
                            />
                          )}
                        </Space>
                      ))}
                    </div>
                  </div>
                );
              })}
          </div>
        </div>
      </Card>
    </Form.Item>
  );
};

interface JobDetailsTabProps {
  form?: any;
}

const JobDetailsTab: React.FC<JobDetailsTabProps> = ({ form }) => {
  const t = useTranslations("posts");
  const jobTypeOptions = useOptions("job.types", Object.values(JOB_TYPE_KEYS));
  const workTypeOptions = useOptions(
    "job.work_types",
    Object.values(WORK_TYPE_KEYS)
  );
  const experienceLevelOptions = useOptions(
    "job.experience_levels",
    Object.values(EXPERIENCE_LEVEL_KEYS)
  );

  const salaryPeriodOptions = getSalaryPeriodSelectOptions();

  const salaryCurrencyOptions = useOptions(
    "job.salary_currencies",
    Object.values(ENUM_POST_SALARY_CURRENCY)
  );

  return (
    <Card title={t("basic_information")} className="mb-6">
      <Flex gap="middle" vertical>
        <Flex gap="middle">
          <BaseSelect
            name="jobType"
            label={t("job_type")}
            required
            options={jobTypeOptions}
            placeholder={t("placeholders.select_job_type")}
            className="w-1/2"
          />

          <BaseSelect
            name="workType"
            label={t("work_type")}
            required
            options={workTypeOptions}
            placeholder={t("placeholders.select_work_type")}
            className="w-1/2"
          />
        </Flex>

        {/* Work Schedule Component */}
        <WorkSchedule
          name="workSchedule"
          label={t("work_schedule.other_periods")}
          form={form}
        />

        <Flex gap="middle">
          <Flex className="flex-2 flex" gap="middle">
            <BaseInputNumber
              name="salaryMin"
              label={t("salary_min")}
              required
              placeholder={t("placeholders.salary")}
              formatter={(value) =>
                `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
              }
              step={10000}
              min={0}
              parser={(value) =>
                value?.replace(/\$\s?|,*/g, "") as unknown as number
              }
              className="flex-2 w-auto"
            />
            <BaseInputNumber
              name="salaryMax"
              label={t("salary_max")}
              required
              placeholder={t("placeholders.salary")}
              formatter={(value) =>
                `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
              }
              step={10000}
              parser={(value) =>
                value?.replace(/\$\s?|,*/g, "") as unknown as number
              }
              className="flex-2 w-auto"
            />
            <BaseSelect
              name="salaryCurrency"
              label={t("salary_currency")}
              options={salaryCurrencyOptions}
              defaultValue={ENUM_POST_SALARY_CURRENCY.VND}
              className="flex-1 w-auto min-w-[120px]"
            />
            <BaseSelect
              name="salaryPeriod"
              label={t("salary_period")}
              required
              options={salaryPeriodOptions}
              placeholder={t("placeholders.select_salary_period")}
              className="flex-1 w-auto min-w-[120px]"
              defaultValue="MONTH"
            />
          </Flex>

          <Flex className="flex-1">
            <BaseSelect
              name="experienceLevel"
              label={t("experience_level")}
              required
              options={experienceLevelOptions}
              placeholder={t("placeholders.select_experience_level")}
              className="w-1/2"
            />
          </Flex>
        </Flex>
        <Flex gap="middle">
          <BaseInputNumber
            name="positions"
            label={t("positions")}
            required
            min={1}
            placeholder={t("placeholders.positions")}
          />
        </Flex>
      </Flex>
    </Card>
  );
};

export default JobDetailsTab;
