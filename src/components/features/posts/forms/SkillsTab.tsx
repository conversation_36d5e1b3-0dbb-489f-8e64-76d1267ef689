"use client";
import BaseInput from "@/components/ui/inputs/BaseInput";
import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import { Button, Card, Flex, Form, FormInstance, Typography } from "antd";
import { useTranslations } from "next-intl";
import React, { useState } from "react";

interface SkillsTabProps {
  form: FormInstance;
}

const { Text } = Typography;

const SkillsTab: React.FC<SkillsTabProps> = ({ form }) => {
  const tPost = useTranslations("posts");
  const [skillInput, setSkillInput] = useState("");

  const handleAddSkill = () => {
    if (!skillInput.trim()) return;

    const skills = form.getFieldValue("skills") || [];
    const newSkills = [...skills, skillInput.trim()];

    // Use setFieldValue instead of setFieldsValue for single field
    form.setFieldValue("skills", newSkills);
    setSkillInput("");
  };

  const handleRemoveSkill = (indexToRemove: number) => {
    const skills = form.getFieldValue("skills") || [];
    const newSkills = skills.filter(
      (_: string, index: number) => index !== indexToRemove
    );

    // Use setFieldValue to trigger re-render
    form.setFieldValue("skills", newSkills);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddSkill();
    }
  };

  return (
    <Card title={tPost("skills.title")} className="mb-6">
      <div className="space-y-4">
        {/* Input for adding new skills */}
        <BaseInput
          label={tPost("skills.label")}
          placeholder={tPost("skills.placeholder")}
          value={skillInput}
          onChange={(e) => setSkillInput(e.target.value)}
          onPressEnter={handleKeyPress}
          suffix={
            <Button
              type="text"
              size="small"
              onClick={handleAddSkill}
              disabled={!skillInput.trim()}
              title={tPost("skills.add_skill")}
            >
              <PlusOutlined />
            </Button>
          }
        />

        {/* Watch the skills field to trigger re-renders */}
        <Form.Item
          shouldUpdate={(prevValues, currentValues) =>
            prevValues.skills !== currentValues.skills
          }
        >
          {({ getFieldValue }) => {
            const skills = getFieldValue("skills") || [];

            return (
              <div className="space-y-2">
                {skills.map((skill: string, index: number) => (
                  <Flex
                    key={`${skill}-${index}`}
                    justify="space-between"
                    align="center"
                  >
                    <Text>• {skill}</Text>
                    <Button
                      type="text"
                      danger
                      size="small"
                      icon={<MinusCircleOutlined />}
                      onClick={() => handleRemoveSkill(index)}
                      title={tPost("skills.remove_skill")}
                    />
                  </Flex>
                ))}

                {skills.length === 0 && (
                  <Text type="secondary" className="text-sm">
                    {tPost("skills.no_skills_added")}
                  </Text>
                )}
              </div>
            );
          }}
        </Form.Item>

        {/* Hidden Form.Item to store the actual skills data */}
        <Form.Item name="skills" hidden>
          <input type="hidden" />
        </Form.Item>
      </div>
    </Card>
  );
};

export default SkillsTab;
