"use client";
import type { Post, PostStatus } from "@/types/post";
import PostService from "@/services/postService";
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  PauseCircleOutlined,
} from "@ant-design/icons";
import {
  Button,
  Card,
  Flex,
  message,
  Space,
  Typography,
  Popconfirm,
} from "antd";
import React from "react";
import { useTranslations } from "next-intl";

const { Text } = Typography;

interface PostStatusCardProps {
  post: Post;
  onStatusChange: (newStatus: PostStatus) => Promise<void>;
}

const PostStatusCard: React.FC<PostStatusCardProps> = ({
  post,
  onStatusChange,
}) => {
  const t = useTranslations("posts");
  const tCommon = useTranslations("common");

  const handleActivatePost = async () => {
    await PostService.activate(post.id.toString());
    message.success(
      tCommon("messages.success.post_updated") || "Post activated successfully"
    );
    window.location.reload();
  };

  return (
    <Card>
      <Flex justify="space-between" align="center">
        <Flex align="center" gap="small">
          <Text strong>{t("title")}:</Text>
          {post.title}
        </Flex>
        <Flex align="center" gap="small">
          <Text strong>{tCommon("fields.status")}:</Text>
          {post.status}
        </Flex>
        <Space>
          {post.status === "pending" && (
            <Popconfirm
              title={t("activate_post_confirm")}
              description={t("activate_post_confirm_desc")}
              onConfirm={handleActivatePost}
              okText={tCommon("responses.yes")}
              cancelText={tCommon("responses.no")}
            >
              <Button icon={<CheckCircleOutlined />} type="primary">
                {t("activate")}
              </Button>
            </Popconfirm>
          )}
          {post.status !== "Bản nháp" && (
            <Button
              icon={<PauseCircleOutlined />}
              onClick={() => onStatusChange("Bản nháp")}
            >
              {tCommon("actions.save_as_draft")}
            </Button>
          )}
          {post.status !== "Đã đóng" && (
            <Button
              icon={<CloseCircleOutlined />}
              danger
              onClick={() => onStatusChange("Đã đóng")}
            >
              {t("close_post") || "Close Post"}
            </Button>
          )}
        </Space>
      </Flex>
    </Card>
  );
};

export default PostStatusCard;
