import React, { useEffect, useState, useCallback } from "react";
import {
  Form,
  Button,
  Modal,
  Row,
  Col,
  Card,
  Typography,
  Space,
  Input,
  Switch,
  DatePicker,
  Select,
  Checkbox,
  InputNumber,
  message,
} from "antd";
import {
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  EnvironmentOutlined,
  LinkedinOutlined,
  GlobalOutlined,
  BankOutlined,
  BookOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  SaveOutlined,
  CloseOutlined,
  PlusOutlined,
  DeleteOutlined,
} from "@ant-design/icons";
import { Resume } from "@/types/resume";
import { ExtendedUser } from "@/types/user";
import { resumeToFormValues } from "@/constants/resume";
import dayjs from "dayjs";
import BaseModal from "@/components/ui/modals/BaseModal";
import { createResume, updateResume } from "@/services/resumeService";
import { LANGUAGE_LEVELS } from "@/constants/languageLevels";
import { useTranslations } from "next-intl";

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface ResumeFormProps {
  currentResume: Resume | null;
  isFormModalVisible: boolean;
  setIsFormModalVisible: (visible: boolean) => void;
  userInfo: ExtendedUser;
  mode?: "view" | "edit";
}

const ResumeFormModal: React.FC<ResumeFormProps> = ({
  currentResume,
  isFormModalVisible,
  setIsFormModalVisible,
  userInfo,
  mode = "edit",
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("personal");
  const [error, setError] = useState<string | null>(null);
  const isView = mode === "view";
  const t = useTranslations("resume");

  const availableDays = [
    t("monday"),
    t("tuesday"),
    t("wednesday"),
    t("thursday"),
    t("friday"),
    t("saturday"),
    t("sunday"),
  ];
  const timeSlots = [t("morning"), t("afternoon"), t("evening"), t("night")];

  // Helper function to format address from UserAddress
  const formatUserAddress = (address?: any): string => {
    if (!address) return "";
    const parts = [
      address.detailAddress,
      address.wardName,
      address.districtName,
      address.provinceName,
    ].filter(Boolean);
    return parts.join(", ");
  };

  // Get initial contact info from userInfo
  const getInitialContactInfo = useCallback(() => {
    return {
      contactInfo: {
        phoneNumber: userInfo.phoneNumber || "",
        email: userInfo.email || "",
        address: formatUserAddress(userInfo.address) || "",
        city: userInfo.address?.wardName || "",
        state: userInfo.address?.provinceName || "",
        zipCode: "",
        country: "Vietnam",
        linkedInUrl: "",
        portfolioUrl: "",
      },
      partTime: {
        isPartTime: false,
      },
      partTimePreference: {
        minHourlyRate: undefined,
        maxHoursPerWeek: undefined,
        availableDays: [],
        availableTimeSlots: [],
        preferredJobTypes: [],
        preferredLocations: [],
        remoteOnly: false,
        maxTravelDistance: undefined,
        additionalNotes: "",
        isStudent: false,
        studyMajor: null,
      },
    };
  }, [userInfo.phoneNumber, userInfo.email, userInfo.address]);

  const initialValues = {
    isActive: false,
    userId: userInfo.id,
    workExperiences: [{}],
    educations: [{}],
    languages: [{}],
    ...getInitialContactInfo(),
  };

  useEffect(() => {
    if (currentResume) {
      const formValues = resumeToFormValues(currentResume);
      // Convert workExperiences[*].startDate/endDate to dayjs
      if (Array.isArray(formValues.workExperiences)) {
        formValues.workExperiences = formValues.workExperiences.map((exp) => ({
          ...exp,
          startDate: exp.startDate ? dayjs(exp.startDate) : exp.startDate,
          endDate: exp.endDate ? dayjs(exp.endDate) : exp.endDate,
        }));
      }
      // Convert educations[*].startDate/endDate to dayjs
      if (Array.isArray(formValues.educations)) {
        formValues.educations = formValues.educations.map((edu) => ({
          ...edu,
          startDate: edu.startDate ? dayjs(edu.startDate) : edu.startDate,
          endDate: edu.endDate ? dayjs(edu.endDate) : edu.endDate,
        }));
      }
      form.setFieldsValue(formValues);
    } else {
      // Pre-populate contact info for new resume
      const contactInfo = getInitialContactInfo();
      form.resetFields();
      form.setFieldsValue(contactInfo);
    }
  }, [currentResume, form, getInitialContactInfo]);

  // --- Section Renderers ---
  const renderBasicInfo = () => (
    <Card
      title={
        <Title level={4} style={{ margin: 0 }}>
          <UserOutlined /> Basic Information
        </Title>
      }
      style={{ marginBottom: 24 }}
    >
      <Form.Item
        label="Resume Name"
        name="name"
        rules={
          isView
            ? []
            : [{ required: true, message: "Please enter resume name" }]
        }
      >
        {isView ? (
          <Text>{form.getFieldValue("name")}</Text>
        ) : (
          <Input placeholder="Enter resume name" size="large" />
        )}
      </Form.Item>
      <Form.Item label="Description" name="description">
        {isView ? (
          <Text>{form.getFieldValue("description")}</Text>
        ) : (
          <TextArea
            placeholder="Enter resume description"
            rows={4}
            showCount
            maxLength={500}
          />
        )}
      </Form.Item>
      <Form.Item
        label="Skills"
        name="skills"
        rules={
          isView ? [] : [{ required: true, message: "Please enter skills" }]
        }
      >
        {isView ? (
          <Space wrap>
            {(form.getFieldValue("skills") || "")
              .split(",")
              .map((s: string, i: number) => (
                <span
                  key={i}
                  style={{
                    background: "#e6f7ff",
                    padding: "4px 12px",
                    borderRadius: 4,
                    marginRight: 4,
                  }}
                >
                  {s.trim()}
                </span>
              ))}
          </Space>
        ) : (
          <Input placeholder={t("enter_skills_placeholder")} size="large" />
        )}
      </Form.Item>
      <Form.Item name="isActive" valuePropName="checked">
        <Space>
          <Switch disabled={isView} />
          <Text>{t("set_as_active_resume")}</Text>
        </Space>
      </Form.Item>
    </Card>
  );

  const renderContactInfo = () => (
    <Card
      title={
        <Title level={4} style={{ margin: 0 }}>
          <PhoneOutlined /> Contact Information
        </Title>
      }
      style={{ marginBottom: 24 }}
    >
      <Form.Item
        label="Phone Number"
        name={["contactInfo", "phoneNumber"]}
        rules={
          isView
            ? []
            : [{ required: true, message: "Please enter phone number" }]
        }
      >
        {isView ? (
          <div>
            <div style={{ fontWeight: 500, marginBottom: 2 }}>Phone Number</div>
            <Text>
              {form.getFieldValue(["contactInfo", "phoneNumber"]) || "-"}
            </Text>
          </div>
        ) : (
          <Input
            prefix={<PhoneOutlined />}
            placeholder="Enter phone number"
            size="large"
          />
        )}
      </Form.Item>
      <Form.Item
        label="Email"
        name={["contactInfo", "email"]}
        rules={
          isView
            ? []
            : [
                { required: true, message: "Please enter email" },
                { type: "email", message: "Please enter valid email" },
              ]
        }
      >
        {isView ? (
          <div>
            <div style={{ fontWeight: 500, marginBottom: 2 }}>Email</div>
            <Text>{form.getFieldValue(["contactInfo", "email"]) || "-"}</Text>
          </div>
        ) : (
          <Input
            prefix={<MailOutlined />}
            placeholder="Enter email address"
            size="large"
          />
        )}
      </Form.Item>
      <Form.Item label="Address" name={["contactInfo", "address"]}>
        {isView ? (
          <div>
            <div style={{ fontWeight: 500, marginBottom: 2 }}>Address</div>
            <Text>{form.getFieldValue(["contactInfo", "address"]) || "-"}</Text>
          </div>
        ) : (
          <TextArea placeholder="Enter address" rows={2} />
        )}
      </Form.Item>
      <Form.Item label="LinkedIn URL" name={["contactInfo", "linkedInUrl"]}>
        {isView ? (
          <div>
            <div style={{ fontWeight: 500, marginBottom: 2 }}>LinkedIn</div>
            <Text>
              {form.getFieldValue(["contactInfo", "linkedInUrl"]) || "-"}
            </Text>
          </div>
        ) : (
          <Input
            prefix={<LinkedinOutlined />}
            placeholder="Enter LinkedIn profile URL"
            size="large"
          />
        )}
      </Form.Item>
      <Form.Item label="Portfolio URL" name={["contactInfo", "portfolioUrl"]}>
        {isView ? (
          <div>
            <div style={{ fontWeight: 500, marginBottom: 2 }}>Portfolio</div>
            <Text>
              {form.getFieldValue(["contactInfo", "portfolioUrl"]) || "-"}
            </Text>
          </div>
        ) : (
          <Input
            prefix={<GlobalOutlined />}
            placeholder="Enter portfolio website URL"
            size="large"
          />
        )}
      </Form.Item>
    </Card>
  );

  const renderLanguages = () => (
    <Card
      title={
        <Title level={4} style={{ margin: 0 }}>
          <GlobalOutlined /> Languages
        </Title>
      }
      style={{ marginBottom: 24 }}
    >
      <Form.List name="languages">
        {(fields, { add, remove }) => (
          <>
            {fields.map(({ key, name, ...restField }) => (
              <Card key={key} type="inner" style={{ marginBottom: 16 }}>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      {...restField}
                      name={[name, "language"]}
                      label="Language"
                      rules={
                        isView
                          ? []
                          : [
                              {
                                required: true,
                                message: "Please enter language",
                              },
                            ]
                      }
                    >
                      {isView ? (
                        <Text>
                          {form.getFieldValue(["languages", name, "language"])}{" "}
                        </Text>
                      ) : (
                        <Input placeholder="e.g., English" />
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={10}>
                    <Form.Item
                      {...restField}
                      name={[name, "level"]}
                      label="Level"
                      rules={
                        isView
                          ? []
                          : [{ required: true, message: "Please select level" }]
                      }
                    >
                      {isView ? (
                        <Text>
                          {form.getFieldValue(["languages", name, "level"])}{" "}
                        </Text>
                      ) : (
                        <Select placeholder="Select level">
                          {LANGUAGE_LEVELS.map((level) => (
                            <Option key={level.value} value={level.value}>
                              {level.label}
                            </Option>
                          ))}
                        </Select>
                      )}
                    </Form.Item>
                  </Col>
                  {!isView && (
                    <Col span={2}>
                      <Form.Item label=" ">
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => remove(name)}
                        />
                      </Form.Item>
                    </Col>
                  )}
                </Row>
              </Card>
            ))}
            {!isView && (
              <Button
                type="dashed"
                onClick={() => add()}
                icon={<PlusOutlined />}
                block
              >
                Add Language
              </Button>
            )}
          </>
        )}
      </Form.List>
    </Card>
  );

  const renderWorkExperience = () => (
    <Card
      title={
        <Title level={4} style={{ margin: 0 }}>
          <BankOutlined /> Work Experience
        </Title>
      }
      style={{ marginBottom: 24 }}
    >
      <Form.List name="workExperiences">
        {(fields, { add, remove }) => (
          <>
            {fields.map(({ key, name, ...restField }) => (
              <Card key={key} type="inner" style={{ marginBottom: 16 }}>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      {...restField}
                      name={[name, "position"]}
                      label="Position"
                      rules={
                        isView
                          ? []
                          : [
                              {
                                required: true,
                                message: "Please enter position",
                              },
                            ]
                      }
                    >
                      {isView ? (
                        <Text>
                          {form.getFieldValue([
                            "workExperiences",
                            name,
                            "position",
                          ])}{" "}
                        </Text>
                      ) : (
                        <Input placeholder="e.g., Software Developer" />
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      {...restField}
                      name={[name, "company"]}
                      label="Company"
                      rules={
                        isView
                          ? []
                          : [
                              {
                                required: true,
                                message: "Please enter company",
                              },
                            ]
                      }
                    >
                      {isView ? (
                        <Text>
                          {form.getFieldValue([
                            "workExperiences",
                            name,
                            "company",
                          ])}{" "}
                        </Text>
                      ) : (
                        <Input placeholder="e.g., Tech Company Inc." />
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      {...restField}
                      name={[name, "startDate"]}
                      label="Start Date"
                      rules={
                        isView
                          ? []
                          : [
                              {
                                required: true,
                                message: "Please select start date",
                              },
                            ]
                      }
                    >
                      {isView ? (
                        <Text>
                          {form
                            .getFieldValue([
                              "workExperiences",
                              name,
                              "startDate",
                            ])
                            ?.format?.("YYYY-MM-DD") || ""}
                        </Text>
                      ) : (
                        <DatePicker style={{ width: "100%" }} />
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      {...restField}
                      name={[name, "endDate"]}
                      label="End Date"
                    >
                      {isView ? (
                        <Text>
                          {form
                            .getFieldValue(["workExperiences", name, "endDate"])
                            ?.format?.("YYYY-MM-DD") || "Present"}
                        </Text>
                      ) : (
                        <DatePicker
                          style={{ width: "100%" }}
                          placeholder="Current"
                        />
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={22}>
                    <Form.Item
                      {...restField}
                      name={[name, "description"]}
                      label="Description"
                    >
                      {isView ? (
                        <Text>
                          {form.getFieldValue([
                            "workExperiences",
                            name,
                            "description",
                          ])}{" "}
                        </Text>
                      ) : (
                        <TextArea
                          rows={3}
                          placeholder="Describe your responsibilities and achievements"
                        />
                      )}
                    </Form.Item>
                  </Col>
                  {!isView && (
                    <Col span={2}>
                      <Form.Item label=" ">
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => remove(name)}
                        />
                      </Form.Item>
                    </Col>
                  )}
                </Row>
              </Card>
            ))}
            {!isView && (
              <Button
                type="dashed"
                onClick={() => add()}
                icon={<PlusOutlined />}
                block
              >
                Add Work Experience
              </Button>
            )}
          </>
        )}
      </Form.List>
    </Card>
  );

  const renderEducation = () => (
    <Card
      title={
        <Title level={4} style={{ margin: 0 }}>
          <BookOutlined /> Education
        </Title>
      }
      style={{ marginBottom: 24 }}
    >
      <Form.List name="educations">
        {(fields, { add, remove }) => (
          <>
            {fields.map(({ key, name, ...restField }) => (
              <Card key={key} type="inner" style={{ marginBottom: 16 }}>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      {...restField}
                      name={[name, "degree"]}
                      label="Degree"
                      rules={
                        isView
                          ? []
                          : [{ required: true, message: "Please enter degree" }]
                      }
                    >
                      {isView ? (
                        <Text>
                          {form.getFieldValue(["educations", name, "degree"])}{" "}
                        </Text>
                      ) : (
                        <Input placeholder="e.g., Bachelor of Science" />
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      {...restField}
                      name={[name, "institution"]}
                      label="Institution"
                      rules={
                        isView
                          ? []
                          : [
                              {
                                required: true,
                                message: "Please enter institution",
                              },
                            ]
                      }
                    >
                      {isView ? (
                        <Text>
                          {form.getFieldValue([
                            "educations",
                            name,
                            "institution",
                          ])}{" "}
                        </Text>
                      ) : (
                        <Input placeholder="e.g., University of Technology" />
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      {...restField}
                      name={[name, "fieldOfStudy"]}
                      label="Field of Study"
                    >
                      {isView ? (
                        <Text>
                          {form.getFieldValue([
                            "educations",
                            name,
                            "fieldOfStudy",
                          ])}{" "}
                        </Text>
                      ) : (
                        <Input placeholder="e.g., Computer Science" />
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      {...restField}
                      name={[name, "startDate"]}
                      label="Start Date"
                      rules={
                        isView
                          ? []
                          : [
                              {
                                required: true,
                                message: "Please select start date",
                              },
                            ]
                      }
                    >
                      {isView ? (
                        <Text>
                          {form
                            .getFieldValue(["educations", name, "startDate"])
                            ?.format?.("YYYY-MM-DD") || ""}
                        </Text>
                      ) : (
                        <DatePicker style={{ width: "100%" }} />
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      {...restField}
                      name={[name, "endDate"]}
                      label="End Date"
                    >
                      {isView ? (
                        <Text>
                          {form
                            .getFieldValue(["educations", name, "endDate"])
                            ?.format?.("YYYY-MM-DD") || "Present"}
                        </Text>
                      ) : (
                        <DatePicker
                          style={{ width: "100%" }}
                          placeholder="Current"
                        />
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={10}>
                    <Form.Item
                      {...restField}
                      name={[name, "description"]}
                      label="Description"
                    >
                      {isView ? (
                        <Text>
                          {form.getFieldValue([
                            "educations",
                            name,
                            "description",
                          ])}{" "}
                        </Text>
                      ) : (
                        <TextArea rows={2} placeholder="Additional details" />
                      )}
                    </Form.Item>
                  </Col>
                  {!isView && (
                    <Col span={2}>
                      <Form.Item label=" ">
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => remove(name)}
                        />
                      </Form.Item>
                    </Col>
                  )}
                </Row>
              </Card>
            ))}
            {!isView && (
              <Button
                type="dashed"
                onClick={() => add()}
                icon={<PlusOutlined />}
                block
              >
                Add Education
              </Button>
            )}
          </>
        )}
      </Form.List>
    </Card>
  );

  const renderPartTimePreferences = () => (
    <Card
      title={
        <Title level={4} style={{ margin: 0 }}>
          <ClockCircleOutlined /> Part-time Preferences
        </Title>
      }
      style={{ marginBottom: 24 }}
    >
      <Row gutter={[24, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Form.Item
            label="Min Hourly Rate"
            name={["partTimePreference", "minHourlyRate"]}
          >
            {isView ? (
              <Text>
                {form.getFieldValue(["partTimePreference", "minHourlyRate"]) ||
                  "-"}
              </Text>
            ) : (
              <InputNumber
                style={{ width: "100%" }}
                placeholder="Min rate"
                min={0}
              />
            )}
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Form.Item
            label="Max Hours Per Week"
            name={["partTimePreference", "maxHoursPerWeek"]}
          >
            {isView ? (
              <Text>
                {form.getFieldValue([
                  "partTimePreference",
                  "maxHoursPerWeek",
                ]) || "-"}
              </Text>
            ) : (
              <InputNumber
                style={{ width: "100%" }}
                placeholder="Max hours"
                min={1}
                max={168}
              />
            )}
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Form.Item
            label="Max Travel Distance"
            name={["partTimePreference", "maxTravelDistance"]}
          >
            {isView ? (
              <Text>
                {form.getFieldValue([
                  "partTimePreference",
                  "maxTravelDistance",
                ]) || "-"}
              </Text>
            ) : (
              <InputNumber
                style={{ width: "100%" }}
                placeholder="Distance (km)"
                min={0}
              />
            )}
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Form.Item
            name={["partTimePreference", "remoteOnly"]}
            valuePropName="checked"
          >
            <Space>
              <Switch disabled={isView} />
              <Text>Remote Only</Text>
            </Space>
          </Form.Item>
        </Col>
        <Col xs={24} sm={12}>
          <Form.Item
            label="Available Days"
            name={["partTimePreference", "availableDays"]}
          >
            {isView ? (
              <Space wrap>
                {(
                  form.getFieldValue(["partTimePreference", "availableDays"]) ||
                  []
                ).map((day: string, i: number) => (
                  <span
                    key={i}
                    style={{
                      background: "#f6ffed",
                      padding: "4px 12px",
                      borderRadius: 4,
                      marginRight: 4,
                    }}
                  >
                    {day}
                  </span>
                ))}
              </Space>
            ) : (
              <Checkbox.Group>
                <Row>
                  {availableDays.map((day) => (
                    <Col span={8} key={day}>
                      <Checkbox value={day}>{day}</Checkbox>
                    </Col>
                  ))}
                </Row>
              </Checkbox.Group>
            )}
          </Form.Item>
        </Col>
        <Col xs={24} sm={12}>
          <Form.Item
            label="Available Time Slots"
            name={["partTimePreference", "availableTimeSlots"]}
          >
            {isView ? (
              <Space wrap>
                {(
                  form.getFieldValue([
                    "partTimePreference",
                    "availableTimeSlots",
                  ]) || []
                ).map((slot: string, i: number) => (
                  <span
                    key={i}
                    style={{
                      background: "#e6f7ff",
                      padding: "4px 12px",
                      borderRadius: 4,
                      marginRight: 4,
                    }}
                  >
                    {slot}
                  </span>
                ))}
              </Space>
            ) : (
              <Checkbox.Group>
                <Row>
                  {timeSlots.map((slot) => (
                    <Col span={12} key={slot}>
                      <Checkbox value={slot}>{slot}</Checkbox>
                    </Col>
                  ))}
                </Row>
              </Checkbox.Group>
            )}
          </Form.Item>
        </Col>
        <Col xs={24} sm={12}>
          <Form.Item
            label="Preferred Job Types"
            name={["partTimePreference", "preferredJobTypes"]}
          >
            {isView ? (
              <Space wrap>
                {(
                  form.getFieldValue([
                    "partTimePreference",
                    "preferredJobTypes",
                  ]) || []
                ).map((type: string, i: number) => (
                  <span
                    key={i}
                    style={{
                      background: "#f9f0ff",
                      padding: "4px 12px",
                      borderRadius: 4,
                      marginRight: 4,
                    }}
                  >
                    {type}
                  </span>
                ))}
              </Space>
            ) : (
              <Select
                mode="tags"
                placeholder="Enter job types"
                style={{ width: "100%" }}
              />
            )}
          </Form.Item>
        </Col>
        <Col xs={24} sm={12}>
          <Form.Item
            label="Preferred Locations"
            name={["partTimePreference", "preferredLocations"]}
          >
            {isView ? (
              <Space wrap>
                {(
                  form.getFieldValue([
                    "partTimePreference",
                    "preferredLocations",
                  ]) || []
                ).map((loc: string, i: number) => (
                  <span
                    key={i}
                    style={{
                      background: "#fffbe6",
                      padding: "4px 12px",
                      borderRadius: 4,
                      marginRight: 4,
                    }}
                  >
                    {loc}
                  </span>
                ))}
              </Space>
            ) : (
              <Select
                mode="tags"
                placeholder="Enter preferred locations"
                style={{ width: "100%" }}
              />
            )}
          </Form.Item>
        </Col>
        <Col xs={24} sm={12}>
          <Form.Item
            name={["partTimePreference", "isStudent"]}
            valuePropName="checked"
            noStyle
          >
            <Switch disabled={isView} />
          </Form.Item>
          <Text style={{ marginLeft: 8 }}>Currently a Student</Text>
        </Col>
        <Col xs={24} sm={12}>
          <Form.Item
            label="Study Major"
            name={["partTimePreference", "studyMajor"]}
          >
            {isView ? (
              <Text>
                {form.getFieldValue(["partTimePreference", "studyMajor"]) ||
                  "-"}
              </Text>
            ) : (
              <Input placeholder="Enter study major" />
            )}
          </Form.Item>
        </Col>
        <Col xs={24}>
          <Form.Item
            label="Additional Notes"
            name={["partTimePreference", "additionalNotes"]}
          >
            {isView ? (
              <Text>
                {form.getFieldValue([
                  "partTimePreference",
                  "additionalNotes",
                ]) || "-"}
              </Text>
            ) : (
              <TextArea
                rows={3}
                placeholder="Any additional information or requirements"
                showCount
                maxLength={1000}
              />
            )}
          </Form.Item>
        </Col>
      </Row>
    </Card>
  );

  // --- Form Submit ---
  const handleFormSubmit = async () => {
    setLoading(true);
    try {
      const values = await form.validateFields();
      let formattedSkills = values.skills;
      if (typeof formattedSkills === "string") {
        formattedSkills = formattedSkills
          .split(",")
          .map((s: string) => s.trim())
          .filter(Boolean);
      }

      // Format work experiences and educations dates
      const formData = {
        ...values,
        skills: formattedSkills,
        userId: userInfo.id,
        workExperiences: values.workExperiences?.map((exp: any) => ({
          ...exp,
          startDate: exp.startDate ? exp.startDate.format("YYYY-MM-DD") : null,
          endDate: exp.endDate ? exp.endDate.format("YYYY-MM-DD") : null,
        })),
        educations: values.educations?.map((edu: any) => ({
          ...edu,
          startDate: edu.startDate ? edu.startDate.format("YYYY-MM-DD") : null,
          endDate: edu.endDate ? edu.endDate.format("YYYY-MM-DD") : null,
        })),
      };

      if (currentResume) {
        // Update existing resume
        await updateResume(currentResume.id, formData);
        message.success("Resume updated successfully!");
      } else {
        // Create new resume
        await createResume(formData);
        message.success("Resume created successfully!");
      }

      setIsFormModalVisible(false);
      // Trigger refresh of parent component if needed
      window.location.reload();
    } catch (error: any) {
      console.error("Error saving resume:", error);

      // Handle validation errors
      if (error?.errorFields && error.errorFields.length > 0) {
        const firstError = error.errorFields[0];
        const fieldName = Array.isArray(firstError.name)
          ? firstError.name.join(".")
          : firstError.name;
        const errorMsg = firstError.errors?.[0] || "This field is required";

        message.error(`Validation Error: ${errorMsg}`);

        // Scroll to the first error field
        form.scrollToField(firstError.name);
        return;
      }

      // Handle API errors
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        `Failed to ${currentResume ? "update" : "create"} resume`;

      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // --- Footer ---
  const renderFooter = () => (
    <div className="flex flex-row-reverse gap-2">
      {isView ? (
        <Button
          onClick={() => setIsFormModalVisible(false)}
          icon={<CloseOutlined />}
        >
          Close
        </Button>
      ) : (
        <>
          <Button
            type="primary"
            onClick={handleFormSubmit}
            loading={loading}
            icon={<SaveOutlined />}
          >
            {currentResume ? "Update Resume" : "Create Resume"}
          </Button>
          <Button
            onClick={() => setIsFormModalVisible(false)}
            icon={<CloseOutlined />}
          >
            Cancel
          </Button>
        </>
      )}
    </div>
  );

  return (
    <BaseModal
      title={
        currentResume
          ? isView
            ? t("view_resume")
            : t("edit_resume")
          : t("create_new_resume")
      }
      isVisible={isFormModalVisible}
      onClose={() => setIsFormModalVisible(false)}
      footer={renderFooter()}
      width={1500}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={initialValues}
        disabled={isView}
      >
        {renderBasicInfo()}
        {renderLanguages()}
        {renderWorkExperience()}
        {renderEducation()}
        {renderContactInfo()}
        {renderPartTimePreferences()}
      </Form>
    </BaseModal>
  );
};

export default ResumeFormModal;
