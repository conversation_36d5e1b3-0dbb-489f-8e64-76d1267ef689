import React from "react";
import { <PERSON>, Row, Col } from "antd";
import BasicInfo from "./BasicInfo";
import ContactInfo from "./ContactInfo";
import Skills from "./Skills";

interface HeaderProps {
  basicInfo: {
    name: string;
    isActive: boolean;
    description?: string;
  };
  contactInfo?: {
    phoneNumber?: string;
    phone?: string;
    email?: string;
    address?: string;
    linkedInUrl?: string;
    portfolioUrl?: string;
  };
  skills?: string[];
}

const Header: React.FC<HeaderProps> = ({ basicInfo, contactInfo, skills }) => {
  return (
    <Card>
      <Row gutter={[24, 24]} align="top">
        {/* Left Side - Avatar + Basic Info */}
        <Col xs={24} lg={12}>
          <BasicInfo basicInfo={basicInfo} />
          <Skills skills={skills} />
        </Col>

        {/* Right Side - Contact Information */}
        <Col xs={24} lg={12}>
          <ContactInfo contactInfo={contactInfo} />
        </Col>
      </Row>
    </Card>
  );
};

export default Header;
