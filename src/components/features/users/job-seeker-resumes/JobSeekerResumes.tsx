"use client";

import { deleteResume, getUserResumes } from "@/services/resumeService";
import { Resume, ResumeFormValues } from "@/types/resume";
import { ExtendedUser } from "@/types/user";
import { Card, Form, message } from "antd";
import React, { useState, useCallback } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { PermissionEnum } from "@/constants/_permissionEnum";
import { useTranslations } from "next-intl";

import ResumeDetailsModal from "@/components/features/users/job-seeker-resumes/ResumeDetailsModal";
import ResumeFormModal from "@/components/features/users/job-seeker-resumes/ResumeFormModal";
import DeleteResumeModal from "./DeleteResumeModal";
import ResumeTable from "./ResumeTable";

interface JobSeekerResumesProps {
  userId: string | number;
  userInfo: ExtendedUser;
}

// Helper to normalize resume fields from API
function normalizeResume(item: any) {
  return {
    ...item,
    lastUpdated: item.lastUpdateAt || item.lastUpdated,
  };
}

const JobSeekerResumes: React.FC<JobSeekerResumesProps> = ({
  userId,
  userInfo,
}) => {
  console.log("🚀 ~ userId:", userId);
  const [form] = Form.useForm<ResumeFormValues>();
  const [isFormModalVisible, setIsFormModalVisible] = useState(false);
  const [isViewModalVisible, setIsViewModalVisible] = useState(false);
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [currentResume, setCurrentResume] = useState<Resume | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { access } = useAuth();
  const t = useTranslations("resume");

  const fetchUserResumesApi = useCallback(
    async (params?: Record<string, unknown>) => {
      try {
        const response = await getUserResumes(userId); // response is full API object
        const resumes = (response.data || []).map(normalizeResume);
        return {
          data: resumes,
          total: resumes.length,
        };
      } catch (error) {
        message.error(
          `Failed to load resumes: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
        throw error;
      }
    },
    [userId]
  );

  const handleOpenCreate = () => {
    setCurrentResume(null);
    form.resetFields();
    setIsFormModalVisible(true);
  };

  const handleEdit = (resume: Resume) => {
    setCurrentResume(resume);
    setIsFormModalVisible(true);
  };

  const handleView = (resume: Resume) => {
    setCurrentResume(resume);
    setIsViewModalVisible(true);
  };

  const handleDelete = async (resume: Resume) => {
    setCurrentResume(resume);
    setIsDeleteModalVisible(true);
  };

  const handleDeleteConfirm = async () => {
    if (!currentResume) return;

    try {
      setIsSubmitting(true);
      await deleteResume(currentResume.id);
      message.success(`Resume "${currentResume.name}" deleted successfully`);
      setIsDeleteModalVisible(false);
    } catch (error) {
      message.error(
        `Failed to delete resume: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card title={t("resumes")}>
      <ResumeTable
        onView={handleView}
        onEdit={access?.[PermissionEnum.RESUME_UPDATE] ? handleEdit : undefined}
        onDelete={
          access?.[PermissionEnum.RESUME_DELETE] ? handleDelete : undefined
        }
        onCreate={
          access?.[PermissionEnum.RESUME_CREATE] ? handleOpenCreate : undefined
        }
        api={fetchUserResumesApi}
        access={access}
      />

      <ResumeFormModal
        currentResume={currentResume}
        isFormModalVisible={isFormModalVisible}
        setIsFormModalVisible={setIsFormModalVisible}
        userInfo={userInfo}
      />

      <ResumeDetailsModal
        currentResume={currentResume}
        isViewModalVisible={isViewModalVisible}
        setIsViewModalVisible={setIsViewModalVisible}
        handleEdit={
          access?.[PermissionEnum.RESUME_UPDATE] ? handleEdit : undefined
        }
        access={access}
      />

      <DeleteResumeModal
        resume={currentResume}
        isVisible={isDeleteModalVisible}
        isLoading={isSubmitting}
        onClose={() => setIsDeleteModalVisible(false)}
        onConfirm={handleDeleteConfirm}
      />
    </Card>
  );
};

export default JobSeekerResumes;
