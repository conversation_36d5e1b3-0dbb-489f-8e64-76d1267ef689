import React from "react";
import BaseInput from "@/components/ui/inputs/BaseInput";
import BaseTextArea from "@/components/ui/inputs/BaseTextArea";
import BaseCheckbox from "@/components/ui/checkboxes/BaseCheckbox";
import DebounceSelect from "@/components/ui/selects/DebounceSelect";
import UserService from "@/services/userService";
import { Form } from "antd";
import { useTranslations } from "next-intl";

interface BasicInfoTabProps {
  userSelectable?: boolean;
}

const fetchUserOptions = async (search: string) => {
  const res = await UserService.getList({ search });
  return { data: res.data };
};

const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ userSelectable }) => {
  const tResume = useTranslations("resume");

  return (
    <>
      {userSelectable && (
        <DebounceSelect
          name="userId"
          label={tResume("form.user")}
          required
          fetchOptions={UserService.fetchUserOptions}
          placeholder={tResume("form.search_and_select_user")}
        />
      )}
      <BaseInput
        label={tResume("form.resume_name")}
        name="name"
        required
        placeholder={tResume("form.resume_name_placeholder")}
      />

      <BaseTextArea
        label={tResume("form.resume_summary_description")}
        name="description"
        placeholder={tResume("form.resume_summary_placeholder")}
        rows={4}
      />

      <Form.Item
        label={tResume("form.skills")}
        name="skills"
        required
        rules={[
          { required: true, message: tResume("form.please_enter_at_least_one_skill") },
          {
            transform: (value) => {
              if (!value) return [];
              return value
                .split(",")
                .map((skill: string) => skill.trim())
                .filter(Boolean);
            },
          },
        ]}
        getValueFromEvent={(e) => e.target.value}
        getValueProps={(value) => ({
          value: Array.isArray(value) ? value.join(", ") : value,
        })}
      >
        <BaseInput
          placeholder={tResume("form.skills_placeholder")}
          helpText={tResume("form.skills_help_text")}
        />
      </Form.Item>

      <BaseCheckbox
        name="isActive"
        checkboxLabel={tResume("form.set_as_active_resume")}
        helpText={tResume("form.active_resume_help_text")}
      />
    </>
  );
};

export default BasicInfoTab;
