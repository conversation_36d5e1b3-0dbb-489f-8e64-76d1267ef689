import React from "react";
import { Button, Form, Input, Select } from "antd";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import { LANGUAGE_LEVELS } from "@/constants/languageLevels";
import { useTranslations } from "next-intl";

const LanguagesTab: React.FC = () => {
  const tResume = useTranslations("resume");
  const tCommon = useTranslations("common");

  return (
    <Form.List name="languages">
      {(fields, { add, remove }) => (
        <>
          {fields.map(({ key, name, ...restField }) => (
            <div
              key={key}
              className="border border-gray-300 p-4 rounded-md mb-4"
            >
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-base font-medium">{tResume("languages")} #{name + 1}</h3>
                {fields.length > 1 && (
                  <Button
                    danger
                    type="text"
                    onClick={() => remove(name)}
                    icon={<DeleteOutlined />}
                  />
                )}
              </div>

              <div className="flex flex-col md:flex-row gap-4">
                <Form.Item
                  {...restField}
                  label={tResume("form.language_name")}
                  name={[name, "language"]}
                  rules={[
                    { required: true, message: `${tCommon("placeholders.please_input")} ${tResume("form.language_name").toLowerCase()}` },
                  ]}
                  className="md:w-1/2"
                >
                  <Input placeholder={tResume("form.language_name_placeholder")} />
                </Form.Item>

                <Form.Item
                  {...restField}
                  label={tResume("form.language_level")}
                  name={[name, "level"]}
                  rules={[
                    { required: true, message: tResume("form.select_language_level") },
                  ]}
                  className="md:w-1/2"
                >
                  <Select placeholder={tResume("form.select_language_level")}>
                    {LANGUAGE_LEVELS.map((level) => (
                      <Select.Option key={level.value} value={level.value}>
                        {level.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </div>
            </div>
          ))}

          <Form.Item>
            <Button
              type="dashed"
              onClick={() => add()}
              block
              icon={<PlusOutlined />}
            >
              {tResume("form.add_language")}
            </Button>
          </Form.Item>
        </>
      )}
    </Form.List>
  );
};

export default LanguagesTab;
