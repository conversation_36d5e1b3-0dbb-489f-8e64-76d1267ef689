// components/features/resumes/ResumeTable.tsx
import BaseTable from "@/components/ui/tables/BaseTable";
import { formatDate } from "@/constants/resume";
import { fetchResumes } from "@/services/resumeService";
import { Resume } from "@/types/resume";
import { ApiListResponse } from "@/types/common";
import { CheckCircleTwoTone } from "@ant-design/icons";
import { TableColumnsType, Tag, Tooltip } from "antd";
import React from "react";

interface ResumeTableProps {
  onView: (resume: Resume) => void;
  onEdit?: (resume: Resume) => void;
  onDelete?: (resume: Resume) => Promise<void>;
  onCreate?: () => void;
  api?: (params?: Record<string, unknown>) => Promise<ApiListResponse<Resume>>;
  access?: any;
}

const ResumeTable: React.FC<ResumeTableProps> = ({
  onView,
  onEdit,
  onDelete,
  onCreate,
  api,
  access,
}) => {
  // Define table columns
  const columns: TableColumnsType<Resume> = [
    {
      title: "Resume Name",
      dataIndex: "name",
      key: "name",
      render: (text, record) => (
        <div className="flex items-center">
          {text}
          {record.isActive && (
            <Tooltip title="Active Resume" className="ml-2">
              <CheckCircleTwoTone twoToneColor="#52c41a" />
            </Tooltip>
          )}
        </div>
      ),
    },
    {
      title: "User ID",
      dataIndex: "userId",
      key: "userId",
      render: (userId) => userId || "-",
    },
    {
      title: "Description",
      dataIndex: "description",
      key: "description",
      render: (desc) => desc || "-",
    },
    {
      title: "Last Updated",
      dataIndex: "lastUpdated",
      key: "lastUpdated",
      render: formatDate,
    },
    {
      title: "Skills",
      dataIndex: "skills",
      key: "skills",
      render: (skills: string[]) => (
        <div className="flex flex-wrap gap-1">
          {skills.slice(0, 3).map((skill, index) => (
            <Tag key={index} color="blue">
              {skill}
            </Tag>
          ))}
          {skills.length > 3 && (
            <Tooltip title={skills.slice(3).join(", ")}>
              <Tag color="default">+{skills.length - 3} more</Tag>
            </Tooltip>
          )}
        </div>
      ),
    },
    {
      title: "Resume Type",
      key: "type",
      render: (_, record) => (
        <Tag color={record.partTimePreference ? "purple" : "blue"}>
          {record.partTimePreference ? "Part-time" : "Full-time"}
        </Tag>
      ),
    },
    {
      title: "Status",
      key: "status",
      render: (_, record) => (
        <Tag color={record.isActive ? "green" : "orange"}>
          {record.isActive ? "Active" : "Inactive"}
        </Tag>
      ),
    },
  ];

  // Define filter options
  const filterOptions = [
    {
      key: "status",
      label: "Status",
      type: "select" as const,
      options: [
        { value: "active", label: "Active" },
        { value: "inactive", label: "Inactive" },
      ],
    },
    {
      key: "type",
      label: "Resume Type",
      type: "select" as const,
      options: [
        { value: "fulltime", label: "Full-time" },
        { value: "parttime", label: "Part-time" },
      ],
    },
  ];

  const showActions = !!onEdit || !!onDelete;

  return (
    <BaseTable<Resume>
      api={api || fetchResumes}
      columns={columns}
      rowKey="id"
      createBtnText="New Resume"
      showSearch={true}
      searchPlaceholder="Search resumes..."
      onCreate={onCreate}
      onEdit={onEdit}
      onDelete={onDelete}
      onRowClick={onView}
      filters={filterOptions}
      showActions={showActions}
    />
  );
};

export default ResumeTable;
