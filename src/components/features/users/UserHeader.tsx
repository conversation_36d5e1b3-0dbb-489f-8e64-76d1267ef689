import { ExtendedUser } from "@/types/user";
import { utils } from "@/utils";
import { getRoleColor } from "@/constants/userRole";
import {
  EditOutlined,
  EnvironmentOutlined,
  LockOutlined,
  MailFilled,
  PhoneOutlined,
  SafetyCertificateTwoTone,
  UnlockOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { Button, Image, Tag } from "antd";

interface UserHeaderProps {
  user: ExtendedUser;
  onEdit: () => void;
  onLock: () => void;
}

const UserHeader = ({ user, onEdit, onLock }: UserHeaderProps) => {
  const getRoleTag = (role: string) => {
    return <Tag color={getRoleColor(role)}>{role}</Tag>;
  };

  return (
    <div className="flex flex-col md:flex-row mb-6">
      {/* Avatar */}
      <div className="flex flex-col items-center">
        {user.profilePicture ? (
          <Image
            width={200}
            alt="User Avatar"
            src={user.profilePicture}
            className="rounded-lg"
          />
        ) : (
          <Image
            width={200}
            alt="Default Avatar"
            src={"/user/default-avatar.svg"}
            className="rounded-lg"
          />
        )}
      </div>

      <div className="flex flex-col w-full ml-4">
        <div className="flex flex-row justify-between w-full md:w-auto">
          <h1 className="text-xl font-bold flex items-center">
            {user.name}
            {user.isEmailVerified && (
              <SafetyCertificateTwoTone
                title="Verified User"
                twoToneColor="#1677ff"
                className="ml-2"
              />
            )}
            <div className="flex items-center ml-2">
              <Tag color={user.active ? "green" : "red"}>
                {user.active ? "Active" : "Inactivated"}
              </Tag>
            </div>
          </h1>

          <div className="mt-4 md:mt-0">
            <Button
              className="mr-2"
              danger={user.active}
              type="primary"
              icon={user.active ? <UnlockOutlined /> : <LockOutlined />}
              onClick={onLock}
            >
              {user.active ? "Inactivate Account" : "Activate Account"}
            </Button>
            <Button type="primary" onClick={onEdit} icon={<EditOutlined />}>
              Edit User
            </Button>
          </div>
        </div>

        <div className="flex flex-wrap items-center mt-2">
          <p className="text-gray-500 font-medium mr-4 flex items-center">
            <UserOutlined className="mr-1" />
            {getRoleTag(user.role)}
          </p>

          <p className="text-gray-500 font-medium mr-4 flex items-center">
            <EnvironmentOutlined className="mr-1" />
            {utils.formatAddress(user.address)}
          </p>

          <p className="text-gray-500 font-medium mr-4 flex items-center">
            <MailFilled className="mr-1" />
            {user.email || "N/A"}
          </p>

          <p className="text-gray-500 font-medium mr-4 flex items-center">
            <PhoneOutlined className="mr-1" />
            {user.phoneNumber || "N/A"}
          </p>
        </div>
      </div>
    </div>
  );
};

export default UserHeader;
