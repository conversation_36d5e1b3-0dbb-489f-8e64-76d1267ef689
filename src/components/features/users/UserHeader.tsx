import { ExtendedUser } from "@/types/user";
import { utils } from "@/utils";
import { getRoleColor } from "@/constants/userRole";
import {
  EditOutlined,
  EnvironmentOutlined,
  LockOutlined,
  MailFilled,
  PhoneOutlined,
  SafetyCertificateTwoTone,
  UnlockOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { Button, Image, Tag } from "antd";
import { useTranslations } from "next-intl";

interface UserHeaderProps {
  user: ExtendedUser;
  onEdit: () => void;
  onLock: () => void;
}

const UserHeader = ({ user, onEdit, onLock }: UserHeaderProps) => {
  const t = useTranslations("user.detail");

  const getRoleTag = (role: string) => {
    return <Tag color={getRoleColor(role)}>{role}</Tag>;
  };

  return (
    <div className="flex flex-col md:flex-row mb-6">
      {/* Avatar */}
      <div className="flex flex-col items-center">
        {user.profilePicture ? (
          <Image
            width={200}
            alt={t("user_avatar")}
            src={user.profilePicture}
            className="rounded-lg"
          />
        ) : (
          <Image
            width={200}
            alt={t("default_avatar")}
            src={"/user/default-avatar.svg"}
            className="rounded-lg"
          />
        )}
      </div>

      <div className="flex flex-col w-full ml-4">
        <div className="flex flex-row justify-between w-full md:w-auto">
          <h1 className="text-xl font-bold flex items-center">
            {user.name}
            {user.isEmailVerified && (
              <SafetyCertificateTwoTone
                title={t("verified_user")}
                twoToneColor="#1677ff"
                className="ml-2"
              />
            )}
            <div className="flex items-center ml-2">
              <Tag color={user.active ? "green" : "red"}>
                {user.active ? t("active") : t("inactivated")}
              </Tag>
            </div>
          </h1>

          <div className="mt-4 md:mt-0">
            <Button
              className="mr-2"
              danger={user.active}
              type="primary"
              icon={user.active ? <UnlockOutlined /> : <LockOutlined />}
              onClick={onLock}
            >
              {user.active ? t("inactivate_account") : t("activate_account")}
            </Button>
            <Button type="primary" onClick={onEdit} icon={<EditOutlined />}>
              {t("edit_user")}
            </Button>
          </div>
        </div>

        <div className="flex flex-wrap items-center mt-2">
          <p className="text-gray-500 font-medium mr-4 flex items-center">
            <UserOutlined className="mr-1" />
            {getRoleTag(user.role)}
          </p>

          <p className="text-gray-500 font-medium mr-4 flex items-center">
            <EnvironmentOutlined className="mr-1" />
            {utils.formatAddress(user.address)}
          </p>

          <p className="text-gray-500 font-medium mr-4 flex items-center">
            <MailFilled className="mr-1" />
            {user.email || t("na")}
          </p>

          <p className="text-gray-500 font-medium mr-4 flex items-center">
            <PhoneOutlined className="mr-1" />
            {user.phoneNumber || t("na")}
          </p>
        </div>
      </div>
    </div>
  );
};

export default UserHeader;
