import React, { useState } from "react";
import { Tabs } from "antd";
import UserInfoTab from "./tabs/UserInfoTab";
import UserProfileTab from "./tabs/UserProfileTab";
import UserActivitiesTab from "./tabs/UserActivitiesTab";
import UserTransactionsTab from "./tabs/UserTransactionsTab";
import UserLogsTab from "./tabs/UserLogsTab";
import { ExtendedUser, TabKey } from "@/types/user";
import { UserRole } from "@/constants/userRole";

interface UserTabsProps {
  user: ExtendedUser;
}

const UserTabs: React.FC<UserTabsProps> = ({ user }) => {
  const [activeTab, setActiveTab] = useState<TabKey>("info");

  return (
    <Tabs
      activeKey={activeTab}
      onChange={(key) => setActiveTab(key as Tab<PERSON><PERSON>)}
      items={[
        {
          key: "info",
          label: "User Information",
          children: <UserInfoTab user={user} />,
        },
        ...(user.role !== UserRole.ADMIN
          ? [
              {
                key: "profile",
                label: "User Profile",
                children: <UserProfileTab user={user} />,
              },
              {
                key: "activities",
                label: "User Activities",
                children: <UserActivitiesTab user={user} />,
              },
              {
                key: "transactions",
                label: "Transactions",
                children: <UserTransactionsTab user={user} />,
              },
              {
                key: "logs",
                label: "Login History",
                children: <UserLogsTab user={user} />,
              },
            ]
          : []),
      ]}
    />
  );
};

export default UserTabs;
