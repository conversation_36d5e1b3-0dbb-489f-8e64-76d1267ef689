import React, { useState } from "react";
import { Tabs } from "antd";
import UserInfoTab from "./tabs/UserInfoTab";
import UserProfileTab from "./tabs/UserProfileTab";
import UserActivitiesTab from "./tabs/UserActivitiesTab";
import UserTransactionsTab from "./tabs/UserTransactionsTab";
import UserLogsTab from "./tabs/UserLogsTab";
import { ExtendedUser, TabKey } from "@/types/user";
import { UserRole } from "@/constants/userRole";
import { useTranslations } from "next-intl";

interface UserTabsProps {
  user: ExtendedUser;
}

const UserTabs: React.FC<UserTabsProps> = ({ user }) => {
  const [activeTab, setActiveTab] = useState<TabKey>("info");
  const t = useTranslations("user.tabs");

  return (
    <Tabs
      activeKey={activeTab}
      onChange={(key) => setActiveTab(key as Tab<PERSON><PERSON>)}
      items={[
        {
          key: "info",
          label: t("user_information"),
          children: <UserInfoTab user={user} />,
        },
        ...(user.role !== UserRole.ADMIN
          ? [
              {
                key: "profile",
                label: t("user_profile"),
                children: <UserProfileTab user={user} />,
              },
              {
                key: "activities",
                label: t("user_activities"),
                children: <UserActivitiesTab user={user} />,
              },
              {
                key: "transactions",
                label: t("transactions"),
                children: <UserTransactionsTab user={user} />,
              },
              {
                key: "logs",
                label: t("login_history"),
                children: <UserLogsTab user={user} />,
              },
            ]
          : []),
      ]}
    />
  );
};

export default UserTabs;
