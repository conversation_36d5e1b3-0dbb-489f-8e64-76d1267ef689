import BaseInput from "@/components/ui/inputs/BaseInput";
import BaseModal from "@/components/ui/modals/BaseModal";
import DebounceSelect from "@/components/ui/selects/DebounceSelect";
import { useNotification } from "@/contexts/NotiContext";
import RoleService from "@/services/roleService";
import UserService from "@/services/userService";
import { Col, Form, Input, message, Row } from "antd";
import { useTranslations } from "next-intl";
import { EyeInvisibleOutlined, EyeOutlined } from "@ant-design/icons";
import React, { useEffect, useState } from "react";
import BaseSelect from "@/components/ui/selects/BaseSelect";
import { USER_TYPES, GENDERS } from "@/constants/user";
import AddressFormField from "@/components/features/address/AddressFormField";
import BaseDatePicker from "@/components/ui/datepickers/BaseDatePicker";
import { Checkbox } from "antd";
import dayjs from "dayjs";

interface UserFormModalProps {
  isVisible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  currentUser?: any; // Using any to handle different user data structures
  lockedUserType?: string; // Lock user type field with this value
}

const UserFormModal: React.FC<UserFormModalProps> = ({
  isVisible,
  onCancel,
  onSuccess,
  currentUser,
  lockedUserType,
}) => {
  const t = useTranslations("user");
  const notification = useNotification();
  const tCommon = useTranslations("common");
  const [form] = Form.useForm();
  const isEdit = Boolean(!!currentUser);
  const modalTitle = isEdit ? t("edit_user") : t("add_user");
  const submitButtonText = isEdit ? tCommon("actions.save") : t("add_user");
  const [loading, setLoading] = useState(false);
  const [isAdminPortal, setIsAdminPortal] = useState(false);
  const [roleOptions, setRoleOptions] = useState<any[]>([]);

  // Calculate initial values for form
  const getInitialValues = () => {
    if (!currentUser) {
      return {
        isAdminPortal: false,
        name: "",
        phoneNumber: "",
        email: "",
        gender: undefined,
        role: lockedUserType || undefined, // Use lockedUserType as default
        dateOfBirth: null,
        address: {
          provinceCode: null,
          districtCode: null,
          wardCode: null,
          detailAddress: "",
        },
        roleIds: [],
      };
    }

    return {
      ...currentUser,
      role: lockedUserType || currentUser.role, // Override with lockedUserType if provided
      dateOfBirth: currentUser.dateOfBirth
        ? dayjs(currentUser.dateOfBirth)
        : null,
      address: currentUser.address
        ? {
            provinceCode: currentUser.address.provinceCode,
            districtCode: currentUser.address.districtCode,
            wardCode: currentUser.address.wardCode,
            detailAddress: currentUser.address.detailAddress,
          }
        : {
            provinceCode: null,
            districtCode: null,
            wardCode: null,
            detailAddress: "",
          },
      isAdminPortal: currentUser.isAdminPortal || false,
    };
  };

  // Fetch existing role names when editing
  const fetchExistingRoles = async (user: any) => {
    if (!user) return;

    // Try to get role IDs from different possible sources
    let roleIds: string[] = [];

    if (user.roleIds && Array.isArray(user.roleIds)) {
      roleIds = user.roleIds;
    } else if (user.roles && Array.isArray(user.roles)) {
      // If roles come as array of role objects
      roleIds = user.roles.map((role: any) => role.id || role);
    } else if (user.adminRoles) {
      // Alternative field name for admin roles
      roleIds = Array.isArray(user.adminRoles)
        ? user.adminRoles
        : [user.adminRoles];
    } else {
      // Fallback: fetch detailed user data to get role information
      try {
        const detailResponse = await UserService.getDetail(user.id);
        const detailedUser = detailResponse.data as any;
        if (
          detailedUser.roleIds ||
          detailedUser.roles ||
          detailedUser.adminRoles
        ) {
          return fetchExistingRoles(detailedUser);
        }
      } catch (error) {
        console.error("Failed to fetch detailed user data:", error);
      }
    }

    if (roleIds.length === 0) return;

    try {
      // Fetch all roles and filter by the existing roleIds
      const response = await RoleService.getList({ limit: 1000 });
      const existingRoles = response.data.filter((role: any) =>
        roleIds.some((id) => id.toString() === role.id.toString())
      );

      const formattedOptions = existingRoles.map((role: any) => ({
        label: role.name,
        value: role.id,
        key: role.id,
      }));

      setRoleOptions(formattedOptions);
    } catch (error) {
      console.error("Failed to fetch existing roles:", error);
    }
  };

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      // Convert dayjs date to string for API and ensure isAdminPortal is included
      const submitValues = {
        ...values,
        isAdminPortal: isAdminPortal, // Explicitly include isAdminPortal state
        dateOfBirth: values.dateOfBirth
          ? values.dateOfBirth.format("YYYY-MM-DD")
          : null,
      };

      console.log("Submit values:", submitValues); // Debug log to verify isAdminPortal

      if (isEdit) {
        await UserService.update(currentUser.id, submitValues);
      } else {
        await UserService.create(submitValues);
      }
      notification.notifySuccess(
        isEdit ? t("user_updated_successfully") : t("user_created_successfully")
      );
      onCancel();
      onSuccess();
    } catch (error) {
      message.error(
        t("failed_to_update_user", {
          error: error instanceof Error ? error.message : String(error),
        })
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isVisible) {
      // Reset form first to clear any previous data
      form.resetFields();

      const initialValues = getInitialValues();
      form.setFieldsValue(initialValues);
      setIsAdminPortal(!!initialValues.isAdminPortal);

      // Fetch existing role names when editing
      if (isEdit && currentUser) {
        fetchExistingRoles(currentUser);
      } else {
        setRoleOptions([]); // Clear options for new user
      }
      fetchRoleOptions("");
    } else {
      // Reset everything when modal is closed
      form.resetFields();
      setRoleOptions([]);
      setIsAdminPortal(false);
    }
  }, [currentUser, isVisible, form]);

  const handleOk = () => {
    form.submit();
  };

  // Enhanced fetchOptions function that merges existing options with search results
  const fetchRoleOptions = async (search: string) => {
    try {
      const response = await RoleService.getList({ search });
      const newOptions = response.data.map((option: any) => ({
        label: option.name,
        value: option.id,
        key: option.id,
      }));

      // Merge with existing role options to avoid losing pre-loaded data
      const mergedOptions = [...roleOptions];
      newOptions.forEach((newOption: any) => {
        if (
          !mergedOptions.some((existing) => existing.value === newOption.value)
        ) {
          mergedOptions.push(newOption);
        }
      });

      setRoleOptions(mergedOptions);
      return { data: mergedOptions };
    } catch (error) {
      console.error("Failed to fetch role options:", error);
      return { data: roleOptions };
    }
  };

  return (
    <BaseModal
      title={modalTitle}
      isVisible={isVisible}
      onSubmit={handleOk}
      onClose={onCancel}
      submitText={submitButtonText}
      confirmLoading={loading}
    >
      <Form
        form={form}
        onFinish={handleSubmit}
        layout="vertical"
        name="userForm"
        key={currentUser?.id || "new"} // Force re-render when editing different users
      >
        <Row justify="space-between" align="middle" gutter={16}>
          <Col span={12}>
            <BaseInput name="name" label={tCommon("fields.name")} required />
          </Col>
          <Col span={12}>
            <BaseInput
              name="phoneNumber"
              label={tCommon("fields.phone")}
              required
            />
          </Col>
          <Col span={12}>
            <BaseDatePicker
              name="dateOfBirth"
              label={tCommon("fields.dateOfBirth")}
              required
            />
          </Col>
          <Col span={12}>
            <BaseSelect
              name="gender"
              label={tCommon("fields.gender")}
              placeholder="Select gender"
              options={GENDERS}
              required
            />
          </Col>
          <Col span={12}>
            <BaseInput
              name="email"
              label={tCommon("fields.email")}
              required
              rules={[
                {
                  type: "email",
                  message: "The input is not valid E-mail!",
                },
              ]}
            />
          </Col>
          <Col span={12}>
            <Form.Item
              name="password"
              label="Password"
              rules={[
                {
                  required: !isEdit,
                  message: "Please input your password!",
                },
              ]}
            >
              <Input.Password
                placeholder={
                  isEdit ? t("password_cannot_be_changed") : "Password"
                }
                disabled={isEdit}
                iconRender={
                  isEdit
                    ? () => null
                    : (visible) =>
                        visible ? <EyeOutlined /> : <EyeInvisibleOutlined />
                }
              />
            </Form.Item>
          </Col>
          <Col span={24}>
            <div className="mb-4">
              <Checkbox
                checked={isAdminPortal}
                onChange={(e) => {
                  const checked = e.target.checked;
                  setIsAdminPortal(checked);
                  if (!checked) {
                    // Clear role selection when unchecked
                    form.setFieldValue("roleIds", []);
                  }
                }}
              >
                {t("admin_portal_access")}
              </Checkbox>
            </div>
          </Col>
          <Col span={12}>
            <DebounceSelect
              name="roleIds"
              mode="multiple"
              label={tCommon("fields.role")}
              required={isAdminPortal}
              disabled={!isAdminPortal}
              fetchOptions={fetchRoleOptions}
              options={roleOptions}
            />
          </Col>
          <Col span={12}>
            <BaseSelect
              name="role"
              label={t("user_type")}
              placeholder="Select user type"
              options={USER_TYPES}
              required
              disabled={!!lockedUserType} // Disable when lockedUserType is provided
            />
          </Col>
          <Col span={24}>
            <AddressFormField
              name="address"
              required
              // className="mt-4"
              // disabled
            />
          </Col>
        </Row>
      </Form>
    </BaseModal>
  );
};

export default UserFormModal;
