import BaseModal from "@/components/ui/modals/BaseModal";
import { ExtendedUser } from "@/types/user";

interface UserStatusModalProps {
  user: ExtendedUser;
  isVisible: boolean;
  onClose: () => void;
  onSubmit: () => void;
}

const UserStatusModal = ({
  user,
  isVisible,
  onClose,
  onSubmit,
}: UserStatusModalProps) => {
  return (
    <BaseModal
      title={user.active ? "Inactivate User Account" : "Activate User Account"}
      isVisible={isVisible}
      onSubmit={onSubmit}
      onClose={onClose}
      submitText={user.active ? "Inactivate" : "Activate"}
      okButtonProps={{ danger: user.active }}
    >
      <p>
        {user.active
          ? `Are you sure you want to inactivate ${user.name}'s account? They will not be able to login or use the platform until the account is activated.`
          : `Are you sure you want to activate ${user.name}'s account? This will restore their access to the platform.`}
      </p>
    </BaseModal>
  );
};

export default UserStatusModal;
