import BaseModal from "@/components/ui/modals/BaseModal";
import { ExtendedUser } from "@/types/user";
import { useTranslations } from "next-intl";

interface UserStatusModalProps {
  user: ExtendedUser;
  isVisible: boolean;
  onClose: () => void;
  onSubmit: () => void;
}

const UserStatusModal = ({
  user,
  isVisible,
  onClose,
  onSubmit,
}: UserStatusModalProps) => {
  const t = useTranslations("user.status_modal");

  return (
    <BaseModal
      title={
        user.active ? t("inactivate_user_account") : t("activate_user_account")
      }
      isVisible={isVisible}
      onSubmit={onSubmit}
      onClose={onClose}
      submitText={user.active ? t("inactivate") : t("activate")}
      okButtonProps={{ danger: user.active }}
    >
      <p>
        {user.active
          ? t("inactivate_confirmation", { name: user.name })
          : t("activate_confirmation", { name: user.name })}
      </p>
    </BaseModal>
  );
};

export default UserStatusModal;
