import { ExtendedUser } from "@/types/user";
import { utils } from "@/utils";
import { Tag } from "antd";
import BaseDescription, {
  DescriptionItem,
} from "@/components/ui/descriptions/BaseDescription";
import JobSeekerResumes from "@/components/features/users/job-seeker-resumes/JobSeekerResumes";
import { useTranslations } from "next-intl";

interface JobSeekerDetailProps {
  user: ExtendedUser;
}

const JobSeekerDetail = ({ user }: JobSeekerDetailProps) => {
  const t = useTranslations("user.professional");

  const professionalInfoItems: DescriptionItem[] = [
    {
      label: t("gender"),
      value: user.gender
        ? user.gender.charAt(0).toUpperCase() + user.gender.slice(1)
        : null,
    },
    {
      label: t("birth_date"),
      value: user.dateOfBirth ? utils.formatBirthDate(user.dateOfBirth) : null,
    },
    {
      label: t("skills"),
      value:
        user.skills && user.skills.length > 0 ? (
          <div>
            {user.skills.map((skill, index) => (
              <Tag key={index} color="blue">
                {skill}
              </Tag>
            ))}
          </div>
        ) : null,
    },
    {
      label: t("interests"),
      value:
        user.interests && user.interests.length > 0 ? (
          <div>
            {user.interests.map((interest, index) => (
              <Tag key={index} color="purple">
                {interest}
              </Tag>
            ))}
          </div>
        ) : null,
    },
    {
      label: t("id_verification"),
      value: (
        <Tag color={user.isPhoneVerified ? "green" : "orange"}>
          {user.isPhoneVerified ? t("verified") : t("not_verified")}
        </Tag>
      ),
    },
  ];

  return (
    <>
      <div className="mb-6">
        <BaseDescription
          title={t("professional_information")}
          items={professionalInfoItems}
          bordered
          column={1}
          layout="horizontal"
        />
      </div>
      <JobSeekerResumes userId={user.id} userInfo={user} />
    </>
  );
};

export default JobSeekerDetail;
