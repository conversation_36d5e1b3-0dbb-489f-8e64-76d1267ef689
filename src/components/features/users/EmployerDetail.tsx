import { ExtendedUser } from "@/types/user";
import { utils } from "@/utils";
import { Tag } from "antd";
import BaseDescription, {
  DescriptionItem,
} from "@/components/ui/descriptions/BaseDescription";
import { useTranslations } from "next-intl";

interface EmployerDetailProps {
  user: ExtendedUser;
}

const EmployerDetail = ({ user }: EmployerDetailProps) => {
  const t = useTranslations("user.professional");

  // Extract company data from user if available
  const companyName = user.name; // Assuming company name is stored in user name for employers
  const industry = t("na"); // Not in ExtendedUser, add if available
  const companySize = t("na"); // Not in ExtendedUser, add if available

  // Create description items array
  const companyInfoItems: DescriptionItem[] = [
    {
      label: t("company_name"),
      value: companyName,
    },
    {
      label: t("industry"),
      value: industry,
    },
    {
      label: t("company_size"),
      value: companySize,
    },
    {
      label: t("business_license"),
      value: <Tag color="orange">{t("not_verified")}</Tag>,
    },
    {
      label: t("company_address"),
      value: utils.formatAddress(user.address),
    },
    {
      label: t("registration_date"),
      value: new Date(user.createdAt).toLocaleDateString(),
    },
  ];

  return (
    <>
      <div className="mb-6">
        <BaseDescription
          title={t("company_information")}
          items={companyInfoItems}
          bordered
        />
      </div>
    </>
  );
};

export default EmployerDetail;
