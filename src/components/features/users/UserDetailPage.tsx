"use client";

import UserFormModal from "@/components/features/users/UserFormModal";
import UserHeader from "@/components/features/users/UserHeader";
import UserStatusModal from "@/components/features/users/UserStatusModal";
import UserTabs from "@/components/features/users/UserTabs";
import { UserRole } from "@/constants/userRole";
import UserService from "@/services/userService";
import { ExtendedUser, UserFormData } from "@/types/user";
import { ArrowLeftOutlined } from "@ant-design/icons";
import { Button, Card, Form, message, Spin } from "antd";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface UserDetailPageProps {
  userId: string;
  backUrl?: string;
  title?: string;
}

export default function UserDetailPage({
  userId,
  backUrl,
  title,
}: UserDetailPageProps) {
  const router = useRouter();
  const [user, setUser] = useState<ExtendedUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [isLockModalVisible, setIsLockModalVisible] = useState(false);
  const [form] = Form.useForm<UserFormData>();

  const fetchUser = async () => {
    setLoading(true);
    try {
      const res = await UserService.getDetail(userId);
      setUser(res.data as ExtendedUser);
    } catch (error) {
      message.error(
        `Failed to load user: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (userId) {
      fetchUser();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId]);

  const handleGoBack = () => {
    if (backUrl) {
      router.push(backUrl);
    } else {
      router.back();
    }
  };

  const handleEdit = () => {
    if (user) {
      form.setFieldsValue({
        name: user.name,
        phone: user.phoneNumber,
        email: user.email,
        role: user.role as UserRole,
      });
      setIsEditModalVisible(true);
    }
  };

  const handleToggleUserStatus = async () => {
    if (!user) return;

    try {
      const newStatus = !user.active;
      const response = await UserService.changeStatus(user.id, newStatus);

      // Update local state with the response data
      setUser(response.data as ExtendedUser);

      message.success(
        `User account ${newStatus ? "activated" : "inactivated"} successfully`
      );
      setIsLockModalVisible(false);
    } catch (error) {
      message.error(
        `Failed to ${user.active ? "inactivate" : "activate"} user: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spin size="large" />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <h1 className="text-xl font-bold mb-4">User not found</h1>
          <Button
            type="primary"
            onClick={handleGoBack}
            icon={<ArrowLeftOutlined />}
          >
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div>
      {title && (
        <div className="mb-4">
          <h1 className="text-2xl font-bold">{title}</h1>
        </div>
      )}

      <Card className="mb-4">
        <UserHeader
          user={user}
          onEdit={handleEdit}
          onLock={() => setIsLockModalVisible(true)}
        />

        <UserTabs user={user} />
      </Card>

      <UserFormModal
        isVisible={isEditModalVisible}
        onCancel={() => setIsEditModalVisible(false)}
        currentUser={user}
        onSuccess={fetchUser}
      />

      <UserStatusModal
        user={user}
        isVisible={isLockModalVisible}
        onClose={() => setIsLockModalVisible(false)}
        onSubmit={handleToggleUserStatus}
      />
    </div>
  );
}
