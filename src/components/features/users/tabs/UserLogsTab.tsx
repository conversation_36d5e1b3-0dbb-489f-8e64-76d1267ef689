import React from "react";
import { Card, Table } from "antd";
import { utils } from "@/utils";
import { ExtendedUser } from "@/types/user";
import { loginHistoryData } from "@/mocks/user";
import { useTranslations } from "next-intl";

interface UserLogsTabProps {
  user: ExtendedUser;
}

const UserLogsTab: React.FC<UserLogsTabProps> = ({ user }) => {
  console.log("🚀 ~ user:", user);
  const t = useTranslations("user.logs");

  // Log columns
  const logColumns = [
    {
      title: t("date_time"),
      dataIndex: "timestamp",
      key: "timestamp",
      render: (text: string) => utils.formatTableDate(text),
    },
    {
      title: t("activity"),
      dataIndex: "activity",
      key: "activity",
    },
    {
      title: t("ip_address"),
      dataIndex: "ipAddress",
      key: "ipAddress",
    },
    {
      title: t("device"),
      dataIndex: "device",
      key: "device",
    },
  ];

  return (
    <Card variant="outlined" title={t("login_history")}>
      <Table
        columns={logColumns}
        dataSource={loginHistoryData}
        pagination={{ pageSize: 10 }}
      />
    </Card>
  );
};

export default UserLogsTab;
