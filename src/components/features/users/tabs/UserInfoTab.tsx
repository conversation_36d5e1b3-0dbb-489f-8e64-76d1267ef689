// components/features/users/UserInfoTab.tsx
import React from "react";
import { Card, Space, Tag } from "antd";
import { utils } from "@/utils";
import { ExtendedUser } from "@/types/user";
import {
  getGenderIcon,
  getRoleTag,
  getStatusTag,
} from "@/app/(main)/account-management/users/helpers";
import BaseDescription from "@/components/ui/descriptions/BaseDescription";
import { DescriptionItem } from "@/components/ui/descriptions/BaseDescription";
import { useTranslations } from "next-intl";

interface UserInfoTabProps {
  user: ExtendedUser;
}

const UserInfoTab: React.FC<UserInfoTabProps> = ({ user }) => {
  const t = useTranslations("user.info");

  const personalInfoItems: DescriptionItem[] = [
    {
      label: t("full_name"),
      value: user.name,
    },
    {
      label: t("email"),
      value: (
        <Space>
          {user.email}
          {user.isEmailVerified && <Tag color="green">{t("verified")}</Tag>}
        </Space>
      ),
    },
    {
      label: t("phone"),
      value: (
        <Space>
          {user.phoneNumber}
          {user.isPhoneVerified && <Tag color="green">{t("verified")}</Tag>}
        </Space>
      ),
    },
    {
      label: t("address"),
      value: utils.formatAddress(user.address),
    },
    {
      label: t("date_of_birth"),
      value: user.dateOfBirth ? utils.formatBirthDate(user.dateOfBirth) : null,
    },
    {
      label: t("gender"),
      value: user.gender ? (
        <Space>
          {getGenderIcon(user.gender)}
          {user.gender.charAt(0).toUpperCase() + user.gender.slice(1)}
        </Space>
      ) : null,
    },
  ];

  const accountInfoItems: DescriptionItem[] = [
    {
      label: t("role"),
      value: getRoleTag(user.role),
    },
    {
      label: t("account_status"),
      value: getStatusTag(user.active ?? false),
    },
    {
      label: t("user_id"),
      value: user.ulid,
    },
    {
      label: t("created_at"),
      value: utils.formatTableDate(user.createdAt),
    },
    {
      label: t("last_login"),
      value: utils.formatTableDate(user.lastLogin),
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card title={t("personal_information")} variant="outlined">
        <BaseDescription items={personalInfoItems} bordered layout="vertical" />
      </Card>

      <Card title={t("account_information")} variant="outlined">
        <BaseDescription items={accountInfoItems} bordered layout="vertical" />
      </Card>
    </div>
  );
};

export default UserInfoTab;
