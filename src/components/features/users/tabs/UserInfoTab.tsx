// components/features/users/UserInfoTab.tsx
import React from "react";
import { Card, Space, Tag } from "antd";
import { utils } from "@/utils";
import { ExtendedUser } from "@/types/user";
import {
  getGenderIcon,
  getRoleTag,
  getStatusTag,
} from "@/app/(main)/account-management/users/helpers";
import BaseDescription from "@/components/ui/descriptions/BaseDescription";
import { DescriptionItem } from "@/components/ui/descriptions/BaseDescription";

interface UserInfoTabProps {
  user: ExtendedUser;
}

const UserInfoTab: React.FC<UserInfoTabProps> = ({ user }) => {
  const personalInfoItems: DescriptionItem[] = [
    {
      label: "Full Name",
      value: user.name,
    },
    {
      label: "Email",
      value: (
        <Space>
          {user.email}
          {user.isEmailVerified && <Tag color="green">Verified</Tag>}
        </Space>
      ),
    },
    {
      label: "Phone",
      value: (
        <Space>
          {user.phoneNumber}
          {user.isPhoneVerified && <Tag color="green">Verified</Tag>}
        </Space>
      ),
    },
    {
      label: "Address",
      value: utils.formatAddress(user.address),
    },
    {
      label: "Date of Birth",
      value: user.dateOfBirth ? utils.formatBirthDate(user.dateOfBirth) : null,
    },
    {
      label: "Gender",
      value: user.gender ? (
        <Space>
          {getGenderIcon(user.gender)}
          {user.gender.charAt(0).toUpperCase() + user.gender.slice(1)}
        </Space>
      ) : null,
    },
  ];

  const accountInfoItems: DescriptionItem[] = [
    {
      label: "Role",
      value: getRoleTag(user.role),
    },
    {
      label: "Account Status",
      value: getStatusTag(user.active ?? false),
    },
    {
      label: "User ID",
      value: user.ulid,
    },
    {
      label: "Created At",
      value: utils.formatTableDate(user.createdAt),
    },
    {
      label: "Last Login",
      value: utils.formatTableDate(user.lastLogin),
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card title="Personal Information" variant="outlined">
        <BaseDescription items={personalInfoItems} bordered layout="vertical" />
      </Card>

      <Card title="Account Information" variant="outlined">
        <BaseDescription items={accountInfoItems} bordered layout="vertical" />
      </Card>
    </div>
  );
};

export default UserInfoTab;
