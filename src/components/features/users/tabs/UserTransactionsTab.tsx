import React from "react";
import { Card, Table, Typography } from "antd";
import { utils } from "@/utils";
import { getTransactionTypeTag } from "@/app/(main)/account-management/users/helpers";
import { ExtendedUser } from "@/types/user";

const { Text } = Typography;

interface UserTransactionsTabProps {
  user: ExtendedUser;
}

const UserTransactionsTab: React.FC<UserTransactionsTabProps> = ({ user }) => {
  // Transactions columns
  const transactionColumns = [
    {
      title: "Transaction ID",
      dataIndex: "id",
      key: "id",
    },
    {
      title: "Type",
      dataIndex: "type",
      key: "type",
      render: (type: "deposit" | "usage") => getTransactionTypeTag(type),
    },
    {
      title: "Points",
      dataIndex: "points",
      key: "points",
      render: (points: number, record: { type: string }) => (
        <Text type={record.type === "deposit" ? "success" : "danger"}>
          {record.type === "deposit" ? "+" : "-"}
          {points}
        </Text>
      ),
    },
    {
      title: "Date",
      dataIndex: "date",
      key: "date",
      render: (text: string) => utils.formatTableDate(text),
    },
    {
      title: "Details",
      dataIndex: "details",
      key: "details",
    },
  ];

  return (
    <Card title="Points Transactions" variant="outlined">
      {user.transactions && user.transactions.length > 0 ? (
        <Table
          columns={transactionColumns}
          dataSource={user.transactions.map((tx) => ({ ...tx, key: tx.id }))}
          pagination={{ pageSize: 5 }}
          summary={(pageData) => {
            let totalPoints = 0;
            pageData.forEach(({ type, points }) => {
              if (type === "deposit") {
                totalPoints += points;
              } else {
                totalPoints -= points;
              }
            });

            return (
              <>
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0} colSpan={2}>
                    <Text strong>Current Balance</Text>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={1} colSpan={3}>
                    <Text strong>{totalPoints} points</Text>
                  </Table.Summary.Cell>
                </Table.Summary.Row>
              </>
            );
          }}
        />
      ) : (
        <Text type="secondary">No transactions available</Text>
      )}
    </Card>
  );
};

export default UserTransactionsTab;
