import React from "react";
import { Card, Table, Typography } from "antd";
import { utils } from "@/utils";
import { getTransactionTypeTag } from "@/app/(main)/account-management/users/helpers";
import { ExtendedUser } from "@/types/user";
import { useTranslations } from "next-intl";

const { Text } = Typography;

interface UserTransactionsTabProps {
  user: ExtendedUser;
}

const UserTransactionsTab: React.FC<UserTransactionsTabProps> = ({ user }) => {
  const t = useTranslations("user.transactions");

  // Transactions columns
  const transactionColumns = [
    {
      title: t("transaction_id"),
      dataIndex: "id",
      key: "id",
    },
    {
      title: t("type"),
      dataIndex: "type",
      key: "type",
      render: (type: "deposit" | "usage") => getTransactionTypeTag(type),
    },
    {
      title: t("points"),
      dataIndex: "points",
      key: "points",
      render: (points: number, record: { type: string }) => (
        <Text type={record.type === "deposit" ? "success" : "danger"}>
          {record.type === "deposit" ? "+" : "-"}
          {points}
        </Text>
      ),
    },
    {
      title: t("date"),
      dataIndex: "date",
      key: "date",
      render: (text: string) => utils.formatTableDate(text),
    },
    {
      title: t("details"),
      dataIndex: "details",
      key: "details",
    },
  ];

  return (
    <Card title={t("points_transactions")} variant="outlined">
      {user.transactions && user.transactions.length > 0 ? (
        <Table
          columns={transactionColumns}
          dataSource={user.transactions.map((tx) => ({ ...tx, key: tx.id }))}
          pagination={{ pageSize: 5 }}
          summary={(pageData) => {
            let totalPoints = 0;
            pageData.forEach(({ type, points }) => {
              if (type === "deposit") {
                totalPoints += points;
              } else {
                totalPoints -= points;
              }
            });

            return (
              <>
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0} colSpan={2}>
                    <Text strong>{t("current_balance")}</Text>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={1} colSpan={3}>
                    <Text strong>
                      {totalPoints} {t("points").toLowerCase()}
                    </Text>
                  </Table.Summary.Cell>
                </Table.Summary.Row>
              </>
            );
          }}
        />
      ) : (
        <Text type="secondary">{t("no_transactions_available")}</Text>
      )}
    </Card>
  );
};

export default UserTransactionsTab;
