import React from "react";
import { Card, List, Table, Typography, Button } from "antd";
import { StarOutlined } from "@ant-design/icons";
import { utils } from "@/utils";
import { ExtendedUser } from "@/types/user";
import {
  getApplicationStatusTag,
  getPostStatusTag,
} from "@/app/(main)/account-management/users/helpers";

const { Text } = Typography;

interface UserActivitiesTabProps {
  user: ExtendedUser;
}

const UserActivitiesTab: React.FC<UserActivitiesTabProps> = ({ user }) => {
  // Posts columns
  const postsColumns = [
    {
      title: "Title",
      dataIndex: "title",
      key: "title",
    },
    {
      title: "Date",
      dataIndex: "date",
      key: "date",
      render: (text: string) => utils.formatTableDate(text),
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      render: (status: string) => getPostStatusTag(status),
    },
    {
      title: "Actions",
      key: "actions",
      render: (_: unknown, record: { id: string }) => (
        <Button type="link" onClick={() => console.log("View post", record.id)}>
          View
        </Button>
      ),
    },
  ];

  // Applications columns
  const applicationsColumns = [
    {
      title: "Job Title",
      dataIndex: "jobTitle",
      key: "jobTitle",
    },
    {
      title: "Company",
      dataIndex: "company",
      key: "company",
    },
    {
      title: "Date",
      dataIndex: "date",
      key: "date",
      render: (text: string) => utils.formatTableDate(text),
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      render: (status: string) => getApplicationStatusTag(status),
    },
    {
      title: "Actions",
      key: "actions",
      render: (_: unknown, record: { id: string }) => (
        <Button
          type="link"
          onClick={() => console.log("View application", record.id)}
        >
          View
        </Button>
      ),
    },
  ];

  return (
    <div className="grid grid-cols-1 gap-6">
      <Card title="Job Posts" variant="outlined">
        {user.posts && user.posts.length > 0 ? (
          <Table
            columns={postsColumns}
            dataSource={user.posts.map((post) => ({ ...post, key: post.id }))}
            pagination={false}
          />
        ) : (
          <Text type="secondary">No job posts available</Text>
        )}
      </Card>

      <Card title="Job Applications" variant="outlined">
        {user.applications && user.applications.length > 0 ? (
          <Table
            columns={applicationsColumns}
            dataSource={user.applications.map((app) => ({
              ...app,
              key: app.id,
            }))}
            pagination={false}
          />
        ) : (
          <Text type="secondary">No job applications available</Text>
        )}
      </Card>

      <Card title="Reviews Received" variant="outlined">
        {user.reviews && user.reviews.length > 0 ? (
          <List
            itemLayout="horizontal"
            dataSource={user.reviews}
            renderItem={(review) => (
              <List.Item>
                <List.Item.Meta
                  avatar={
                    <StarOutlined
                      style={{ fontSize: "24px", color: "#faad14" }}
                    />
                  }
                  title={
                    <div className="flex items-center justify-between">
                      <span>From: {review.from}</span>
                      <span>Rating: {review.rating}/5</span>
                    </div>
                  }
                  description={
                    <div>
                      <div>{review.comment}</div>
                      <div className="text-right text-gray-400">
                        {utils.formatTableDate(review.date)}
                      </div>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        ) : (
          <Text type="secondary">No reviews received</Text>
        )}
      </Card>
    </div>
  );
};

export default UserActivitiesTab;
