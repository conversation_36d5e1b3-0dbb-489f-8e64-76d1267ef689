import React from "react";
import { Card, List, Table, Typography, Button } from "antd";
import { StarOutlined } from "@ant-design/icons";
import { utils } from "@/utils";
import { ExtendedUser } from "@/types/user";
import {
  getApplicationStatusTag,
  getPostStatusTag,
} from "@/app/(main)/account-management/users/helpers";
import { useTranslations } from "next-intl";

const { Text } = Typography;

interface UserActivitiesTabProps {
  user: ExtendedUser;
}

const UserActivitiesTab: React.FC<UserActivitiesTabProps> = ({ user }) => {
  const t = useTranslations("user.activities");

  // Posts columns
  const postsColumns = [
    {
      title: t("title"),
      dataIndex: "title",
      key: "title",
    },
    {
      title: t("date"),
      dataIndex: "date",
      key: "date",
      render: (text: string) => utils.formatTableDate(text),
    },
    {
      title: t("status"),
      dataIndex: "status",
      key: "status",
      render: (status: string) => getPostStatusTag(status),
    },
    {
      title: t("actions"),
      key: "actions",
      render: (_: unknown, record: { id: string }) => (
        <Button type="link" onClick={() => console.log("View post", record.id)}>
          {t("view")}
        </Button>
      ),
    },
  ];

  // Applications columns
  const applicationsColumns = [
    {
      title: t("job_title"),
      dataIndex: "jobTitle",
      key: "jobTitle",
    },
    {
      title: t("company"),
      dataIndex: "company",
      key: "company",
    },
    {
      title: t("date"),
      dataIndex: "date",
      key: "date",
      render: (text: string) => utils.formatTableDate(text),
    },
    {
      title: t("status"),
      dataIndex: "status",
      key: "status",
      render: (status: string) => getApplicationStatusTag(status),
    },
    {
      title: t("actions"),
      key: "actions",
      render: (_: unknown, record: { id: string }) => (
        <Button
          type="link"
          onClick={() => console.log("View application", record.id)}
        >
          {t("view")}
        </Button>
      ),
    },
  ];

  return (
    <div className="grid grid-cols-1 gap-6">
      <Card title={t("job_posts")} variant="outlined">
        {user.posts && user.posts.length > 0 ? (
          <Table
            columns={postsColumns}
            dataSource={user.posts.map((post) => ({ ...post, key: post.id }))}
            pagination={false}
          />
        ) : (
          <Text type="secondary">{t("no_job_posts_available")}</Text>
        )}
      </Card>

      <Card title={t("job_applications")} variant="outlined">
        {user.applications && user.applications.length > 0 ? (
          <Table
            columns={applicationsColumns}
            dataSource={user.applications.map((app) => ({
              ...app,
              key: app.id,
            }))}
            pagination={false}
          />
        ) : (
          <Text type="secondary">{t("no_job_applications_available")}</Text>
        )}
      </Card>

      <Card title={t("reviews_received")} variant="outlined">
        {user.reviews && user.reviews.length > 0 ? (
          <List
            itemLayout="horizontal"
            dataSource={user.reviews}
            renderItem={(review) => (
              <List.Item>
                <List.Item.Meta
                  avatar={
                    <StarOutlined
                      style={{ fontSize: "24px", color: "#faad14" }}
                    />
                  }
                  title={
                    <div className="flex items-center justify-between">
                      <span>
                        {t("from")}: {review.from}
                      </span>
                      <span>
                        {t("rating")}: {review.rating}/5
                      </span>
                    </div>
                  }
                  description={
                    <div>
                      <div>{review.comment}</div>
                      <div className="text-right text-gray-400">
                        {utils.formatTableDate(review.date)}
                      </div>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        ) : (
          <Text type="secondary">{t("no_reviews_received")}</Text>
        )}
      </Card>
    </div>
  );
};

export default UserActivitiesTab;
