"use client";

import React, { createContext, useContext, ReactNode } from "react";
import { App, NotificationArgsProps } from "antd";

interface NotificationContextType {
  notifySuccess: (message: string, description?: string) => void;
  notifyError: (message: string, description?: string) => void;
  notifyInfo: (message: string, description?: string) => void;
  notifyWarning: (message: string, description?: string) => void;
}

export const NotificationContext =
  createContext<NotificationContextType | null>(null);

interface NotificationProviderProps {
  children: ReactNode;
  defaultDuration?: number;
  defaultPlacement?: NotificationArgsProps["placement"];
}

const NotificationProviderInner: React.FC<NotificationProviderProps> = ({
  children,
  defaultDuration = 4,
  defaultPlacement = "topRight",
}) => {
  const { notification } = App.useApp();

  const contextValue: NotificationContextType = {
    notifySuccess: (message: string, description?: string) => {
      notification.success({
        message,
        description,
        placement: defaultPlacement,
        duration: defaultDuration,
      });
    },
    notifyError: (message: string, description?: string) => {
      notification.error({
        message,
        description,
        placement: defaultPlacement,
        duration: defaultDuration,
      });
    },
    notifyInfo: (message: string, description?: string) => {
      notification.info({
        message,
        description,
        placement: defaultPlacement,
        duration: defaultDuration,
      });
    },
    notifyWarning: (message: string, description?: string) => {
      notification.warning({
        message,
        description,
        placement: defaultPlacement,
        duration: defaultDuration,
      });
    },
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  );
};

export const NotificationProvider: React.FC<NotificationProviderProps> = (
  props
) => {
  return (
    <App>
      <NotificationProviderInner {...props} />
    </App>
  );
};

export const useNotification = (): NotificationContextType => {
  const context = useContext(NotificationContext);

  if (!context) {
    throw new Error(
      "useNotification must be used within a NotificationProvider"
    );
  }

  return context;
};
